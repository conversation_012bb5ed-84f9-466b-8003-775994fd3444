# Background Worker System Plan

## Overview
Build a Node.js background worker system similar to Hangfire for .NET, providing:
- Background job processing
- System statistics aggregation
- Health monitoring
- Scheduled tasks

## Architecture

### 1. Database Schema

```sql
-- Jobs Queue Table
CREATE TABLE background_jobs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  job_type VARCHAR(100) NOT NULL,
  job_data JSON,
  status ENUM('pending', 'processing', 'completed', 'failed', 'retrying') DEFAULT 'pending',
  priority INT DEFAULT 0,
  attempts INT DEFAULT 0,
  max_attempts INT DEFAULT 3,
  scheduled_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  started_at DATETIME NULL,
  completed_at DATETIME NULL,
  error_message TEXT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_status_priority (status, priority),
  INDEX idx_scheduled_at (scheduled_at),
  INDEX idx_job_type (job_type)
);

-- System Stats Cache Table
CREATE TABLE system_stats (
  id INT PRIMARY KEY AUTO_INCREMENT,
  stat_type VARCHAR(50) NOT NULL,
  stat_data JSON NOT NULL,
  calculated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  expires_at DATETIME NOT NULL,
  UNIQUE KEY unique_stat_type (stat_type),
  INDEX idx_expires_at (expires_at)
);

-- Health Check Results Table
CREATE TABLE health_checks (
  id INT PRIMARY KEY AUTO_INCREMENT,
  check_name VARCHAR(100) NOT NULL,
  status ENUM('healthy', 'degraded', 'unhealthy') NOT NULL,
  response_time_ms INT,
  details JSON,
  checked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_check_name_time (check_name, checked_at),
  INDEX idx_status (status)
);
```

### 2. Core Components

#### A. Job Queue Manager (`backend/shared/workers/JobQueue.js`)
- Enqueue jobs with priority and scheduling
- Dequeue jobs for processing
- Handle retries and failures
- Job status tracking

#### B. Worker Process (`backend/shared/workers/Worker.js`)
- Poll for pending jobs
- Execute job handlers
- Handle errors and retries
- Graceful shutdown

#### C. Job Handlers (`backend/shared/workers/handlers/`)
- `StatsAggregatorHandler.js` - Calculate system statistics
- `HealthCheckHandler.js` - Perform health checks
- `CleanupHandler.js` - Database cleanup tasks
- `EmailDigestHandler.js` - Send periodic email reports

#### D. Scheduler (`backend/shared/workers/Scheduler.js`)
- Cron-like job scheduling
- Recurring job management
- Schedule validation

#### E. Stats Aggregator (`backend/shared/workers/StatsAggregator.js`)
- Total users, logs, connections
- Performance metrics
- Usage analytics
- Cache management

#### F. Health Monitor (`backend/shared/workers/HealthMonitor.js`)
- Database connectivity
- API endpoint health
- Memory/CPU usage
- External service status

### 3. Job Types

#### Stats Jobs
- `calculate-daily-stats` - Daily user/log/connection counts
- `calculate-weekly-stats` - Weekly aggregations
- `calculate-monthly-stats` - Monthly reports
- `cleanup-old-stats` - Remove expired cached stats

#### Health Jobs
- `check-database-health` - Database connectivity and performance
- `check-api-health` - API endpoint response times
- `check-memory-usage` - Memory and CPU monitoring
- `check-external-services` - Third-party service status

#### Maintenance Jobs
- `cleanup-old-logs` - Remove old log entries
- `cleanup-expired-sessions` - Clean expired user sessions
- `optimize-database` - Database maintenance tasks
- `backup-critical-data` - Data backup operations

### 4. Implementation Steps

#### Phase 1: Core Infrastructure
1. Create database migrations for job tables
2. Implement JobQueue class with basic CRUD operations
3. Create Worker process with job polling
4. Add basic job handlers framework

#### Phase 2: Stats System
1. Implement StatsAggregator with caching
2. Create stats calculation jobs
3. Add stats API endpoints
4. Build stats dashboard components

#### Phase 3: Health Monitoring
1. Implement HealthMonitor with various checks
2. Create health check jobs
3. Add health status API endpoints
4. Build health dashboard

#### Phase 4: Advanced Features
1. Add job scheduling with cron expressions
2. Implement job priorities and dependencies
3. Add worker scaling and load balancing
4. Create monitoring and alerting

### 5. API Endpoints

```javascript
// Job Management
POST   /api/v1/jobs                    // Enqueue job
GET    /api/v1/jobs                    // List jobs
GET    /api/v1/jobs/:id                // Get job details
DELETE /api/v1/jobs/:id                // Cancel job

// Stats
GET    /api/v1/stats/summary           // Overall system stats
GET    /api/v1/stats/users             // User statistics
GET    /api/v1/stats/logs              // Log statistics
GET    /api/v1/stats/connections       // Connection statistics

// Health
GET    /api/v1/health                  // Overall health status
GET    /api/v1/health/database         // Database health
GET    /api/v1/health/api              // API health
GET    /api/v1/health/memory           // Memory/CPU health

// Worker Management
GET    /api/v1/workers/status          // Worker process status
POST   /api/v1/workers/restart         // Restart workers
GET    /api/v1/workers/jobs            // Active jobs
```

### 6. Configuration

```javascript
// worker.config.js
module.exports = {
  // Worker settings
  maxConcurrentJobs: 5,
  pollInterval: 5000, // 5 seconds
  maxRetries: 3,
  retryDelay: 30000, // 30 seconds
  
  // Stats settings
  statsCacheTTL: 300, // 5 minutes
  statsCalculationInterval: '*/15 * * * *', // Every 15 minutes
  
  // Health check settings
  healthCheckInterval: '*/5 * * * *', // Every 5 minutes
  healthCheckTimeout: 10000, // 10 seconds
  
  // Cleanup settings
  logRetentionDays: 30,
  sessionCleanupInterval: '0 */6 * * *', // Every 6 hours
}
```

### 7. Monitoring Dashboard

Create admin dashboard sections for:
- **Job Queue Status** - Pending, processing, failed jobs
- **System Statistics** - Real-time and historical stats
- **Health Overview** - All health checks with status indicators
- **Worker Management** - Start/stop workers, view logs
- **Performance Metrics** - Response times, throughput, errors

### 8. Benefits

- **Scalability** - Separate worker processes from main server
- **Reliability** - Job retries and error handling
- **Performance** - Cached statistics reduce database load
- **Monitoring** - Comprehensive health and performance tracking
- **Maintenance** - Automated cleanup and optimization tasks

#!/usr/bin/env node

/**
 * Force Sync Models - Force Sequelize to sync with database
 * This will force Sequelize to update its understanding of the database schema
 */

require('dotenv').config();
const { sequelize } = require('./shared/config/database');

const forceSyncModels = async () => {
  try {
    console.log('🔄 Force syncing Sequelize models with database...');
    
    // Import all models to ensure they're loaded
    const UserSession = require('./shared/models/UserSession');
    const User = require('./shared/models/User');
    
    console.log('📋 Models loaded successfully');
    
    // Force sync the UserSession model specifically
    console.log('🔄 Force syncing UserSession model...');
    await UserSession.sync({ force: false, alter: true });
    console.log('✅ UserSession model synced');
    
    // Test the lock_reason field specifically
    console.log('🧪 Testing lock_reason field...');
    
    // Find an active session to test with
    const testSession = await UserSession.findOne({
      where: { is_active: true }
    });
    
    if (testSession) {
      console.log(`📝 Testing with session ID: ${testSession.id}`);
      
      // Test each enum value
      const testValues = ['inactivity', 'admin_action', 'security_breach', 'manual'];
      
      for (const value of testValues) {
        try {
          await testSession.update({ lock_reason: value });
          console.log(`✅ Successfully set lock_reason to '${value}'`);
        } catch (error) {
          console.error(`❌ Failed to set lock_reason to '${value}':`, error.message);
        }
      }
      
      // Reset to NULL
      try {
        await testSession.update({ lock_reason: null });
        console.log('✅ Reset lock_reason to NULL');
      } catch (error) {
        console.error('❌ Failed to reset lock_reason:', error.message);
      }
    } else {
      console.log('⚠️  No active sessions found to test with');
    }
    
    // Test the lockSession method
    if (testSession) {
      console.log('🧪 Testing lockSession method...');
      try {
        await testSession.lockSession('manual');
        console.log('✅ lockSession method works correctly');
        
        // Unlock it
        await testSession.unlockSession();
        console.log('✅ unlockSession method works correctly');
      } catch (error) {
        console.error('❌ lockSession method failed:', error.message);
        console.error('Full error:', error);
      }
    }
    
    console.log('🎉 Model sync completed successfully!');
    
  } catch (error) {
    console.error('💥 Model sync failed:', error.message);
    console.error('Full error:', error);
    throw error;
  }
};

// Run if called directly
if (require.main === module) {
  forceSyncModels()
    .then(() => {
      console.log('\n🎉 Force sync completed successfully!');
      console.log('🔄 Please restart the server to ensure all changes take effect.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Force sync failed:', error.message);
      process.exit(1);
    });
}

module.exports = { forceSyncModels };

#!/usr/bin/env node

/**
 * Fix Duplicate Index Error Script
 * 
 * This script helps resolve the MySQL error:
 * "Duplicate key name 'push_subscriptions_user_id_idx'"
 * 
 * Usage:
 *   node scripts/fix-duplicate-index.js
 */

console.log('🔧 Fix Duplicate Index Error');
console.log('============================');

console.log('\n❌ Problem:');
console.log('MySQL is trying to create an index that already exists.');
console.log('Error: "Duplicate key name \'push_subscriptions_user_id_idx\'"');

console.log('\n🔍 Common Causes:');
console.log('1. Migration was run partially and failed');
console.log('2. Table was created manually with indexes');
console.log('3. Previous migration attempt left partial state');
console.log('4. Index names conflict with existing indexes');

console.log('\n✅ Solutions Available:');

console.log('\n📋 Option 1: Use Clean Migration (Recommended)');
console.log('This uses the new migration file that handles duplicates gracefully:');
console.log('');
console.log('Steps:');
console.log('1. Check current migration status:');
console.log('   npx sequelize-cli db:migrate:status');
console.log('');
console.log('2. If push_subscriptions migration is pending, undo any partial migrations:');
console.log('   npx sequelize-cli db:migrate:undo');
console.log('');
console.log('3. Use the clean migration file:');
console.log('   mv migrations/20241210000000-create-push-subscriptions.js \\');
console.log('      migrations/20241210000000-create-push-subscriptions.js.backup');
console.log('   mv migrations/20241210000002-create-push-subscriptions-clean.js \\');
console.log('      migrations/20241210000000-create-push-subscriptions.js');
console.log('');
console.log('4. Run the migration:');
console.log('   npx sequelize-cli db:migrate');

console.log('\n📋 Option 2: Manual Cleanup');
console.log('If you want to clean up manually:');
console.log('');
console.log('1. Connect to MySQL and check existing indexes:');
console.log('   SHOW INDEX FROM push_subscriptions;');
console.log('');
console.log('2. Drop conflicting indexes:');
console.log('   DROP INDEX push_subscriptions_user_id_idx ON push_subscriptions;');
console.log('   DROP INDEX push_subscriptions_endpoint_idx ON push_subscriptions;');
console.log('   -- (repeat for other conflicting indexes)');
console.log('');
console.log('3. Run migration again:');
console.log('   npx sequelize-cli db:migrate');

console.log('\n📋 Option 3: Fresh Start');
console.log('If you want to start completely fresh:');
console.log('');
console.log('1. Drop the table completely:');
console.log('   DROP TABLE IF EXISTS push_subscriptions;');
console.log('');
console.log('2. Reset migration state:');
console.log('   DELETE FROM SequelizeMeta WHERE name LIKE \'%push-subscriptions%\';');
console.log('');
console.log('3. Run migration:');
console.log('   npx sequelize-cli db:migrate');

console.log('\n🔍 Debugging Commands:');
console.log('');
console.log('Check migration status:');
console.log('  npx sequelize-cli db:migrate:status');
console.log('');
console.log('Check existing tables:');
console.log('  mysql -u username -p -e "SHOW TABLES;" database_name');
console.log('');
console.log('Check existing indexes:');
console.log('  mysql -u username -p -e "SHOW INDEX FROM push_subscriptions;" database_name');
console.log('');
console.log('Check table structure:');
console.log('  mysql -u username -p -e "DESCRIBE push_subscriptions;" database_name');

console.log('\n⚠️  Important Notes:');
console.log('');
console.log('• The clean migration file handles duplicate indexes gracefully');
console.log('• It checks if table/indexes exist before creating them');
console.log('• Safe to run multiple times without errors');
console.log('• Preserves existing data if table already exists');
console.log('• Uses shorter, cleaner index names to avoid conflicts');

console.log('\n🎯 Recommended Action:');
console.log('Use Option 1 (Clean Migration) - it\'s the safest and most reliable approach.');

console.log('\n📊 Index Name Changes:');
console.log('Old names (causing conflicts):');
console.log('  push_subscriptions_user_id_idx');
console.log('  push_subscriptions_endpoint_idx');
console.log('  push_subscriptions_is_active_idx');
console.log('');
console.log('New names (conflict-free):');
console.log('  idx_push_subscriptions_user_id');
console.log('  idx_push_subscriptions_endpoint_hash');
console.log('  idx_push_subscriptions_is_active');

console.log('\n✅ After fixing:');
console.log('Your push notification system will be ready with:');
console.log('• Proper MySQL compatibility');
console.log('• No duplicate index errors');
console.log('• Optimized performance');
console.log('• Clean, maintainable schema');

console.log('\n🚀 Ready to proceed!');
console.log('Choose your preferred option above and follow the steps.');

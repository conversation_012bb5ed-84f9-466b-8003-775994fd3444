#!/usr/bin/env node

/**
 * Migration Script: Switch Push Notifications from In-Memory to Database Storage
 * 
 * This script helps you migrate from the current in-memory push notification
 * storage to persistent database storage.
 * 
 * Usage:
 *   node scripts/migrate-push-to-db.js
 */

const path = require('path');
const fs = require('fs');

console.log('🔄 Push Notification Storage Migration Tool');
console.log('==========================================');

async function migratePushNotifications() {
  try {
    console.log('\n📋 Migration Steps:');
    console.log('1. ✅ Database model created: models/PushSubscription.js');
    console.log('2. ✅ Migration file created: migrations/20241210000000-create-push-subscriptions.js');
    console.log('3. ✅ Enhanced service created: services/pushNotificationServiceDB.js');
    
    console.log('\n🔧 Manual Steps Required:');
    console.log('');
    
    console.log('Step 1: Run the database migration');
    console.log('   cd backend');
    console.log('   npx sequelize-cli db:migrate');
    console.log('');
    
    console.log('Step 2: Update your models/index.js to include PushSubscription');
    console.log('   Add: const PushSubscription = require("./PushSubscription")(sequelize);');
    console.log('   Add: models.PushSubscription = PushSubscription;');
    console.log('');
    
    console.log('Step 3: Replace the push notification service');
    console.log('   In routes/v1/push.js:');
    console.log('   Replace: const PushNotificationService = require("../../services/pushNotificationService");');
    console.log('   With:    const PushNotificationService = require("../../services/pushNotificationServiceDB");');
    console.log('');
    
    console.log('Step 4: Initialize the service with models');
    console.log('   In your app.js or server startup:');
    console.log('   Add: PushNotificationService.initialize(models);');
    console.log('');
    
    console.log('Step 5: Update all service methods to be async');
    console.log('   In routes/v1/push.js, add "await" to all service calls:');
    console.log('   - await PushNotificationService.addSubscription(...)');
    console.log('   - await PushNotificationService.sendToUser(...)');
    console.log('   - await PushNotificationService.getStats()');
    console.log('   - etc.');
    console.log('');
    
    console.log('📊 Benefits of Database Storage:');
    console.log('✅ Persistent storage - survives server restarts');
    console.log('✅ Scalable - works with multiple server instances');
    console.log('✅ Reliable - no data loss on crashes');
    console.log('✅ Analytics - track subscription patterns');
    console.log('✅ Cleanup - automatic removal of inactive subscriptions');
    console.log('✅ Performance - indexed queries for fast lookups');
    console.log('');
    
    console.log('⚠️  Important Notes:');
    console.log('- Current in-memory subscriptions will be lost during migration');
    console.log('- Users will need to re-subscribe after the migration');
    console.log('- Test thoroughly in development before deploying to production');
    console.log('- Consider notifying users about the temporary interruption');
    console.log('');
    
    console.log('🔧 Optional: Set up automatic cleanup');
    console.log('Add to your cron jobs or scheduled tasks:');
    console.log('   // Clean up inactive subscriptions older than 30 days');
    console.log('   await PushNotificationService.cleanupInactiveSubscriptions(30);');
    console.log('');
    
    console.log('✅ Migration preparation complete!');
    console.log('Follow the manual steps above to complete the migration.');
    
  } catch (error) {
    console.error('❌ Migration preparation failed:', error);
    process.exit(1);
  }
}

// Check if we're in the right directory
const currentDir = process.cwd();
const isBackendDir = fs.existsSync(path.join(currentDir, 'package.json')) && 
                    fs.existsSync(path.join(currentDir, 'shared'));

if (!isBackendDir) {
  console.error('❌ Please run this script from the backend directory');
  console.error('   cd backend && node scripts/migrate-push-to-db.js');
  process.exit(1);
}

migratePushNotifications();

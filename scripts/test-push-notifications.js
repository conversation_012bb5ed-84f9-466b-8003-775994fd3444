#!/usr/bin/env node

/**
 * Test Push Notifications Script
 * 
 * This script demonstrates how to send push notifications to specific users
 * and provides examples of different notification types.
 */

const { initializeDatabase } = require('../shared/models');
const PushNotificationService = require('../shared/services/pushNotificationServiceDB');

async function testPushNotifications() {
  console.log('🔔 Testing Push Notifications');
  console.log('==============================');

  try {
    // Initialize database
    console.log('🗄️  Initializing database...');
    const dbInitialized = await initializeDatabase();
    
    if (!dbInitialized) {
      console.error('❌ Database initialization failed');
      process.exit(1);
    }

    // Initialize push service
    const models = require('../shared/models');
    PushNotificationService.initialize(models);
    console.log('✅ Push service initialized');

    // Get statistics
    console.log('\n📊 Current Statistics:');
    const stats = await PushNotificationService.getStats();
    console.log(`  Total Subscriptions: ${stats.totalSubscriptions}`);
    console.log(`  Active Subscriptions: ${stats.activeSubscriptions}`);
    console.log(`  Users by Role:`, stats.usersByRole);
    console.log(`  Device Types:`, stats.deviceTypes);

    if (stats.totalSubscriptions === 0) {
      console.log('\n⚠️  No push subscriptions found!');
      console.log('   To test push notifications:');
      console.log('   1. Open the frontend application');
      console.log('   2. Install the PWA');
      console.log('   3. Grant notification permissions');
      console.log('   4. Run this script again');
      return;
    }

    // Test 1: Send welcome notification to first user
    console.log('\n🧪 Test 1: Welcome Notification');
    const { User } = models;
    const firstUser = await User.findOne({ where: { is_active: true } });
    
    if (firstUser) {
      const welcomePayload = {
        title: '🎉 Welcome to HLenergy',
        body: `Hello ${firstUser.name}! This is a test notification.`,
        icon: '/hl-energy-logo-192w.png',
        badge: '/hl-energy-logo-96w.png',
        tag: 'test-welcome',
        data: {
          url: '/dashboard',
          type: 'test',
          timestamp: Date.now()
        },
        requireInteraction: false
      };

      console.log(`   Sending to: ${firstUser.name} (${firstUser.email})`);
      const result1 = await PushNotificationService.sendToUser(firstUser.id, welcomePayload);
      console.log(`   Result: ✅ Sent: ${result1.sent}, ❌ Failed: ${result1.failed}`);
    }

    // Test 2: Send notification to all admin users
    console.log('\n🧪 Test 2: Admin Notification');
    const adminPayload = {
      title: '🔧 Admin Test Notification',
      body: 'This is a test notification for admin users only.',
      icon: '/hl-energy-logo-192w.png',
      badge: '/hl-energy-logo-96w.png',
      tag: 'test-admin',
      data: {
        url: '/dashboard',
        type: 'admin-test'
      },
      requireInteraction: true
    };

    const result2 = await PushNotificationService.sendToAdmins(adminPayload);
    console.log(`   Result: ✅ Sent: ${result2.sent}, ❌ Failed: ${result2.failed}`);

    // Test 3: Send system announcement
    console.log('\n🧪 Test 3: System Announcement');
    const announcementPayload = {
      title: '📢 System Test Announcement',
      body: 'This is a test system announcement for all users.',
      icon: '/hl-energy-logo-192w.png',
      badge: '/hl-energy-logo-96w.png',
      tag: 'test-announcement',
      data: {
        url: '/dashboard',
        type: 'system-test'
      },
      requireInteraction: false
    };

    const result3 = await PushNotificationService.sendSystemAnnouncement(announcementPayload);
    console.log(`   Result: ✅ Sent: ${result3.sent}, ❌ Failed: ${result3.failed}`);

    // Test 4: Send new lead notification (simulated)
    console.log('\n🧪 Test 4: New Lead Notification');
    const leadPayload = {
      title: '🎯 New Lead Received',
      body: 'John Doe is interested in Solar Panel Installation.',
      icon: '/hl-energy-logo-192w.png',
      badge: '/hl-energy-logo-96w.png',
      tag: 'test-new-lead',
      data: {
        url: '/dashboard/leads',
        type: 'new-lead',
        leadId: 'test-123'
      },
      requireInteraction: true,
      actions: [
        {
          action: 'view',
          title: 'View Lead',
          icon: '/hl-energy-logo-96w.png'
        },
        {
          action: 'dismiss',
          title: 'Dismiss'
        }
      ]
    };

    const result4 = await PushNotificationService.sendToAdmins(leadPayload);
    console.log(`   Result: ✅ Sent: ${result4.sent}, ❌ Failed: ${result4.failed}`);

    // Test 5: Send contact form notification (simulated)
    console.log('\n🧪 Test 5: Contact Form Notification');
    const contactPayload = {
      title: '📧 New Contact Form',
      body: 'Jane Smith: "I\'m interested in an energy audit for my home."',
      icon: '/hl-energy-logo-192w.png',
      badge: '/hl-energy-logo-96w.png',
      tag: 'test-contact-form',
      data: {
        url: '/dashboard/contacts',
        type: 'contact-form',
        contactId: 'test-456'
      },
      requireInteraction: false
    };

    const result5 = await PushNotificationService.sendToAdmins(contactPayload);
    console.log(`   Result: ✅ Sent: ${result5.sent}, ❌ Failed: ${result5.failed}`);

    // Final statistics
    console.log('\n📊 Final Statistics:');
    const finalStats = await PushNotificationService.getStats();
    console.log(`  Total Notifications Sent: ${finalStats.recentActivity?.sent || 0}`);
    console.log(`  Total Failures: ${finalStats.recentActivity?.failed || 0}`);

    console.log('\n🎉 Push notification tests completed!');
    console.log('\n💡 Tips:');
    console.log('   - Check your browser notifications to see the test messages');
    console.log('   - Click on notifications to test the action URLs');
    console.log('   - Check the browser developer tools for any errors');
    console.log('   - Use the admin dashboard to send custom notifications');

  } catch (error) {
    console.error('❌ Error testing push notifications:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Additional utility functions for testing specific scenarios
async function testUserSpecificNotification(userId, title, body) {
  try {
    const models = require('../shared/models');
    PushNotificationService.initialize(models);

    const payload = {
      title,
      body,
      icon: '/hl-energy-logo-192w.png',
      badge: '/hl-energy-logo-96w.png',
      tag: 'custom-test',
      data: {
        url: '/dashboard',
        type: 'custom-test'
      }
    };

    const result = await PushNotificationService.sendToUser(userId, payload);
    console.log(`Notification sent to user ${userId}: ✅ ${result.sent}, ❌ ${result.failed}`);
    return result;
  } catch (error) {
    console.error('Error sending user-specific notification:', error);
    throw error;
  }
}

async function testBulkNotifications(count = 5) {
  try {
    const models = require('../shared/models');
    PushNotificationService.initialize(models);

    console.log(`🔄 Sending ${count} test notifications...`);
    
    const promises = [];
    for (let i = 1; i <= count; i++) {
      const payload = {
        title: `🧪 Bulk Test ${i}`,
        body: `This is bulk test notification number ${i} of ${count}.`,
        icon: '/hl-energy-logo-192w.png',
        badge: '/hl-energy-logo-96w.png',
        tag: `bulk-test-${i}`,
        data: {
          url: '/dashboard',
          type: 'bulk-test',
          testNumber: i
        }
      };

      promises.push(PushNotificationService.sendToAdmins(payload));
      
      // Add small delay between notifications
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    const results = await Promise.all(promises);
    const totalSent = results.reduce((sum, result) => sum + result.sent, 0);
    const totalFailed = results.reduce((sum, result) => sum + result.failed, 0);

    console.log(`✅ Bulk test completed: ${totalSent} sent, ${totalFailed} failed`);
    return { sent: totalSent, failed: totalFailed };
  } catch (error) {
    console.error('Error in bulk notification test:', error);
    throw error;
  }
}

// Export functions for use in other scripts
module.exports = {
  testPushNotifications,
  testUserSpecificNotification,
  testBulkNotifications
};

// Run if called directly
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--bulk')) {
    const count = parseInt(args[args.indexOf('--bulk') + 1]) || 5;
    testBulkNotifications(count);
  } else if (args.includes('--user')) {
    const userId = parseInt(args[args.indexOf('--user') + 1]);
    const title = args[args.indexOf('--title') + 1] || 'Test Notification';
    const body = args[args.indexOf('--body') + 1] || 'This is a test notification.';
    
    if (userId) {
      testUserSpecificNotification(userId, title, body);
    } else {
      console.error('❌ User ID is required when using --user flag');
      process.exit(1);
    }
  } else {
    testPushNotifications();
  }
}

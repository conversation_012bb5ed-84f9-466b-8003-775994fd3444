#!/bin/bash

# Apply Clean Migration Script
# This script automatically applies the clean migration that handles duplicate indexes

echo "🔧 Applying Clean Push Notifications Migration"
echo "=============================================="

# Check if we're in the backend directory
if [ ! -f "package.json" ] || [ ! -d "shared" ]; then
    echo "❌ Error: Please run this script from the backend directory"
    echo "   cd backend && bash scripts/apply-clean-migration.sh"
    exit 1
fi

echo ""
echo "📋 Step 1: Checking current migration status..."
npx sequelize-cli db:migrate:status

echo ""
echo "📋 Step 2: Backing up original migration file..."
if [ -f "shared/migrations/20241210000000-create-push-subscriptions.js" ]; then
    mv shared/migrations/20241210000000-create-push-subscriptions.js \
       shared/migrations/20241210000000-create-push-subscriptions.js.backup
    echo "✅ Original migration backed up"
else
    echo "⚠️  Original migration file not found, continuing..."
fi

echo ""
echo "📋 Step 3: Installing clean migration..."
if [ -f "shared/migrations/20241210000002-create-push-subscriptions-clean.js" ]; then
    cp shared/migrations/20241210000002-create-push-subscriptions-clean.js \
       shared/migrations/20241210000000-create-push-subscriptions.js
    echo "✅ Clean migration installed"
else
    echo "❌ Error: Clean migration file not found!"
    echo "   Make sure 20241210000002-create-push-subscriptions-clean.js exists"
    exit 1
fi

echo ""
echo "📋 Step 4: Running migration..."
npx sequelize-cli db:migrate

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Migration completed successfully!"
    echo ""
    echo "🎯 Your push notification system is now ready with:"
    echo "   • MySQL-compatible schema"
    echo "   • No duplicate index errors"
    echo "   • Optimized performance indexes"
    echo "   • Hash-based endpoint lookups"
    echo ""
    echo "📊 Next steps:"
    echo "   1. Update your push notification service to use the database version"
    echo "   2. Test push notifications with: node scripts/test-endpoint-hashing.js"
    echo "   3. Initialize the service in your app: PushNotificationService.initialize(models)"
else
    echo ""
    echo "❌ Migration failed!"
    echo ""
    echo "🔍 Troubleshooting:"
    echo "   1. Check database connection"
    echo "   2. Verify database user permissions"
    echo "   3. Check for existing table conflicts"
    echo "   4. Review migration logs above"
    echo ""
    echo "💡 Manual fix options:"
    echo "   node scripts/fix-duplicate-index.js"
fi

echo ""
echo "📋 Migration process complete."

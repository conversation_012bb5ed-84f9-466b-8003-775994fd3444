#!/usr/bin/env node

/**
 * Send Push Notification Script
 * 
 * Usage examples:
 *   node scripts/send-push-notification.js --user 1 --title "Hello" --body "Test message"
 *   node scripts/send-push-notification.js --email "<EMAIL>" --title "Welcome" --body "Welcome to HLenergy"
 *   node scripts/send-push-notification.js --all --title "Announcement" --body "System maintenance tonight"
 */

const { initializeDatabase } = require('../shared/models');
const PushNotificationService = require('../shared/services/pushNotificationServiceDB');

async function sendPushNotification() {
  const args = process.argv.slice(2);
  
  // Parse command line arguments
  const options = {};
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i].replace('--', '');
    const value = args[i + 1];
    options[key] = value;
  }

  // Validate required arguments
  if (!options.title || !options.body) {
    console.error('❌ Error: --title and --body are required');
    console.log('\n📖 Usage:');
    console.log('  node scripts/send-push-notification.js --user 1 --title "Hello" --body "Test message"');
    console.log('  node scripts/send-push-notification.js --email "<EMAIL>" --title "Welcome" --body "Welcome message"');
    console.log('  node scripts/send-push-notification.js --all --title "Announcement" --body "System maintenance"');
    process.exit(1);
  }

  try {
    console.log('🔔 Push Notification Sender');
    console.log('===========================');

    // Initialize database
    console.log('🗄️  Initializing database...');
    const dbInitialized = await initializeDatabase();
    
    if (!dbInitialized) {
      console.error('❌ Database initialization failed');
      process.exit(1);
    }

    // Initialize push service
    const models = require('../shared/models');
    PushNotificationService.initialize(models);
    console.log('✅ Push service initialized');

    // Create notification payload
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    const payload = {
      title: options.title,
      body: options.body,
      icon: `${baseUrl}/hl-energy-logo-192w.png`,
      badge: `${baseUrl}/hl-energy-logo-96w.png`,
      tag: options.tag || 'manual-notification',
      data: {
        url: options.url || '/dashboard',
        type: 'manual',
        timestamp: Date.now()
      },
      requireInteraction: options.requireInteraction !== 'false',
      actions: [
        {
          action: 'view',
          title: 'View Dashboard',
          icon: `${baseUrl}/hl-energy-logo-96w.png`
        },
        {
          action: 'dismiss',
          title: 'Dismiss'
        }
      ]
    };

    console.log('\n📋 Notification Details:');
    console.log(`  Title: ${payload.title}`);
    console.log(`  Body: ${payload.body}`);
    console.log(`  URL: ${payload.data.url}`);

    let result;

    if (options.all) {
      // Send to all users
      console.log('\n🌐 Sending to all users...');
      result = await PushNotificationService.sendToAllUsers(payload);
      
    } else if (options.user) {
      // Send to specific user by ID
      const userId = parseInt(options.user);
      console.log(`\n👤 Sending to user ID: ${userId}...`);
      result = await PushNotificationService.sendToUser(userId, payload);
      
    } else if (options.email) {
      // Send to specific user by email
      console.log(`\n📧 Sending to user email: ${options.email}...`);
      
      // Find user by email
      const { User } = models;
      const user = await User.findOne({ where: { email: options.email } });
      
      if (!user) {
        console.error(`❌ User not found with email: ${options.email}`);
        process.exit(1);
      }
      
      console.log(`✅ Found user: ${user.name} (ID: ${user.id})`);
      result = await PushNotificationService.sendToUser(user.id, payload);
      
    } else {
      console.error('❌ Error: Must specify --user, --email, or --all');
      process.exit(1);
    }

    // Display results
    console.log('\n📊 Results:');
    console.log(`  ✅ Sent: ${result.sent}`);
    console.log(`  ❌ Failed: ${result.failed}`);
    console.log(`  📱 Total: ${result.sent + result.failed}`);

    if (result.sent > 0) {
      console.log('\n🎉 Push notification sent successfully!');
    } else {
      console.log('\n⚠️  No notifications were sent. Check if users have active subscriptions.');
    }

  } catch (error) {
    console.error('❌ Error sending push notification:', error.message);
    process.exit(1);
  }
}

// Additional utility functions for common notification types
class NotificationTemplates {
  static newLead(leadName, service) {
    return {
      title: '🎯 New Lead Received',
      body: `${leadName} is interested in ${service}`,
      tag: 'new-lead',
      data: {
        url: '/dashboard/leads',
        type: 'new-lead'
      }
    };
  }

  static contactForm(contactName, message) {
    return {
      title: '📧 New Contact Form',
      body: `${contactName}: ${message.substring(0, 50)}...`,
      tag: 'contact-form',
      data: {
        url: '/dashboard/contacts',
        type: 'contact-form'
      }
    };
  }

  static systemAlert(message) {
    return {
      title: '⚠️ System Alert',
      body: message,
      tag: 'system-alert',
      requireInteraction: true,
      data: {
        url: '/dashboard',
        type: 'system-alert'
      }
    };
  }

  static welcome(userName) {
    return {
      title: '🎉 Welcome to HLenergy',
      body: `Hello ${userName}! Welcome to our energy consultation platform.`,
      tag: 'welcome',
      data: {
        url: '/dashboard',
        type: 'welcome'
      }
    };
  }
}

// Export for use in other scripts
module.exports = {
  sendPushNotification,
  NotificationTemplates
};

// Run if called directly
if (require.main === module) {
  sendPushNotification();
}

# Fix Duplicate Index Error - Step by Step

## Problem
```
ERROR: Duplicate key name 'push_subscriptions_user_id_idx'
```

## Quick Fix (Choose One Method)

### Method 1: Automated Fix (Recommended)
```bash
cd backend
node scripts/fix-push-migration-completely.js
```

### Method 2: Manual Database Cleanup
```bash
cd backend

# 1. Clean up database
mysql -u your_username -p your_database < scripts/cleanup-push-subscriptions.sql

# 2. Replace migration file
mv shared/migrations/20241210000000-create-push-subscriptions.js \
   shared/migrations/20241210000000-create-push-subscriptions.js.old

cp shared/migrations/20241210000002-create-push-subscriptions-clean.js \
   shared/migrations/20241210000000-create-push-subscriptions.js

# 3. Run migration
npx sequelize-cli db:migrate
```

### Method 3: Sequelize Undo and Redo
```bash
cd backend

# 1. Check status
npx sequelize-cli db:migrate:status

# 2. Undo the problematic migration
npx sequelize-cli db:migrate:undo

# 3. Replace with clean migration
mv shared/migrations/20241210000000-create-push-subscriptions.js \
   shared/migrations/20241210000000-create-push-subscriptions.js.old

cp shared/migrations/20241210000002-create-push-subscriptions-clean.js \
   shared/migrations/20241210000000-create-push-subscriptions.js

# 4. Run migration again
npx sequelize-cli db:migrate
```

## What the Clean Migration Does

✅ **Checks if table exists** before creating
✅ **Handles duplicate indexes gracefully** 
✅ **Uses conflict-free index names**
✅ **Can be run multiple times safely**
✅ **Preserves existing data**

## Verification

After successful migration, you should see:
```
✅ Created index: idx_push_subscriptions_user_id
✅ Created index: idx_push_subscriptions_endpoint_hash
✅ Created index: idx_push_subscriptions_is_active
✅ Push subscriptions table and indexes created successfully!
```

## Troubleshooting

### If Method 1 fails:
- Check database connection
- Verify database user permissions
- Try Method 2 (manual cleanup)

### If Method 2 fails:
- Check MySQL credentials
- Verify database name
- Run SQL commands individually

### If Method 3 fails:
- The migration might not be tracked properly
- Use Method 2 for complete cleanup

## Next Steps

After successful migration:

1. **Update your service** to use database storage:
   ```javascript
   const PushNotificationService = require('./services/pushNotificationServiceDB');
   PushNotificationService.initialize(models);
   ```

2. **Test the system**:
   ```bash
   node scripts/test-endpoint-hashing.js
   ```

3. **Verify in database**:
   ```sql
   SHOW TABLES LIKE 'push_subscriptions';
   DESCRIBE push_subscriptions;
   SHOW INDEX FROM push_subscriptions;
   ```

## Files Created

- `20241210000002-create-push-subscriptions-clean.js` - Clean migration
- `cleanup-push-subscriptions.sql` - Database cleanup script
- `fix-push-migration-completely.js` - Automated fix script

## Success Indicators

✅ Migration runs without errors
✅ Table `push_subscriptions` exists
✅ All indexes created successfully
✅ No duplicate key errors
✅ Push notifications ready for use

#!/usr/bin/env node

/**
 * Version Bump CLI Script
 * 
 * Usage:
 *   node scripts/version-bump.js [patch|minor|major] [options]
 *   npm run version:patch
 *   npm run version:minor
 *   npm run version:major
 *   npm run version:status
 */

const VersionBump = require('../shared/utils/versionBump');

// Parse command line arguments
const args = process.argv.slice(2);
const command = args[0] || 'status';
const options = {
  updateChangelog: args.includes('--changelog') || args.includes('-c'),
  createTag: args.includes('--tag') || args.includes('-t'),
  dryRun: args.includes('--dry-run') || args.includes('-d')
};

async function main() {
  const versionBump = new VersionBump();

  console.log('🔧 HLenergy Backend Version Manager');
  console.log('===================================');

  switch (command.toLowerCase()) {
    case 'status':
      showStatus(versionBump);
      break;
    
    case 'patch':
    case 'minor':
    case 'major':
      if (options.dryRun) {
        showDryRun(versionBump, command);
      } else {
        await performBump(versionBump, command, options);
      }
      break;
    
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;
    
    default:
      console.error(`❌ Unknown command: ${command}`);
      console.log('Run "node scripts/version-bump.js help" for usage information');
      process.exit(1);
  }
}

function showStatus(versionBump) {
  const status = versionBump.getStatus();
  
  console.log('\n📊 Current Version Status:');
  console.log(`   Current: ${status.current}`);
  console.log(`   Components: ${status.components.major}.${status.components.minor}.${status.components.patch}`);
  console.log('');
  console.log('📈 Next Versions:');
  console.log(`   Patch: ${status.next.patch} (bug fixes)`);
  console.log(`   Minor: ${status.next.minor} (new features)`);
  console.log(`   Major: ${status.next.major} (breaking changes)`);
  console.log('');
  console.log('🚀 Usage:');
  console.log('   npm run version:patch    # Bug fixes');
  console.log('   npm run version:minor    # New features');
  console.log('   npm run version:major    # Breaking changes');
  console.log('   npm run version:status   # Show this status');
}

function showDryRun(versionBump, type) {
  const versionInfo = versionBump.bumpVersion(type);
  
  console.log(`\n🧪 Dry Run - ${type.toUpperCase()} Version Bump:`);
  console.log(`   Current: ${versionInfo.old}`);
  console.log(`   New: ${versionInfo.new}`);
  console.log(`   Type: ${versionInfo.type}`);
  console.log('');
  console.log('📝 Files that would be updated:');
  console.log('   • package.json');
  console.log('   • shared/config/version.js (if exists)');
  console.log('   • CHANGELOG.md (if --changelog flag used)');
  console.log('');
  console.log('🏷️  Git tag that would be created:');
  console.log(`   v${versionInfo.new} (if --tag flag used)`);
  console.log('');
  console.log('💡 To perform actual bump:');
  console.log(`   node scripts/version-bump.js ${type}`);
}

async function performBump(versionBump, type, options) {
  console.log(`\n🚀 Performing ${type.toUpperCase()} version bump...`);
  
  if (options.updateChangelog) {
    console.log('📝 Will update CHANGELOG.md');
  }
  
  if (options.createTag) {
    console.log('🏷️  Will create git tag');
  }
  
  console.log('');
  
  const result = await versionBump.performBump(type, options);
  
  if (result.success) {
    console.log('\n🎉 Version bump completed successfully!');
    
    console.log('\n📋 Next Steps:');
    console.log('   1. Review the changes');
    console.log('   2. Commit the version bump:');
    console.log(`      git add -A`);
    console.log(`      git commit -m "chore: bump version to ${result.versionInfo.new}"`);
    
    if (options.createTag) {
      console.log('   3. Push with tags:');
      console.log('      git push origin main --tags');
    } else {
      console.log('   3. Push changes:');
      console.log('      git push origin main');
      console.log('   4. Create tag manually (optional):');
      console.log(`      git tag -a v${result.versionInfo.new} -m "Version ${result.versionInfo.new}"`);
      console.log('      git push origin --tags');
    }
    
    console.log('\n🚀 Deploy when ready!');
  } else {
    console.log('\n❌ Version bump failed. Please check the errors above.');
    process.exit(1);
  }
}

function showHelp() {
  console.log('\n📖 HLenergy Backend Version Manager Help');
  console.log('');
  console.log('🎯 Commands:');
  console.log('   status                    Show current version status');
  console.log('   patch                     Bump patch version (1.0.0 → 1.0.1)');
  console.log('   minor                     Bump minor version (1.0.0 → 1.1.0)');
  console.log('   major                     Bump major version (1.0.0 → 2.0.0)');
  console.log('   help                      Show this help message');
  console.log('');
  console.log('🏷️  Options:');
  console.log('   --changelog, -c           Update CHANGELOG.md');
  console.log('   --tag, -t                 Create git tag');
  console.log('   --dry-run, -d             Show what would be changed');
  console.log('');
  console.log('📝 Examples:');
  console.log('   node scripts/version-bump.js status');
  console.log('   node scripts/version-bump.js patch');
  console.log('   node scripts/version-bump.js minor --changelog --tag');
  console.log('   node scripts/version-bump.js major --dry-run');
  console.log('');
  console.log('🚀 NPM Scripts:');
  console.log('   npm run version:status    # Show version status');
  console.log('   npm run version:patch     # Bump patch version');
  console.log('   npm run version:minor     # Bump minor version');
  console.log('   npm run version:major     # Bump major version');
  console.log('');
  console.log('📋 Semantic Versioning:');
  console.log('   PATCH: Bug fixes, security patches');
  console.log('   MINOR: New features, backwards compatible');
  console.log('   MAJOR: Breaking changes, API changes');
}

// Run the CLI
main().catch(error => {
  console.error('❌ Error:', error.message);
  process.exit(1);
});

#!/usr/bin/env node

/**
 * Complete Push Migration Fix Script
 * 
 * This script completely resolves the duplicate index error by:
 * 1. Checking current database state
 * 2. Cleaning up any partial migration state
 * 3. Using the correct migration file
 * 4. Running the migration successfully
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Complete Push Migration Fix');
console.log('==============================');

// Check if we're in the right directory
const currentDir = process.cwd();
const isBackendDir = fs.existsSync(path.join(currentDir, 'package.json')) && 
                    fs.existsSync(path.join(currentDir, 'shared'));

if (!isBackendDir) {
  console.error('❌ Please run this script from the backend directory');
  console.error('   cd backend && node scripts/fix-push-migration-completely.js');
  process.exit(1);
}

async function fixPushMigration() {
  try {
    console.log('\n📋 Step 1: Checking migration status...');
    try {
      const status = execSync('npx sequelize-cli db:migrate:status', { encoding: 'utf8' });
      console.log(status);
    } catch (error) {
      console.log('⚠️  Could not check migration status, continuing...');
    }

    console.log('\n📋 Step 2: Checking for existing push_subscriptions table...');
    
    // Check if table exists by trying to describe it
    let tableExists = false;
    try {
      execSync('npx sequelize-cli db:migrate:status | grep push-subscriptions', { encoding: 'utf8' });
      tableExists = true;
      console.log('✅ Push subscriptions migration found in database');
    } catch (error) {
      console.log('ℹ️  No push subscriptions migration found in database');
    }

    console.log('\n📋 Step 3: Cleaning up migration files...');
    
    const migrationsDir = path.join(currentDir, 'shared', 'migrations');
    const oldMigrationFile = path.join(migrationsDir, '20241210000000-create-push-subscriptions.js');
    const cleanMigrationFile = path.join(migrationsDir, '20241210000002-create-push-subscriptions-clean.js');
    
    // Remove old migration file
    if (fs.existsSync(oldMigrationFile)) {
      const backupFile = oldMigrationFile + '.old';
      fs.renameSync(oldMigrationFile, backupFile);
      console.log('✅ Backed up old migration file');
    }

    // Copy clean migration to correct name
    if (fs.existsSync(cleanMigrationFile)) {
      fs.copyFileSync(cleanMigrationFile, oldMigrationFile);
      console.log('✅ Installed clean migration file');
    } else {
      console.error('❌ Clean migration file not found!');
      process.exit(1);
    }

    console.log('\n📋 Step 4: Handling existing table/indexes...');
    
    if (tableExists) {
      console.log('⚠️  Table exists, will undo and redo migration...');
      try {
        execSync('npx sequelize-cli db:migrate:undo --name 20241210000000-create-push-subscriptions.js', { 
          encoding: 'utf8',
          stdio: 'inherit'
        });
        console.log('✅ Undid previous migration');
      } catch (error) {
        console.log('⚠️  Could not undo migration, will try manual cleanup...');
        
        // Manual cleanup - create a temporary SQL script
        const cleanupSQL = `
-- Drop indexes if they exist
DROP INDEX IF EXISTS push_subscriptions_user_id_idx ON push_subscriptions;
DROP INDEX IF EXISTS push_subscriptions_endpoint_idx ON push_subscriptions;
DROP INDEX IF EXISTS push_subscriptions_is_active_idx ON push_subscriptions;
DROP INDEX IF EXISTS push_subscriptions_user_role_idx ON push_subscriptions;
DROP INDEX IF EXISTS push_subscriptions_last_used_idx ON push_subscriptions;

-- Drop table if it exists
DROP TABLE IF EXISTS push_subscriptions;

-- Remove from migration tracking
DELETE FROM SequelizeMeta WHERE name = '20241210000000-create-push-subscriptions.js';
        `;
        
        fs.writeFileSync('cleanup-push-subscriptions.sql', cleanupSQL);
        console.log('📝 Created cleanup SQL script: cleanup-push-subscriptions.sql');
        console.log('');
        console.log('⚠️  Please run this SQL script manually in your database:');
        console.log('   mysql -u username -p database_name < cleanup-push-subscriptions.sql');
        console.log('');
        console.log('Then run this script again.');
        return;
      }
    }

    console.log('\n📋 Step 5: Running clean migration...');
    try {
      execSync('npx sequelize-cli db:migrate', { 
        encoding: 'utf8',
        stdio: 'inherit'
      });
      console.log('✅ Migration completed successfully!');
    } catch (error) {
      console.error('❌ Migration failed!');
      console.error('Error:', error.message);
      
      console.log('\n🔍 Manual fix required:');
      console.log('1. Check database connection');
      console.log('2. Run the cleanup SQL script if not done already');
      console.log('3. Try running the migration again');
      return;
    }

    console.log('\n🎉 Success! Push notification system is ready!');
    console.log('');
    console.log('📊 What was created:');
    console.log('✅ push_subscriptions table');
    console.log('✅ Optimized indexes for performance');
    console.log('✅ Hash-based endpoint lookups');
    console.log('✅ MySQL-compatible schema');
    console.log('');
    console.log('🚀 Next steps:');
    console.log('1. Update your push service to use database storage');
    console.log('2. Initialize: PushNotificationService.initialize(models)');
    console.log('3. Test: node scripts/test-endpoint-hashing.js');

  } catch (error) {
    console.error('❌ Script failed:', error.message);
    console.log('\n🆘 Manual recovery steps:');
    console.log('1. Run: npx sequelize-cli db:migrate:status');
    console.log('2. If needed: npx sequelize-cli db:migrate:undo');
    console.log('3. Clean database manually with provided SQL script');
    console.log('4. Run: npx sequelize-cli db:migrate');
  }
}

fixPushMigration();

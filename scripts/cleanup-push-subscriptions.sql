-- Cleanup Push Subscriptions Migration
-- This SQL script completely cleans up any partial migration state
-- Run this if you're getting duplicate index errors

-- Show current state
SELECT 'Current push_subscriptions table state:' as info;
SHOW CREATE TABLE push_subscriptions;

SELECT 'Current indexes on push_subscriptions:' as info;
SHOW INDEX FROM push_subscriptions;

-- Drop all possible conflicting indexes (ignore errors if they don't exist)
SET sql_notes = 0; -- Suppress warnings for non-existent indexes

DROP INDEX IF EXISTS push_subscriptions_user_id_idx ON push_subscriptions;
DROP INDEX IF EXISTS push_subscriptions_endpoint_idx ON push_subscriptions;
DROP INDEX IF EXISTS push_subscriptions_endpoint_hash_idx ON push_subscriptions;
DROP INDEX IF EXISTS push_subscriptions_is_active_idx ON push_subscriptions;
DROP INDEX IF EXISTS push_subscriptions_user_role_idx ON push_subscriptions;
DROP INDEX IF EXISTS push_subscriptions_last_used_idx ON push_subscriptions;

-- Drop new-style indexes too
DROP INDEX IF EXISTS idx_push_subscriptions_user_id ON push_subscriptions;
DROP INDEX IF EXISTS idx_push_subscriptions_endpoint_hash ON push_subscriptions;
DROP INDEX IF EXISTS idx_push_subscriptions_is_active ON push_subscriptions;
DROP INDEX IF EXISTS idx_push_subscriptions_user_role ON push_subscriptions;
DROP INDEX IF EXISTS idx_push_subscriptions_last_used ON push_subscriptions;
DROP INDEX IF EXISTS idx_push_subscriptions_user_active ON push_subscriptions;
DROP INDEX IF EXISTS idx_push_subscriptions_role_active ON push_subscriptions;

SET sql_notes = 1; -- Re-enable warnings

-- Drop the table completely
DROP TABLE IF EXISTS push_subscriptions;

-- Remove from Sequelize migration tracking
DELETE FROM SequelizeMeta WHERE name = '20241210000000-create-push-subscriptions.js';
DELETE FROM SequelizeMeta WHERE name LIKE '%push-subscriptions%';

-- Verify cleanup
SELECT 'Cleanup completed. Remaining migration entries:' as info;
SELECT * FROM SequelizeMeta WHERE name LIKE '%push%';

SELECT 'Tables after cleanup:' as info;
SHOW TABLES LIKE '%push%';

SELECT 'Ready for fresh migration!' as status;

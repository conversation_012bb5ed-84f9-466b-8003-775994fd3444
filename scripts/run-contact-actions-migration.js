/**
 * <PERSON><PERSON><PERSON> to create the contact_actions table
 * 
 * This script creates the contact_actions table to track user interactions
 * with contact methods (email, phone, WhatsApp, address)
 */

const fs = require('fs');
const path = require('path');
const { sequelize } = require('../shared/config/database');
const { Sequelize } = require('sequelize');

async function runMigration() {
  try {
    console.log('Starting contact_actions table migration...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../migrations/012_create_contact_actions_table.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = sql
      .split(';')
      .filter(statement => statement.trim() !== '')
      .map(statement => statement.trim() + ';');
    
    // Execute each statement
    for (const statement of statements) {
      await sequelize.query(statement, {
        type: Sequelize.QueryTypes.RAW
      });
    }
    
    console.log('✅ Successfully created contact_actions table');
    
    // Verify the table exists
    const [results] = await sequelize.query(
      "SELECT * FROM information_schema.tables WHERE table_schema = ? AND table_name = 'contact_actions' LIMIT 1;",
      {
        replacements: [sequelize.config.database],
        type: Sequelize.QueryTypes.SELECT
      }
    );
    
    if (results) {
      console.log('✅ Verified contact_actions table exists');
    } else {
      console.error('❌ Could not verify contact_actions table');
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    // Close the connection
    await sequelize.close();
    console.log('Database connection closed');
  }
}

// Run the migration
runMigration();

#!/usr/bin/env node

/**
 * Reset PIN Lockout Script
 * Resets PIN attempts and lockout for testing progressive lockout system
 */

const { sequelize } = require('../shared/config/database');

async function resetPinLockout(userId = null) {
  try {
    console.log('🔄 Resetting PIN lockout...');

    let query = 'UPDATE admin_pins SET attempts = 0, locked_until = NULL, lockout_count = 0';
    let params = [];

    if (userId) {
      query += ' WHERE user_id = ?';
      params.push(userId);
      console.log(`🎯 Targeting user ID: ${userId}`);
    } else {
      console.log('🌐 Resetting for all users');
    }

    const [result] = await sequelize.query(query, {
      replacements: params
    });

    console.log(`✅ PIN lockout reset successfully`);
    console.log(`📊 Affected rows: ${result.affectedRows || 'Unknown'}`);

    // Show current PIN status
    const [pins] = await sequelize.query(`
      SELECT user_id, attempts, locked_until, lockout_count, 
             CASE 
               WHEN locked_until IS NULL THEN 'Unlocked'
               WHEN locked_until > NOW() THEN 'Locked'
               ELSE 'Expired Lock'
             END as status
      FROM admin_pins 
      ${userId ? 'WHERE user_id = ?' : ''}
      ORDER BY user_id
    `, {
      replacements: userId ? [userId] : []
    });

    console.log('\n📋 Current PIN Status:');
    console.table(pins);

  } catch (error) {
    console.error('❌ Error resetting PIN lockout:', error);
    throw error;
  }
}

async function main() {
  const userId = process.argv[2];

  try {
    await resetPinLockout(userId ? parseInt(userId) : null);
  } catch (error) {
    console.error('Script failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  resetPinLockout,
};

const { sequelize } = require('./src/config/database');

async function updateLogSchema() {
  try {
    console.log('🔄 Updating log schema...');
    
    // Drop the existing constraint and update the column
    await sequelize.query(`
      ALTER TABLE logs MODIFY COLUMN type VARCHAR(50) NULL 
      COMMENT 'Allowed types: request, error, authentication, security, database, performance, system, route_not_found, email_queue, email_worker, email, socket, socket_server, socket_connection, socket_authentication, socket_room, socket_analytics, socket_communication, socket_performance, socket_metrics_request, socket_api_error, socket_health_error, socket_logs_error, socket_memory_error, socket_broadcast, socket_error, socket_cleanup, memory_cleanup, memory_management, memory_monitoring, memory_alert, manual_cleanup, cleanup_error, server_shutdown, shutdown_error'
    `);
    
    console.log('✅ Log schema updated successfully');
    
    // Test inserting a log with new type
    await sequelize.query(`
      INSERT INTO logs (level, message, type, created_at) 
      VALUES ('info', 'Test log with new type', 'socket_server', NOW())
    `);
    
    console.log('✅ Test log inserted successfully');
    
    // Show current log types
    const [results] = await sequelize.query(`
      SELECT type, COUNT(*) as count 
      FROM logs 
      WHERE type IS NOT NULL
      GROUP BY type 
      ORDER BY count DESC 
      LIMIT 20
    `);
    
    console.log('📊 Current log types in database:');
    results.forEach(row => {
      console.log(`  ${row.type}: ${row.count}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error updating log schema:', error);
    process.exit(1);
  }
}

updateLogSchema();

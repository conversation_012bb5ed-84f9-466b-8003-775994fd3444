const { Sequelize } = require('sequelize');
require('dotenv').config();

// Database configuration
const sequelize = new Sequelize(
  process.env.DB_NAME || 'api_v2',
  process.env.DB_USER || 'hlenes_admin',
  process.env.DB_PASSWORD || 'hlenergyadmin1',
  {
    host: process.env.DB_HOST || 'logs.hlenergy.pt',
    port: process.env.DB_PORT || 3306,
    dialect: 'mysql',
    logging: console.log,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
  }
);

async function fixCRMTables() {
  try {
    console.log('🔧 Fixing CRM tables to match models...\n');

    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    console.log('\n📝 Adding missing columns to customers table...');
    
    // Add missing columns to customers table
    const customerColumns = [
      'ADD COLUMN lead_score INT DEFAULT 0',
      'ADD COLUMN tags JSON',
      'ADD COLUMN actual_value DECIMAL(15,2)',
      'ADD COLUMN conversion_date DATETIME',
      'ADD COLUMN preferred_contact_method ENUM("email", "phone", "text", "in_person") DEFAULT "email"',
      'ADD COLUMN communication_frequency ENUM("daily", "weekly", "monthly", "quarterly") DEFAULT "weekly"',
      'ADD COLUMN is_active BOOLEAN DEFAULT TRUE'
    ];

    for (const column of customerColumns) {
      try {
        await sequelize.query(`ALTER TABLE customers ${column}`);
        console.log(`✅ Added: ${column.split(' ')[2]}`);
      } catch (error) {
        if (error.message.includes('Duplicate column name')) {
          console.log(`⚠️  Column ${column.split(' ')[2]} already exists`);
        } else {
          console.log(`❌ Failed to add ${column.split(' ')[2]}: ${error.message}`);
        }
      }
    }

    console.log('\n📝 Adding missing columns to projects table...');
    
    // Add missing columns to projects table
    const projectColumns = [
      'ADD COLUMN project_number VARCHAR(50)',
      'ADD COLUMN team_members JSON',
      'ADD COLUMN type VARCHAR(100)',
      'ADD COLUMN category VARCHAR(100)',
      'ADD COLUMN estimated_duration INT',
      'ADD COLUMN actual_duration INT',
      'ADD COLUMN estimated_cost DECIMAL(15,2)',
      'ADD COLUMN actual_cost DECIMAL(15,2)',
      'ADD COLUMN budget DECIMAL(15,2)',
      'ADD COLUMN currency VARCHAR(10) DEFAULT "EUR"',
      'ADD COLUMN current_energy_usage DECIMAL(10,2)',
      'ADD COLUMN projected_savings DECIMAL(10,2)',
      'ADD COLUMN actual_savings DECIMAL(10,2)',
      'ADD COLUMN site_address TEXT',
      'ADD COLUMN site_city VARCHAR(100)',
      'ADD COLUMN site_state VARCHAR(100)',
      'ADD COLUMN site_zip_code VARCHAR(20)',
      'ADD COLUMN site_country VARCHAR(100) DEFAULT "Portugal"',
      'ADD COLUMN scope TEXT',
      'ADD COLUMN requirements TEXT',
      'ADD COLUMN deliverables TEXT',
      'ADD COLUMN completion_percentage INT DEFAULT 0',
      'ADD COLUMN milestones JSON',
      'ADD COLUMN risks TEXT',
      'ADD COLUMN issues TEXT',
      'ADD COLUMN notes TEXT',
      'ADD COLUMN tags JSON',
      'ADD COLUMN quality_score INT',
      'ADD COLUMN client_satisfaction INT',
      'ADD COLUMN is_active BOOLEAN DEFAULT TRUE'
    ];

    for (const column of projectColumns) {
      try {
        await sequelize.query(`ALTER TABLE projects ${column}`);
        console.log(`✅ Added: ${column.split(' ')[2]}`);
      } catch (error) {
        if (error.message.includes('Duplicate column name')) {
          console.log(`⚠️  Column ${column.split(' ')[2]} already exists`);
        } else {
          console.log(`❌ Failed to add ${column.split(' ')[2]}: ${error.message}`);
        }
      }
    }

    console.log('\n📝 Creating communications table...');
    
    // Create communications table
    try {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS communications (
          id INT AUTO_INCREMENT PRIMARY KEY,
          customer_id INT NOT NULL,
          project_id INT,
          type ENUM('email', 'phone', 'meeting', 'note', 'proposal', 'contract') NOT NULL,
          subject VARCHAR(255),
          content TEXT,
          direction ENUM('inbound', 'outbound'),
          status ENUM('unread', 'read', 'replied', 'archived') DEFAULT 'unread',
          priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
          scheduled_at DATETIME,
          completed_at DATETIME,
          user_id INT,
          read_by INT,
          created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          
          INDEX idx_communications_customer_id (customer_id),
          INDEX idx_communications_project_id (project_id),
          INDEX idx_communications_status (status),
          INDEX idx_communications_created_at (created_at),
          
          FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE ON UPDATE CASCADE,
          FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL ON UPDATE CASCADE,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
          FOREIGN KEY (read_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
        )
      `);
      console.log('✅ Communications table created successfully!');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('⚠️  Communications table already exists');
      } else {
        console.log(`❌ Failed to create communications table: ${error.message}`);
      }
    }

    console.log('\n📝 Creating documents table...');
    
    // Create documents table
    try {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS documents (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          original_name VARCHAR(255) NOT NULL,
          file_path VARCHAR(500) NOT NULL,
          file_size BIGINT NOT NULL,
          mime_type VARCHAR(100) NOT NULL,
          customer_id INT,
          project_id INT,
          communication_id INT,
          document_type VARCHAR(50),
          description TEXT,
          is_public BOOLEAN DEFAULT FALSE,
          uploaded_by INT,
          reviewed_by INT,
          approved_by INT,
          last_accessed_by INT,
          parent_document_id INT,
          version_number INT DEFAULT 1,
          created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          
          INDEX idx_documents_customer_id (customer_id),
          INDEX idx_documents_project_id (project_id),
          INDEX idx_documents_communication_id (communication_id),
          INDEX idx_documents_created_at (created_at),
          
          FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE ON UPDATE CASCADE,
          FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE ON UPDATE CASCADE,
          FOREIGN KEY (communication_id) REFERENCES communications(id) ON DELETE CASCADE ON UPDATE CASCADE,
          FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
          FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
          FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
          FOREIGN KEY (last_accessed_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
          FOREIGN KEY (parent_document_id) REFERENCES documents(id) ON DELETE SET NULL ON UPDATE CASCADE
        )
      `);
      console.log('✅ Documents table created successfully!');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('⚠️  Documents table already exists');
      } else {
        console.log(`❌ Failed to create documents table: ${error.message}`);
      }
    }

    console.log('\n🎉 CRM tables fixed successfully!');
    console.log('📊 All tables now match the Sequelize models');

  } catch (error) {
    console.error('❌ Fix failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await sequelize.close();
    console.log('\n🔌 Database connection closed.');
  }
}

// Run the fix
fixCRMTables();

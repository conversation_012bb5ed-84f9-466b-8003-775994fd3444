const express = require('express');
const router = express.Router();
const { body, query, validationResult } = require('express-validator');

// In-memory storage for analytics (in production, use a proper database)
let analyticsEvents = [];
let analyticsMetrics = {
  pageViews: 0,
  uniqueVisitors: new Set(),
  sessions: new Set(),
  conversions: 0,
  bounceRate: 0,
  avgSessionDuration: 0
};

/**
 * @swagger
 * /api/v1/analytics/events:
 *   post:
 *     summary: Track analytics events
 *     tags: [Analytics]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               events:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     event:
 *                       type: string
 *                     category:
 *                       type: string
 *                     properties:
 *                       type: object
 *                     sessionId:
 *                       type: string
 *                     userId:
 *                       type: string
 *     responses:
 *       200:
 *         description: Events tracked successfully
 *       400:
 *         description: Invalid request data
 */
router.post('/events', [
  body('events').isArray().withMessage('Events must be an array'),
  body('events.*.event').notEmpty().withMessage('Event name is required'),
  body('events.*.category').notEmpty().withMessage('Event category is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { events } = req.body;
    
    // Process and store events
    for (const event of events) {
      // Add timestamp if not present
      if (!event.properties.timestamp) {
        event.properties.timestamp = Date.now();
      }
      
      // Store event
      analyticsEvents.push({
        ...event,
        id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        receivedAt: new Date().toISOString()
      });
      
      // Update metrics
      updateMetrics(event);
    }

    console.log(`Received ${events.length} analytics events`);
    
    res.json({
      success: true,
      message: `Successfully tracked ${events.length} events`,
      eventsProcessed: events.length
    });
  } catch (error) {
    console.error('Analytics events error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to track analytics events',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/v1/analytics/metrics:
 *   get:
 *     summary: Get analytics metrics
 *     tags: [Analytics]
 *     parameters:
 *       - in: query
 *         name: timeRange
 *         schema:
 *           type: string
 *           enum: [24h, 7d, 30d, 90d]
 *         description: Time range for metrics
 *     responses:
 *       200:
 *         description: Analytics metrics
 */
router.get('/metrics', [
  query('timeRange').optional().isIn(['24h', '7d', '30d', '90d'])
], async (req, res) => {
  try {
    const { timeRange = '7d' } = req.query;
    
    // Calculate time range
    const now = Date.now();
    const timeRanges = {
      '24h': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000,
      '90d': 90 * 24 * 60 * 60 * 1000
    };
    
    const startTime = now - timeRanges[timeRange];
    
    // Filter events by time range
    const filteredEvents = analyticsEvents.filter(event => 
      event.properties.timestamp >= startTime
    );
    
    // Calculate metrics
    const metrics = calculateMetrics(filteredEvents, timeRange);
    
    res.json({
      success: true,
      data: metrics,
      timeRange,
      eventsCount: filteredEvents.length
    });
  } catch (error) {
    console.error('Analytics metrics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get analytics metrics',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/v1/analytics/business-metrics:
 *   get:
 *     summary: Get business metrics
 *     tags: [Analytics]
 *     parameters:
 *       - in: query
 *         name: timeRange
 *         schema:
 *           type: string
 *           enum: [24h, 7d, 30d, 90d]
 *         description: Time range for metrics
 *     responses:
 *       200:
 *         description: Business metrics
 */
router.get('/business-metrics', async (req, res) => {
  try {
    const { timeRange = '7d' } = req.query;
    
    // Mock business metrics (replace with real calculations)
    const businessMetrics = {
      revenue: Math.floor(Math.random() * 50000) + 10000,
      leads: Math.floor(Math.random() * 100) + 20,
      conversions: Math.floor(Math.random() * 50) + 5,
      customerAcquisitionCost: Math.floor(Math.random() * 200) + 50,
      lifetimeValue: Math.floor(Math.random() * 2000) + 500,
      churnRate: (Math.random() * 10).toFixed(2),
      growthRate: (Math.random() * 20).toFixed(2)
    };
    
    res.json({
      success: true,
      data: businessMetrics,
      timeRange
    });
  } catch (error) {
    console.error('Business metrics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get business metrics',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/v1/analytics/heatmap:
 *   get:
 *     summary: Get heatmap data
 *     tags: [Analytics]
 *     parameters:
 *       - in: query
 *         name: page
 *         required: true
 *         schema:
 *           type: string
 *         description: Page to get heatmap data for
 *       - in: query
 *         name: timeRange
 *         schema:
 *           type: string
 *           enum: [24h, 7d, 30d]
 *         description: Time range for heatmap data
 *     responses:
 *       200:
 *         description: Heatmap data
 */
router.get('/heatmap', async (req, res) => {
  try {
    const { page, timeRange = '7d' } = req.query;
    
    if (!page) {
      return res.status(400).json({
        success: false,
        message: 'Page parameter is required'
      });
    }
    
    // Filter click events for the specified page
    const clickEvents = analyticsEvents.filter(event => 
      event.event === 'click' && 
      event.properties.page === page
    );
    
    // Generate heatmap data
    const heatmapData = {
      page,
      clicks: clickEvents.map(event => ({
        x: event.properties.x,
        y: event.properties.y,
        timestamp: event.properties.timestamp,
        element: event.properties.element
      })),
      totalClicks: clickEvents.length
    };
    
    res.json({
      success: true,
      data: heatmapData,
      timeRange
    });
  } catch (error) {
    console.error('Heatmap data error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get heatmap data',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/v1/analytics/conversion-funnel:
 *   get:
 *     summary: Get conversion funnel data
 *     tags: [Analytics]
 *     parameters:
 *       - in: query
 *         name: timeRange
 *         schema:
 *           type: string
 *           enum: [24h, 7d, 30d, 90d]
 *         description: Time range for funnel data
 *     responses:
 *       200:
 *         description: Conversion funnel data
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   step:
 *                     type: string
 *                   users:
 *                     type: integer
 *                   conversionRate:
 *                     type: number
 */
router.get('/conversion-funnel', async (req, res) => {
  try {
    const { timeRange = '7d' } = req.query;

    // Calculate time range
    const now = Date.now();
    const timeRanges = {
      '24h': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000,
      '90d': 90 * 24 * 60 * 60 * 1000
    };

    const startTime = now - timeRanges[timeRange];

    // Filter events by time range
    const filteredEvents = analyticsEvents.filter(event =>
      event.properties.timestamp >= startTime
    );

    // Calculate funnel steps based on actual events
    const pageViews = filteredEvents.filter(e => e.event === 'page_view').length;
    const contactFormViews = filteredEvents.filter(e =>
      e.event === 'page_view' && e.properties.page === '/contact'
    ).length;
    const contactSubmissions = filteredEvents.filter(e =>
      e.event === 'contact_form_submission' || e.event === 'conversion'
    ).length;
    const quoteRequests = filteredEvents.filter(e =>
      e.event === 'quote_request'
    ).length;
    const consultations = filteredEvents.filter(e =>
      e.event === 'consultation_booked'
    ).length;
    const contracts = filteredEvents.filter(e =>
      e.event === 'contract_signed'
    ).length;

    // Use actual data if available, otherwise use realistic mock data
    const baseUsers = pageViews > 0 ? pageViews : 1000;
    const contactViews = contactFormViews > 0 ? contactFormViews : Math.floor(baseUsers * 0.15);
    const submissions = contactSubmissions > 0 ? contactSubmissions : Math.floor(contactViews * 0.5);
    const quotes = quoteRequests > 0 ? quoteRequests : Math.floor(submissions * 0.6);
    const consultationCount = consultations > 0 ? consultations : Math.floor(quotes * 0.6);
    const contractCount = contracts > 0 ? contracts : Math.floor(consultationCount * 0.55);

    const funnelData = [
      {
        step: 'Website Visit',
        users: baseUsers,
        conversionRate: 100
      },
      {
        step: 'Contact Page View',
        users: contactViews,
        conversionRate: Number(((contactViews / baseUsers) * 100).toFixed(1))
      },
      {
        step: 'Contact Form Submission',
        users: submissions,
        conversionRate: Number(((submissions / baseUsers) * 100).toFixed(1))
      },
      {
        step: 'Quote Request',
        users: quotes,
        conversionRate: Number(((quotes / baseUsers) * 100).toFixed(1))
      },
      {
        step: 'Consultation Booked',
        users: consultationCount,
        conversionRate: Number(((consultationCount / baseUsers) * 100).toFixed(1))
      },
      {
        step: 'Contract Signed',
        users: contractCount,
        conversionRate: Number(((contractCount / baseUsers) * 100).toFixed(1))
      }
    ];

    res.json({
      success: true,
      data: funnelData,
      timeRange,
      totalEvents: filteredEvents.length,
      debug: {
        eventsAnalyzed: filteredEvents.length,
        pageViewEvents: pageViews,
        contactFormEvents: contactSubmissions,
        quoteRequestEvents: quoteRequests,
        consultationEvents: consultations,
        contractEvents: contracts
      }
    });
  } catch (error) {
    console.error('Conversion funnel error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get conversion funnel data',
      error: error.message
    });
  }
});

// Helper function to update metrics
function updateMetrics(event) {
  switch (event.event) {
    case 'page_view':
      analyticsMetrics.pageViews++;
      if (event.sessionId) {
        analyticsMetrics.sessions.add(event.sessionId);
      }
      if (event.userId) {
        analyticsMetrics.uniqueVisitors.add(event.userId);
      }
      break;
    case 'conversion':
      analyticsMetrics.conversions++;
      break;
  }
}

// Helper function to calculate metrics
function calculateMetrics(events, timeRange) {
  const pageViews = events.filter(e => e.event === 'page_view').length;
  const uniqueSessions = new Set(events.map(e => e.sessionId)).size;
  const uniqueUsers = new Set(events.filter(e => e.userId).map(e => e.userId)).size;
  const conversions = events.filter(e => e.event === 'conversion').length;
  
  return {
    pageViews,
    uniqueVisitors: uniqueUsers,
    sessions: uniqueSessions,
    conversions,
    conversionRate: pageViews > 0 ? ((conversions / pageViews) * 100).toFixed(2) : 0,
    bounceRate: Math.floor(Math.random() * 50) + 20, // Mock data
    avgSessionDuration: Math.floor(Math.random() * 300) + 60, // Mock data
    topPages: getTopPages(events),
    deviceTypes: getDeviceTypes(events),
    trafficSources: getTrafficSources(events)
  };
}

function getTopPages(events) {
  const pageViews = events.filter(e => e.event === 'page_view');
  const pageCounts = {};
  
  pageViews.forEach(event => {
    const page = event.properties.page || '/';
    pageCounts[page] = (pageCounts[page] || 0) + 1;
  });
  
  return Object.entries(pageCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([page, views]) => ({ page, views }));
}

function getDeviceTypes(events) {
  // Mock device type data
  return [
    { type: 'Desktop', count: Math.floor(Math.random() * 100) + 50 },
    { type: 'Mobile', count: Math.floor(Math.random() * 80) + 30 },
    { type: 'Tablet', count: Math.floor(Math.random() * 20) + 5 }
  ];
}

function getTrafficSources(events) {
  // Mock traffic source data
  return [
    { source: 'Direct', count: Math.floor(Math.random() * 60) + 20 },
    { source: 'Google', count: Math.floor(Math.random() * 40) + 15 },
    { source: 'Social Media', count: Math.floor(Math.random() * 30) + 10 },
    { source: 'Referral', count: Math.floor(Math.random() * 20) + 5 }
  ];
}

module.exports = router;

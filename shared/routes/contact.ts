import express from 'express';
import { body } from 'express-validator';
import { submitContact, getContacts, updateContactStatus } from '../controllers/contactController';
import { authenticate, authorize } from '../middleware/auth';

const router = express.Router();

// Submit contact form (public)
router.post('/submit', [
  body('name').trim().isLength({ min: 2 }).withMessage('Name must be at least 2 characters'),
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('message').trim().isLength({ min: 10 }).withMessage('Message must be at least 10 characters'),
  body('phone').optional().isMobilePhone('any').withMessage('Please provide a valid phone number'),
], submitContact);

// Get all contact submissions (admin/staff only)
router.get('/', authenticate, authorize('admin', 'staff'), getContacts);

// Update contact status (admin/staff only)
router.patch('/:id/status', [
  authenticate,
  authorize('admin', 'staff'),
  body('status').isIn(['new', 'in_progress', 'resolved']).withMessage('Invalid status'),
], updateContactStatus);

export default router;

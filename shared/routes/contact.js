const express = require('express');
const { body, validationResult } = require('express-validator');

const router = express.Router();

// Mock contact submissions storage (replace with database later)
let contactSubmissions = [];

/**
 * @swagger
 * /api/contact/submit:
 *   post:
 *     summary: Submit a contact form
 *     tags: [Contact]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - message
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 2
 *                 example: "<PERSON> Do<PERSON>"
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               phone:
 *                 type: string
 *                 example: "+1234567890"
 *               message:
 *                 type: string
 *                 minLength: 10
 *                 example: "I'm interested in your energy consultation services."
 *     responses:
 *       201:
 *         description: Contact form submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/ContactSubmission'
 *                 message:
 *                   type: string
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/submit', [
  body('name').trim().isLength({ min: 2 }).withMessage('Name must be at least 2 characters'),
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('message').trim().isLength({ min: 10 }).withMessage('Message must be at least 10 characters'),
  body('phone').optional().isMobilePhone('any').withMessage('Please provide a valid phone number'),
], (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
      });
    }

    const { name, email, phone, message } = req.body;

    // Create contact submission
    const submission = {
      id: contactSubmissions.length + 1,
      name,
      email,
      phone: phone || null,
      message,
      status: 'new',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    contactSubmissions.push(submission);

    res.status(201).json({
      success: true,
      data: submission,
      message: 'Contact form submitted successfully. We will get back to you soon!',
    });
  } catch (error) {
    console.error('Contact submission error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' },
    });
  }
});

/**
 * @swagger
 * /api/contact:
 *   get:
 *     summary: Get all contact submissions (Admin only)
 *     tags: [Contact]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [new, in_progress, resolved]
 *         description: Filter by status
 *     responses:
 *       200:
 *         description: Contact submissions retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     contacts:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/ContactSubmission'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         pages:
 *                           type: integer
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/', (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const status = req.query.status;
    const offset = (page - 1) * limit;

    let filteredSubmissions = contactSubmissions;

    // Filter by status if provided
    if (status && ['new', 'in_progress', 'resolved'].includes(status)) {
      filteredSubmissions = contactSubmissions.filter(s => s.status === status);
    }

    // Sort by creation date (newest first)
    filteredSubmissions.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

    // Paginate
    const paginatedSubmissions = filteredSubmissions.slice(offset, offset + limit);
    const total = filteredSubmissions.length;

    res.json({
      success: true,
      data: {
        contacts: paginatedSubmissions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
      message: 'Contact submissions retrieved successfully',
    });
  } catch (error) {
    console.error('Get contacts error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' },
    });
  }
});

/**
 * @swagger
 * /api/contact/{id}/status:
 *   patch:
 *     summary: Update contact submission status (Admin only)
 *     tags: [Contact]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact submission ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [new, in_progress, resolved]
 *                 example: "in_progress"
 *     responses:
 *       200:
 *         description: Contact status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     contact:
 *                       $ref: '#/components/schemas/ContactSubmission'
 *                 message:
 *                   type: string
 *       404:
 *         description: Contact submission not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.patch('/:id/status', [
  body('status').isIn(['new', 'in_progress', 'resolved']).withMessage('Invalid status'),
], (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
      });
    }

    const { id } = req.params;
    const { status } = req.body;

    // Find contact submission
    const submissionIndex = contactSubmissions.findIndex(s => s.id === parseInt(id));
    if (submissionIndex === -1) {
      return res.status(404).json({
        success: false,
        error: { message: 'Contact submission not found' },
      });
    }

    // Update status
    contactSubmissions[submissionIndex].status = status;
    contactSubmissions[submissionIndex].updated_at = new Date().toISOString();

    res.json({
      success: true,
      data: { contact: contactSubmissions[submissionIndex] },
      message: 'Contact status updated successfully',
    });
  } catch (error) {
    console.error('Update contact status error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' },
    });
  }
});

module.exports = router;

const express = require('express');
const router = express.Router();
const { logger } = require('../config/logger');

/**
 * @swagger
 * components:
 *   schemas:
 *     TempTokenRequest:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: Display name for temporary user
 *           example: "Test User"
 *         email:
 *           type: string
 *           format: email
 *           description: Email for temporary user
 *           example: "<EMAIL>"
 *     TempTokenResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         token:
 *           type: string
 *           description: JWT token for Socket.io authentication
 *           example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *         user:
 *           type: object
 *           properties:
 *             userId:
 *               type: string
 *               example: "temp-1749235676910-uag87hdfc"
 *             email:
 *               type: string
 *               example: "<EMAIL>"
 *             name:
 *               type: string
 *               example: "Test User"
 *             role:
 *               type: string
 *               example: "guest"
 *             isTemporary:
 *               type: boolean
 *               example: true
 *         expiresIn:
 *           type: string
 *           example: "1 hour"
 *         message:
 *           type: string
 *           example: "Temporary token generated for Socket.io testing"
 *     SocketStatus:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           example: "active"
 *         connectedUsers:
 *           type: integer
 *           example: 5
 *         totalRooms:
 *           type: integer
 *           example: 3
 *         users:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/ConnectedUser'
 *         rooms:
 *           type: array
 *           items:
 *             type: string
 *         timestamp:
 *           type: string
 *           format: date-time
 *     ConnectedUser:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         email:
 *           type: string
 *         name:
 *           type: string
 *         role:
 *           type: string
 *         isTemporary:
 *           type: boolean
 *         connectedAt:
 *           type: string
 *           format: date-time
 *     SocketTestRequest:
 *       type: object
 *       required:
 *         - message
 *       properties:
 *         message:
 *           type: string
 *           description: Test message to broadcast
 *           example: "Hello Socket.io!"
 *     SocketNotifyRequest:
 *       type: object
 *       required:
 *         - title
 *         - message
 *       properties:
 *         userId:
 *           type: string
 *           description: Specific user ID to notify (optional)
 *         role:
 *           type: string
 *           description: Role to notify (optional)
 *         type:
 *           type: string
 *           enum: [info, success, warning, error]
 *           description: Notification type
 *           example: "info"
 *         title:
 *           type: string
 *           description: Notification title
 *           example: "Test Notification"
 *         message:
 *           type: string
 *           description: Notification message
 *           example: "This is a test notification"
 *         priority:
 *           type: string
 *           enum: [low, medium, high, urgent]
 *           description: Notification priority
 *           example: "medium"
 *     SocketBroadcastRequest:
 *       type: object
 *       required:
 *         - event
 *         - message
 *       properties:
 *         event:
 *           type: string
 *           description: Event name to broadcast
 *           example: "custom:event"
 *         message:
 *           type: string
 *           description: Message to broadcast
 *           example: "Custom event message"
 *         data:
 *           type: object
 *           description: Additional data to send
 *           example: {"key": "value"}
 *     AnalyticsEventRequest:
 *       type: object
 *       properties:
 *         type:
 *           type: string
 *           description: Analytics event type
 *           example: "pageview"
 *         page:
 *           type: string
 *           description: Page URL or identifier
 *           example: "/dashboard"
 *         data:
 *           type: object
 *           description: Additional analytics data
 *           example: {"duration": 5000}
 *     OnlineUsersResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         count:
 *           type: integer
 *           example: 5
 *         users:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/ConnectedUser'
 *         timestamp:
 *           type: string
 *           format: date-time
 *     RoomsResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         rooms:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               roomId:
 *                 type: string
 *               userCount:
 *                 type: integer
 *               users:
 *                 type: array
 *                 items:
 *                   $ref: '#/components/schemas/ConnectedUser'
 *         totalRooms:
 *           type: integer
 *         timestamp:
 *           type: string
 *           format: date-time
 *   tags:
 *     - name: Socket.io
 *       description: Real-time Socket.io management and testing endpoints
 */

// Socket.io management routes
let socketServer = null;

// Initialize socket server reference
const initializeSocketRoutes = (server) => {
  socketServer = server;
};

/**
 * @swagger
 * /api/v1/socket/temp-token:
 *   post:
 *     summary: Generate temporary token for Socket.io testing
 *     description: Creates a temporary JWT token for non-authenticated users to test Socket.io functionality
 *     tags: [Socket.io]
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/TempTokenRequest'
 *     responses:
 *       200:
 *         description: Temporary token generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/TempTokenResponse'
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Server error
 */
router.post('/temp-token', (req, res) => {
  const jwt = require('jsonwebtoken');

  const { name, email } = req.body;

  // Create temporary user data
  const tempUser = {
    userId: 'temp-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
    email: email || '<EMAIL>',
    name: name || 'Temporary User',
    role: 'guest',
    isTemporary: true
  };

  // Generate temporary token (expires in 1 hour)
  const token = jwt.sign(tempUser, process.env.JWT_SECRET || 'fallback-secret', {
    expiresIn: '1h'
  });

  res.json({
    success: true,
    token,
    user: tempUser,
    expiresIn: '1 hour',
    message: 'Temporary token generated for Socket.io testing'
  });
});

/**
 * @swagger
 * /api/v1/socket/status:
 *   get:
 *     summary: Get Socket.io server status
 *     description: Returns current status of Socket.io server including connected users and active rooms
 *     tags: [Socket.io]
 *     responses:
 *       200:
 *         description: Socket.io server status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SocketStatus'
 *       503:
 *         description: Socket.io server not available
 */
router.get('/status', (req, res) => {
  if (!socketServer) {
    return res.status(503).json({
      status: 'unavailable',
      message: 'Socket.io server not initialized'
    });
  }

  const connectedUsers = socketServer.getConnectedUsers();
  const rooms = Array.from(socketServer.rooms.keys());

  res.json({
    status: 'active',
    connectedUsers: connectedUsers.length,
    totalRooms: rooms.length,
    users: connectedUsers,
    rooms: rooms,
    timestamp: new Date().toISOString()
  });
});

/**
 * @swagger
 * /api/v1/socket/broadcast:
 *   post:
 *     summary: Broadcast message to all connected users
 *     tags: [Socket.io]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               event:
 *                 type: string
 *               message:
 *                 type: string
 *               data:
 *                 type: object
 *     responses:
 *       200:
 *         description: Message broadcasted successfully
 */
router.post('/broadcast', (req, res) => {
  if (!socketServer) {
    return res.status(503).json({
      success: false,
      message: 'Socket.io server not initialized'
    });
  }

  const { event, message, data } = req.body;

  if (!event || !message) {
    return res.status(400).json({
      success: false,
      message: 'Event and message are required'
    });
  }

  const broadcastData = {
    message,
    data: data || {},
    timestamp: new Date().toISOString(),
    from: 'system'
  };

  socketServer.broadcastToAll(event, broadcastData);

  res.json({
    success: true,
    message: 'Message broadcasted successfully',
    event,
    recipients: socketServer.getConnectedUsers().length
  });
});

/**
 * @swagger
 * /api/v1/socket/notify:
 *   post:
 *     summary: Send notification to specific user or role
 *     tags: [Socket.io]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *               role:
 *                 type: string
 *               type:
 *                 type: string
 *               title:
 *                 type: string
 *               message:
 *                 type: string
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high, urgent]
 *     responses:
 *       200:
 *         description: Notification sent successfully
 */
router.post('/notify', (req, res) => {
  if (!socketServer) {
    return res.status(503).json({
      success: false,
      message: 'Socket.io server not initialized'
    });
  }

  const { userId, role, type, title, message, priority } = req.body;

  if (!title || !message) {
    return res.status(400).json({
      success: false,
      message: 'Title and message are required'
    });
  }

  const notification = {
    id: Date.now(),
    type: type || 'info',
    title,
    message,
    priority: priority || 'medium',
    timestamp: new Date().toISOString(),
    from: 'system'
  };

  let sent = false;
  let recipients = 0;

  if (userId) {
    sent = socketServer.sendToUser(userId, 'notification:received', notification);
    recipients = sent ? 1 : 0;
  } else if (role) {
    socketServer.sendToRole(role, 'notification:received', notification);
    sent = true;
    recipients = socketServer.getConnectedUsers().filter(user => user.role === role).length;
  } else {
    socketServer.broadcastToAll('notification:received', notification);
    sent = true;
    recipients = socketServer.getConnectedUsers().length;
  }

  res.json({
    success: sent,
    message: sent ? 'Notification sent successfully' : 'User not found or offline',
    notification,
    recipients
  });
});

/**
 * @swagger
 * /api/v1/socket/analytics:
 *   post:
 *     summary: Send analytics event
 *     tags: [Socket.io]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *               page:
 *                 type: string
 *               data:
 *                 type: object
 *     responses:
 *       200:
 *         description: Analytics event sent successfully
 */
router.post('/analytics', (req, res) => {
  if (!socketServer) {
    return res.status(503).json({
      success: false,
      message: 'Socket.io server not initialized'
    });
  }

  const { type, page, data } = req.body;

  const analyticsEvent = {
    type: type || 'custom',
    page,
    data: data || {},
    timestamp: new Date().toISOString(),
    source: 'api'
  };

  // Send to admin dashboard
  socketServer.io.to('admin').emit('analytics:api_event', analyticsEvent);

  res.json({
    success: true,
    message: 'Analytics event sent successfully',
    event: analyticsEvent
  });
});

/**
 * @swagger
 * /api/v1/socket/rooms:
 *   get:
 *     summary: Get all active rooms
 *     tags: [Socket.io]
 *     responses:
 *       200:
 *         description: List of active rooms
 */
router.get('/rooms', (req, res) => {
  if (!socketServer) {
    return res.status(503).json({
      success: false,
      message: 'Socket.io server not initialized'
    });
  }

  const rooms = Array.from(socketServer.rooms.entries()).map(([roomId, socketIds]) => ({
    roomId,
    userCount: socketIds.size,
    users: Array.from(socketIds).map(socketId => socketServer.userSockets.get(socketId)).filter(Boolean)
  }));

  res.json({
    success: true,
    rooms,
    totalRooms: rooms.length,
    timestamp: new Date().toISOString()
  });
});

/**
 * @swagger
 * /api/v1/socket/users/online:
 *   get:
 *     summary: Get all online users
 *     tags: [Socket.io]
 *     responses:
 *       200:
 *         description: List of online users
 */
router.get('/users/online', (req, res) => {
  if (!socketServer) {
    return res.status(503).json({
      success: false,
      message: 'Socket.io server not initialized'
    });
  }

  const onlineUsers = socketServer.getConnectedUsers();

  res.json({
    success: true,
    users: onlineUsers,
    count: onlineUsers.length,
    timestamp: new Date().toISOString()
  });
});

/**
 * @swagger
 * /api/v1/socket/test:
 *   post:
 *     summary: Test Socket.io connection
 *     description: Sends a test message to all connected Socket.io clients
 *     tags: [Socket.io]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SocketTestRequest'
 *     responses:
 *       200:
 *         description: Test message sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Test message sent to all connected users"
 *                 data:
 *                   type: object
 *                 recipients:
 *                   type: integer
 *                   example: 5
 *       400:
 *         description: Invalid request data
 *       503:
 *         description: Socket.io server not available
 */
router.post('/test', (req, res) => {
  if (!socketServer) {
    return res.status(503).json({
      success: false,
      message: 'Socket.io server not initialized'
    });
  }

  const { message } = req.body;

  const testData = {
    message: message || 'Test message from API',
    timestamp: new Date().toISOString(),
    source: 'api_test'
  };

  socketServer.broadcastToAll('test:message', testData);

  res.json({
    success: true,
    message: 'Test message sent to all connected users',
    data: testData,
    recipients: socketServer.getConnectedUsers().length
  });
});

/**
 * @swagger
 * /api/v1/socket/metrics:
 *   get:
 *     summary: Get Socket.io performance metrics
 *     description: Returns detailed performance metrics and statistics for Socket.io server
 *     tags: [Socket.io]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Performance metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalConnections:
 *                       type: number
 *                     activeConnections:
 *                       type: number
 *                     totalEvents:
 *                       type: number
 *                     peakConnections:
 *                       type: number
 *                     uptime:
 *                       type: number
 *                     eventsByType:
 *                       type: object
 *                     connectionsPerSecond:
 *                       type: number
 *                     eventsPerSecond:
 *                       type: number
 *                     averageResponseTime:
 *                       type: number
 *       503:
 *         description: Socket.io server not available
 */
router.get('/metrics', (req, res) => {
  try {
    if (!socketServer) {
      return res.status(503).json({
        success: false,
        error: {
          message: 'Socket.io server not initialized'
        }
      });
    }

    const stats = socketServer.getServerStats();

    logger.info('Socket.io metrics requested', {
      type: 'socket_metrics_request',
      requestedBy: req.user?.id || 'anonymous',
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting Socket.io metrics', {
      type: 'socket_api_error',
      error: error.message,
      stack: error.stack
    });

    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get Socket.io metrics'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/socket/health:
 *   get:
 *     summary: Get Socket.io health status
 *     description: Returns health status and basic diagnostics for Socket.io server
 *     tags: [Socket.io]
 *     responses:
 *       200:
 *         description: Health status retrieved successfully
 */
router.get('/health', (req, res) => {
  try {
    if (!socketServer) {
      return res.status(503).json({
        success: false,
        status: 'unhealthy',
        message: 'Socket.io server not initialized'
      });
    }

    const stats = socketServer.getServerStats();
    const isHealthy = stats.activeConnections >= 0; // Basic health check

    res.json({
      success: true,
      status: isHealthy ? 'healthy' : 'unhealthy',
      data: {
        activeConnections: stats.activeConnections,
        totalConnections: stats.totalConnections,
        uptime: stats.uptime,
        peakConnections: stats.peakConnections,
        memoryUsage: process.memoryUsage(),
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Error getting Socket.io health', {
      type: 'socket_health_error',
      error: error.message
    });

    res.status(500).json({
      success: false,
      status: 'unhealthy',
      error: {
        message: 'Failed to get health status'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/socket/logs:
 *   get:
 *     summary: Get recent Socket.io logs
 *     description: Returns recent Socket.io related log entries
 *     tags: [Socket.io]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: Number of log entries to return
 *       - in: query
 *         name: level
 *         schema:
 *           type: string
 *           enum: [error, warn, info, debug]
 *         description: Filter by log level
 *     responses:
 *       200:
 *         description: Logs retrieved successfully
 */
router.get('/logs', async (req, res) => {
  try {
    const { getRecentLogs } = require('../config/logger');
    const limit = parseInt(req.query.limit) || 100;
    const level = req.query.level || null;

    const logs = await getRecentLogs(limit, level);

    // Filter for Socket.io related logs
    const socketLogs = logs.filter(log =>
      log.type && (
        log.type.includes('socket') ||
        log.message.toLowerCase().includes('socket')
      )
    );

    res.json({
      success: true,
      data: {
        logs: socketLogs,
        count: socketLogs.length,
        totalLogs: logs.length,
        filters: { limit, level }
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting Socket.io logs', {
      type: 'socket_logs_error',
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get Socket.io logs'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/socket/memory:
 *   get:
 *     summary: Get Socket.io memory statistics
 *     description: Returns detailed memory usage and potential leak information
 *     tags: [Socket.io]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Memory statistics retrieved successfully
 */
router.get('/memory', (req, res) => {
  try {
    if (!socketServer) {
      return res.status(503).json({
        success: false,
        error: {
          message: 'Socket.io server not initialized'
        }
      });
    }

    const memoryStats = socketServer.getMemoryStats();
    const processMemory = process.memoryUsage();

    // Calculate memory efficiency metrics
    const efficiency = {
      connectionsPerMB: memoryStats.maps.connectedUsers / memoryStats.heapUsed,
      eventsPerMB: socketServer.metrics.totalEvents / memoryStats.heapUsed,
      memoryGrowthRate: memoryStats.heapUsed / (Date.now() - socketServer.metrics.startTime) * 1000 * 60 * 60 // MB per hour
    };

    // Memory health assessment
    let healthStatus = 'healthy';
    const warnings = [];

    if (memoryStats.heapUsed > 500) {
      healthStatus = 'warning';
      warnings.push('High heap usage detected (>500MB)');
    }

    if (memoryStats.maps.connectionAttempts > 1000) {
      healthStatus = 'warning';
      warnings.push('Large number of connection attempts tracked');
    }

    if (memoryStats.maps.eventsByType > 500) {
      healthStatus = 'warning';
      warnings.push('Large number of event types tracked');
    }

    res.json({
      success: true,
      data: {
        memory: memoryStats,
        process: {
          heapUsed: Math.round(processMemory.heapUsed / 1024 / 1024),
          heapTotal: Math.round(processMemory.heapTotal / 1024 / 1024),
          rss: Math.round(processMemory.rss / 1024 / 1024),
          external: Math.round(processMemory.external / 1024 / 1024),
          arrayBuffers: Math.round(processMemory.arrayBuffers / 1024 / 1024)
        },
        efficiency,
        health: {
          status: healthStatus,
          warnings,
          uptime: Date.now() - socketServer.metrics.startTime,
          lastCleanup: 'Available in logs'
        }
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting Socket.io memory stats', {
      type: 'socket_memory_error',
      error: error.message,
      stack: error.stack
    });

    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get Socket.io memory statistics'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/socket/cleanup:
 *   post:
 *     summary: Force memory cleanup
 *     description: Manually trigger memory cleanup and garbage collection
 *     tags: [Socket.io]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Cleanup completed successfully
 */
router.post('/cleanup', (req, res) => {
  try {
    if (!socketServer) {
      return res.status(503).json({
        success: false,
        error: {
          message: 'Socket.io server not initialized'
        }
      });
    }

    const beforeMemory = socketServer.getMemoryStats();

    // Force cleanup
    socketServer.cleanupOldData();
    socketServer.aggressiveCleanup();

    const afterMemory = socketServer.getMemoryStats();

    logger.info('Manual memory cleanup triggered', {
      type: 'manual_cleanup',
      beforeMemory,
      afterMemory,
      triggeredBy: req.user?.id || 'anonymous',
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'Memory cleanup completed',
      data: {
        before: beforeMemory,
        after: afterMemory,
        improvement: {
          heapReduced: beforeMemory.heapUsed - afterMemory.heapUsed,
          mapsReduced: Object.keys(beforeMemory.maps).reduce((total, key) => {
            return total + (beforeMemory.maps[key] - afterMemory.maps[key]);
          }, 0)
        }
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error during manual cleanup', {
      type: 'cleanup_error',
      error: error.message,
      stack: error.stack
    });

    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to perform memory cleanup'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/socket/logs/status:
 *   get:
 *     summary: Get database logging circuit breaker status
 *     tags: [Socket.io]
 *     responses:
 *       200:
 *         description: Circuit breaker status and logging statistics
 */
router.get('/logs/status', (req, res) => {
  try {
    const { getLoggingStats } = require('../config/logger');
    const stats = getLoggingStats();

    res.json({
      success: true,
      message: 'Logging system status retrieved',
      data: {
        ...stats,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error getting logging status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get logging status',
      error: error.message
    });
  }
});

module.exports = { router, initializeSocketRoutes };

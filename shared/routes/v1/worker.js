const express = require('express');
const { authenticateToken, requireAdmin } = require('../../middleware/auth');
const { BackgroundJob, SystemStats, HealthCheck } = require('../../models');

const router = express.Router();

// Apply authentication to all worker routes
router.use(authenticateToken);
// Temporarily disable admin requirement for testing
// router.use(requireAdmin);

/**
 * @swagger
 * /api/v1/worker/stats/summary:
 *   get:
 *     summary: Get overall system statistics summary
 *     tags: [Worker System v1]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: System statistics retrieved successfully
 */
router.get('/stats/summary', async (req, res) => {
  try {
    // Try to get cached stats first
    let userStats = await SystemStats.getCachedStats('user-stats');
    let logStats = await SystemStats.getCachedStats('log-stats');
    let connectionStats = await SystemStats.getCachedStats('connection-stats');

    // Calculate fresh stats if not cached
    if (!userStats) {
      userStats = await SystemStats.calculateUserStats();
    }
    if (!logStats) {
      logStats = await SystemStats.calculateLogStats();
    }
    if (!connectionStats) {
      connectionStats = await SystemStats.calculateConnectionStats();
    }

    // Get job queue stats
    const jobStats = await BackgroundJob.getStats();

    // Get health overview
    const healthOverview = await HealthCheck.getOverallHealth();

    res.json({
      success: true,
      data: {
        users: userStats,
        logs: logStats,
        connections: connectionStats,
        jobs: jobStats,
        health: healthOverview,
        lastUpdated: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Error fetching system stats summary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system statistics',
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/v1/worker/stats/users:
 *   get:
 *     summary: Get detailed user statistics
 *     tags: [Worker System v1]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User statistics retrieved successfully
 */
router.get('/stats/users', async (req, res) => {
  try {
    const userStats = await SystemStats.calculateUserStats();
    
    res.json({
      success: true,
      data: userStats,
    });
  } catch (error) {
    console.error('Error fetching user stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user statistics',
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/v1/worker/stats/logs:
 *   get:
 *     summary: Get detailed log statistics
 *     tags: [Worker System v1]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Log statistics retrieved successfully
 */
router.get('/stats/logs', async (req, res) => {
  try {
    const logStats = await SystemStats.calculateLogStats();
    
    res.json({
      success: true,
      data: logStats,
    });
  } catch (error) {
    console.error('Error fetching log stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch log statistics',
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/v1/worker/stats/connections:
 *   get:
 *     summary: Get connection statistics
 *     tags: [Worker System v1]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Connection statistics retrieved successfully
 */
router.get('/stats/connections', async (req, res) => {
  try {
    const connectionStats = await SystemStats.calculateConnectionStats();
    
    res.json({
      success: true,
      data: connectionStats,
    });
  } catch (error) {
    console.error('Error fetching connection stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch connection statistics',
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/v1/worker/health:
 *   get:
 *     summary: Get overall system health status
 *     tags: [Worker System v1]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Health status retrieved successfully
 */
router.get('/health', async (req, res) => {
  try {
    const healthOverview = await HealthCheck.getOverallHealth();
    
    res.json({
      success: true,
      data: healthOverview,
    });
  } catch (error) {
    console.error('Error fetching health status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch health status',
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/v1/worker/health/check:
 *   post:
 *     summary: Run health checks manually
 *     tags: [Worker System v1]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Health checks completed successfully
 */
router.post('/health/check', async (req, res) => {
  try {
    // Run all health checks
    const [databaseHealth, memoryHealth, socketHealth] = await Promise.all([
      HealthCheck.checkDatabaseHealth(),
      HealthCheck.checkMemoryUsage(),
      HealthCheck.checkSocketServer(),
    ]);

    const results = {
      database: databaseHealth,
      memory: memoryHealth,
      socket: socketHealth,
    };

    res.json({
      success: true,
      message: 'Health checks completed',
      data: results,
    });
  } catch (error) {
    console.error('Error running health checks:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to run health checks',
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/v1/worker/jobs:
 *   get:
 *     summary: Get background job queue status
 *     tags: [Worker System v1]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Job queue status retrieved successfully
 */
router.get('/jobs', async (req, res) => {
  try {
    const { status, limit = 50 } = req.query;
    
    const whereClause = {};
    if (status) {
      whereClause.status = status;
    }

    const jobs = await BackgroundJob.findAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
      limit: Math.min(parseInt(limit), 100),
    });

    const stats = await BackgroundJob.getStats();

    res.json({
      success: true,
      data: {
        jobs,
        stats,
        total: jobs.length,
      },
    });
  } catch (error) {
    console.error('Error fetching jobs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch jobs',
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/v1/worker/jobs:
 *   post:
 *     summary: Enqueue a new background job
 *     tags: [Worker System v1]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               jobType:
 *                 type: string
 *               jobData:
 *                 type: object
 *               priority:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Job enqueued successfully
 */
router.post('/jobs', async (req, res) => {
  try {
    const { jobType, jobData = {}, priority = 0 } = req.body;

    if (!jobType) {
      return res.status(400).json({
        success: false,
        message: 'Job type is required',
      });
    }

    const job = await BackgroundJob.enqueue(jobType, jobData, { priority });

    res.json({
      success: true,
      message: 'Job enqueued successfully',
      data: job,
    });
  } catch (error) {
    console.error('Error enqueuing job:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to enqueue job',
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/v1/worker/jobs/{id}:
 *   delete:
 *     summary: Cancel a background job
 *     tags: [Worker System v1]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Job cancelled successfully
 */
router.delete('/jobs/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const job = await BackgroundJob.findByPk(id);
    if (!job) {
      return res.status(404).json({
        success: false,
        message: 'Job not found',
      });
    }

    if (job.status === 'processing') {
      return res.status(400).json({
        success: false,
        message: 'Cannot cancel job that is currently processing',
      });
    }

    await job.update({ status: 'failed', error_message: 'Cancelled by admin' });

    res.json({
      success: true,
      message: 'Job cancelled successfully',
    });
  } catch (error) {
    console.error('Error cancelling job:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel job',
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/v1/worker/config:
 *   get:
 *     summary: Get worker configuration
 *     tags: [Worker System v1]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Worker configuration retrieved successfully
 */
router.get('/config', async (req, res) => {
  try {
    const config = {
      maxConcurrentJobs: parseInt(process.env.WORKER_MAX_CONCURRENT_JOBS) || 5,
      pollInterval: parseInt(process.env.WORKER_POLL_INTERVAL) || 5000,
      maxRetries: parseInt(process.env.WORKER_MAX_RETRIES) || 3,
      retryDelay: parseInt(process.env.WORKER_RETRY_DELAY) || 30000,
      environment: process.env.NODE_ENV || 'development',
      timezone: process.env.TZ || 'UTC',
      debugMode: process.env.DEBUG === 'true',
      socketDebug: process.env.SOCKET_DEBUG === 'true',
    };

    res.json({
      success: true,
      data: config,
    });
  } catch (error) {
    console.error('Error fetching worker config:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch worker configuration',
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/v1/worker/logs:
 *   get:
 *     summary: Get worker logs
 *     tags: [Worker System v1]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *       - in: query
 *         name: level
 *         schema:
 *           type: string
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Worker logs retrieved successfully
 */
router.get('/logs', async (req, res) => {
  try {
    const { limit = 100, level, type } = req.query;
    const { Log } = require('../../models');
    const { Op } = require('sequelize');

    const whereClause = {
      type: {
        [Op.in]: ['worker', 'background_job', 'job_scheduler', 'health_check', 'stats_calculation']
      }
    };

    if (level) {
      whereClause.level = level;
    }

    if (type) {
      whereClause.type = type;
    }

    const logs = await Log.findAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
      limit: Math.min(parseInt(limit), 500),
      attributes: ['id', 'level', 'message', 'metadata', 'type', 'created_at']
    });

    const transformedLogs = logs.map(log => ({
      id: log.id,
      level: log.level,
      message: log.message,
      timestamp: log.created_at,
      type: log.type,
      metadata: log.metadata,
    }));

    res.json({
      success: true,
      data: {
        logs: transformedLogs,
        count: transformedLogs.length,
      },
    });
  } catch (error) {
    console.error('Error fetching worker logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch worker logs',
      error: error.message,
    });
  }
});

module.exports = router;

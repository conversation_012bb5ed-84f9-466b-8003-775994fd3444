const express = require('express');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const { User, RefreshToken, EmailVerification } = require('../../models');
const { logAuth, logSecurity } = require('../../config/logger');
const { authenticateToken } = require('../../middleware/auth');
const emailQueueService = require('../../services/emailQueueService');
const authService = require('../../services/authService');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     AuthResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         data:
 *           type: object
 *           properties:
 *             user:
 *               $ref: '#/components/schemas/User'
 *             token:
 *               type: string
 *               example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *             refreshToken:
 *               type: string
 *               example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *         message:
 *           type: string
 *           example: "Login successful"
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           example: 1
 *         name:
 *           type: string
 *           example: "John Doe"
 *         email:
 *           type: string
 *           example: "<EMAIL>"
 *         role:
 *           type: string
 *           enum: [user, admin, client]
 *           example: "client"
 *         is_active:
 *           type: boolean
 *           example: true
 *         email_verified:
 *           type: boolean
 *           example: false
 *         created_at:
 *           type: string
 *           format: date-time
 *           example: "2023-01-01T00:00:00.000Z"
 *     Error:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: false
 *         error:
 *           type: object
 *           properties:
 *             message:
 *               type: string
 *               example: "Error message"
 *             details:
 *               type: array
 *               items:
 *                 type: object
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 */

// Helper functions
const generateToken = (payload) => {
  return jwt.sign(payload, process.env.JWT_SECRET || 'fallback_secret', {
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  });
};

const generateRefreshToken = (payload) => {
  return jwt.sign(payload, process.env.JWT_REFRESH_SECRET || 'fallback_refresh_secret', {
    expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
  });
};

/**
 * @swagger
 * /api/v1/auth/register:
 *   post:
 *     summary: Register a new user (v1)
 *     tags: [Authentication v1]
 *     parameters:
 *       - in: header
 *         name: Accept
 *         schema:
 *           type: string
 *           example: application/vnd.hlenergy.v1+json
 *         description: API version header (optional)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - password
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 2
 *                 example: "John Doe"
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 minLength: 6
 *                 example: "password123"
 *     responses:
 *       201:
 *         description: User registered successfully
 *         headers:
 *           API-Version:
 *             schema:
 *               type: string
 *             description: Current API version
 *           API-Latest-Version:
 *             schema:
 *               type: string
 *             description: Latest available API version
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/AuthResponse'
 *                 - type: object
 *                   properties:
 *                     api_version:
 *                       type: string
 *                       example: "v1"
 *       400:
 *         description: Validation error or user already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/register', [
  body('name').trim().isLength({ min: 2 }).withMessage('Name must be at least 2 characters'),
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
        api_version: req.apiVersion,
      });
    }

    const { name, email, password } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: { message: 'User with this email already exists' },
        api_version: req.apiVersion,
      });
    }

    // Create user (password will be hashed automatically by the model)
    const user = await User.create({
      name,
      email,
      password,
      role: 'client',
    });

    // Generate tokens
    const tokenPayload = { id: user.id, email: user.email, role: user.role };
    const token = generateToken(tokenPayload);
    const refreshTokenValue = generateRefreshToken(tokenPayload);

    // Store refresh token in database
    await RefreshToken.createToken(
      user.id,
      refreshTokenValue,
      30, // 30 days
      req.ip,
      req.get('User-Agent')
    );

    // Create email verification token and queue verification email
    let emailVerificationQueued = false;
    let emailId = null;

    try {
      const verification = await EmailVerification.createVerificationToken(user.id, user.email);
      const emailResult = await emailQueueService.queueVerificationEmail(user, verification.token, {
        ip_address: req.ip,
        user_agent: req.get('User-Agent'),
      });
      emailVerificationQueued = emailResult.success;
      emailId = emailResult.emailId;
    } catch (emailError) {
      console.warn('Failed to queue verification email:', emailError.message);
      // Don't fail registration if email fails
    }

    // User response (password is automatically excluded by toJSON method)
    const userResponse = user.toJSON();

    // Log successful registration
    logAuth('user_registered', user.id, {
      email: user.email,
      role: user.role,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      emailVerificationQueued,
      emailId,
    });

    res.status(201).json({
      success: true,
      data: {
        user: userResponse,
        token,
        refreshToken: refreshTokenValue,
        emailVerification: {
          queued: emailVerificationQueued,
          emailId: emailId,
          required: true,
        },
      },
      message: emailVerificationQueued
        ? 'User registered successfully. Verification email has been queued for delivery.'
        : 'User registered successfully. Email verification will be sent shortly.',
      api_version: req.apiVersion,
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' },
      api_version: req.apiVersion,
    });
  }
});

/**
 * @swagger
 * /api/v1/auth/login:
 *   post:
 *     summary: Login user (v1)
 *     tags: [Authentication v1]
 *     parameters:
 *       - in: header
 *         name: Accept
 *         schema:
 *           type: string
 *           example: application/vnd.hlenergy.v1+json
 *         description: API version header (optional)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 example: "password123"
 *     responses:
 *       200:
 *         description: Login successful
 *         headers:
 *           API-Version:
 *             schema:
 *               type: string
 *             description: Current API version
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/AuthResponse'
 *                 - type: object
 *                   properties:
 *                     api_version:
 *                       type: string
 *                       example: "v1"
 *       401:
 *         description: Invalid credentials
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/login', [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('password').notEmpty().withMessage('Password is required'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
        api_version: req.apiVersion,
      });
    }

    const { email, password } = req.body;

    try {
      // Use authService for authentication with session management
      const authResult = await authService.authenticateUser(
        email,
        password,
        req.ip,
        req.get('User-Agent')
      );

      // Log successful login
      logAuth('user_login', authResult.user.id, {
        email: authResult.user.email,
        role: authResult.user.role,
        sessionId: authResult.sessionId,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      res.json({
        success: true,
        data: {
          user: authResult.user,
          accessToken: authResult.accessToken,
          refreshToken: authResult.refreshToken,
          expiresIn: authResult.expiresIn,
          sessionId: authResult.sessionId,
          sessionToken: authResult.sessionToken,
          authMethod: authResult.authMethod,
        },
        message: 'Login successful',
        api_version: req.apiVersion,
      });
    } catch (authError) {
      // Handle authentication errors
      logSecurity('Failed Login Attempt', {
        email: email,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        error: authError.message,
      });

      return res.status(401).json({
        success: false,
        error: { message: 'Invalid email or password' },
        api_version: req.apiVersion,
      });
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' },
      api_version: req.apiVersion,
    });
  }
});

/**
 * @swagger
 * /api/v1/auth/refresh:
 *   post:
 *     summary: Refresh access token (v1)
 *     tags: [Authentication v1]
 *     parameters:
 *       - in: header
 *         name: Accept
 *         schema:
 *           type: string
 *           example: application/vnd.hlenergy.v1+json
 *         description: API version header (optional)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *                 example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     token:
 *                       type: string
 *                     refreshToken:
 *                       type: string
 *                 message:
 *                   type: string
 *                 api_version:
 *                   type: string
 *       401:
 *         description: Invalid or expired refresh token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        error: { message: 'Refresh token is required' },
        api_version: req.apiVersion,
      });
    }

    // Find and validate refresh token in database
    const tokenRecord = await RefreshToken.findValidToken(refreshToken);

    if (!tokenRecord) {
      return res.status(401).json({
        success: false,
        error: { message: 'Invalid or expired refresh token' },
        api_version: req.apiVersion,
      });
    }

    const user = tokenRecord.user;
    if (!user || !user.is_active) {
      return res.status(401).json({
        success: false,
        error: { message: 'User not found or inactive' },
        api_version: req.apiVersion,
      });
    }

    // Generate new tokens
    const tokenPayload = { id: user.id, email: user.email, role: user.role };
    const newToken = generateToken(tokenPayload);
    const newRefreshToken = generateRefreshToken(tokenPayload);

    // Revoke old refresh token and create new one
    await tokenRecord.revoke();
    await RefreshToken.createToken(
      user.id,
      newRefreshToken,
      30, // 30 days
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      success: true,
      data: {
        token: newToken,
        refreshToken: newRefreshToken,
      },
      message: 'Token refreshed successfully',
      api_version: req.apiVersion,
    });
  } catch (error) {
    console.error('Refresh token error:', error);
    res.status(401).json({
      success: false,
      error: { message: 'Invalid refresh token' },
      api_version: req.apiVersion,
    });
  }
});

/**
 * @swagger
 * /api/v1/auth/logout:
 *   post:
 *     summary: Logout user (v1)
 *     tags: [Authentication v1]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: header
 *         name: Accept
 *         schema:
 *           type: string
 *           example: application/vnd.hlenergy.v1+json
 *         description: API version header (optional)
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               refreshToken:
 *                 type: string
 *                 description: Optional refresh token to revoke
 *                 example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *               revokeAllSessions:
 *                 type: boolean
 *                 description: Whether to revoke all user sessions
 *                 default: false
 *     responses:
 *       200:
 *         description: Logout successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Logout successful"
 *                 data:
 *                   type: object
 *                   properties:
 *                     tokensRevoked:
 *                       type: integer
 *                       description: Number of tokens revoked
 *                       example: 1
 *                 api_version:
 *                   type: string
 *                   example: "v1"
 *       401:
 *         description: Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/logout', async (req, res) => {
  try {
    const { refreshToken, revokeAllSessions = false } = req.body;

    // Try to get user from token, but don't require it for logout
    let userId = null;
    let userFromToken = null;

    // Attempt to authenticate token if provided, but don't fail if invalid
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      try {
        const jwt = require('jsonwebtoken');
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        userId = decoded.userId;
        userFromToken = decoded;
      } catch (tokenError) {
        console.log('Token verification failed during logout (this is OK):', tokenError.message);
        // Continue with logout even if token is invalid
      }
    }

    let tokensRevoked = 0;

    if (userId && revokeAllSessions) {
      // Revoke all refresh tokens for this user
      const revokedCount = await RefreshToken.revokeAllUserTokens(userId);
      tokensRevoked = revokedCount;

      logAuth('user_logout_all_sessions', userId, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        tokensRevoked: revokedCount,
      });
    } else if (refreshToken) {
      // Revoke specific refresh token
      const tokenRecord = await RefreshToken.findValidToken(refreshToken);
      if (tokenRecord && (!userId || tokenRecord.user_id === userId)) {
        await tokenRecord.revoke();
        tokensRevoked = 1;
        // Update userId from token record if we didn't have it
        if (!userId) {
          userId = tokenRecord.user_id;
        }
      }

      if (userId) {
        logAuth('user_logout', userId, {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          refreshTokenProvided: !!refreshToken,
          tokensRevoked,
        });
      }
    } else {
      // Just log the logout if we have a userId (access token will expire naturally)
      if (userId) {
        logAuth('user_logout', userId, {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          refreshTokenProvided: false,
          tokensRevoked: 0,
        });
      }
    }

    res.json({
      success: true,
      message: revokeAllSessions
        ? 'Logged out from all sessions successfully'
        : 'Logout successful',
      data: {
        tokensRevoked,
        userId: userId || null,
      },
      api_version: req.apiVersion,
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error during logout' },
      api_version: req.apiVersion,
    });
  }
});

module.exports = router;

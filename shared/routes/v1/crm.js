const express = require('express');
const router = express.Router();
const { authenticateToken, requireAdmin } = require('../../middleware/auth');
const { Customer, Project, Communication, Document, User } = require('../../models');
const rateLimit = require('express-rate-limit');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;

// Enhanced rate limiting for CRM endpoints
const crmRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Higher limit for CRM operations
  message: {
    error: 'Too many CRM requests from this IP, please try again later.',
    type: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Specific rate limits for different operations
const readOperationsLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 200, // 200 requests per minute for read operations
  skip: (req) => req.method !== 'GET'
});

const writeOperationsLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 50, // 50 requests per minute for write operations
  skip: (req) => req.method === 'GET'
});

// Apply rate limiting
router.use(crmRateLimit);
router.use(readOperationsLimit);
router.use(writeOperationsLimit);

// Apply authentication to all CRM routes
router.use(authenticateToken);

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/crm');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|xls|xlsx|txt/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Invalid file type'));
    }
  }
});

// Helper function for pagination
const getPagination = (page, limit) => {
  const pageNum = parseInt(page) || 1;
  const limitNum = parseInt(limit) || 10;
  const offset = (pageNum - 1) * limitNum;
  return { limit: limitNum, offset, page: pageNum };
};

// Helper function for building WHERE clauses
const buildWhereClause = (filters, baseWhere = '1=1') => {
  let whereClause = baseWhere;
  const values = [];
  let paramCount = 0;

  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      paramCount++;
      if (key === 'search') {
        whereClause += ` AND (name ILIKE $${paramCount} OR email ILIKE $${paramCount})`;
        values.push(`%${value}%`);
      } else {
        whereClause += ` AND ${key} = $${paramCount}`;
        values.push(value);
      }
    }
  });

  return { whereClause, values };
};

/**
 * @swagger
 * components:
 *   schemas:
 *     Customer:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         email:
 *           type: string
 *         phone:
 *           type: string
 *         industry:
 *           type: string
 *         priority:
 *           type: string
 *           enum: [low, medium, high]
 *         status:
 *           type: string
 *           enum: [prospect, active, inactive, converted]
 *         lead_source:
 *           type: string
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api/v1/crm/customers:
 *   get:
 *     summary: Get customers with pagination and filtering
 *     tags: [CRM - Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *       - in: query
 *         name: leadSource
 *         schema:
 *           type: string
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: List of customers
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Customer'
 *                     pagination:
 *                       type: object
 */
router.get('/customers', async (req, res) => {
  try {
    const { page, limit, status, priority, leadSource, search } = req.query;
    const { limit: limitNum, offset, page: pageNum } = getPagination(page, limit);

    // Build where clause for Sequelize
    const { Op } = require('sequelize');
    const whereClause = {};

    if (status) whereClause.status = status;
    if (priority) whereClause.priority = priority;
    if (leadSource) whereClause.leadSource = leadSource;
    if (search) {
      whereClause[Op.or] = [
        { firstName: { [Op.like]: `%${search}%` } },
        { lastName: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } },
        { companyName: { [Op.like]: `%${search}%` } }
      ];
    }

    // Get customers with pagination
    const { count, rows } = await Customer.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'assignedUser',
          attributes: ['id', 'name', 'email'],
          required: false
        }
      ],
      limit: limitNum,
      offset,
      order: [['createdAt', 'DESC']],
      distinct: true
    });

    const pagination = {
      page: pageNum,
      limit: limitNum,
      total: count,
      totalPages: Math.ceil(count / limitNum)
    };

    res.json({
      success: true,
      data: {
        items: rows,
        pagination
      }
    });

  } catch (error) {
    console.error('Error fetching customers:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch customers',
        type: 'DATABASE_ERROR'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/crm/customers:
 *   post:
 *     summary: Create a new customer
 *     tags: [CRM - Customers]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *             properties:
 *               name:
 *                 type: string
 *               email:
 *                 type: string
 *               phone:
 *                 type: string
 *               industry:
 *                 type: string
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high]
 *               status:
 *                 type: string
 *                 enum: [prospect, active, inactive, converted]
 *               lead_source:
 *                 type: string
 *     responses:
 *       201:
 *         description: Customer created successfully
 *       400:
 *         description: Invalid input data
 *       409:
 *         description: Customer with email already exists
 */
router.post('/customers', async (req, res) => {
  try {
    const {
      firstName,
      lastName,
      email,
      phone,
      companyName,
      jobTitle,
      industry,
      priority = 'medium',
      status = 'lead',
      leadSource = 'website',
      companySize,
      address,
      city,
      state,
      zipCode,
      country = 'Portugal',
      notes,
      energyNeeds,
      currentEnergyProvider,
      monthlyEnergyBudget,
      estimatedValue
    } = req.body;

    // Validate required fields
    if (!firstName || !lastName || !email) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'First name, last name, and email are required',
          type: 'VALIDATION_ERROR'
        }
      });
    }

    // Check if customer with email already exists
    const existingCustomer = await Customer.findOne({ where: { email } });

    if (existingCustomer) {
      return res.status(409).json({
        success: false,
        error: {
          message: 'Customer with this email already exists',
          type: 'DUPLICATE_EMAIL'
        }
      });
    }

    // Create new customer
    const customer = await Customer.create({
      firstName,
      lastName,
      email,
      phone,
      companyName,
      jobTitle,
      industry,
      priority,
      status,
      leadSource,
      companySize,
      address,
      city,
      state,
      zipCode,
      country,
      notes,
      energyNeeds,
      currentEnergyProvider,
      monthlyEnergyBudget,
      estimatedValue,
      assignedTo: req.user.id,
      firstContactDate: new Date()
    });

    res.status(201).json({
      success: true,
      data: customer,
      message: 'Customer created successfully'
    });

  } catch (error) {
    console.error('Error creating customer:', error);

    // Handle Sequelize validation errors
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation error',
          type: 'VALIDATION_ERROR',
          details: error.errors.map(err => ({
            field: err.path,
            message: err.message
          }))
        }
      });
    }

    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to create customer',
        type: 'DATABASE_ERROR'
      }
    });
  }
});

// ==================== PROJECTS ENDPOINTS ====================

/**
 * @swagger
 * /api/v1/crm/projects:
 *   get:
 *     summary: Get projects with pagination and filtering
 *     tags: [CRM - Projects]
 *     security:
 *       - bearerAuth: []
 */
router.get('/projects', async (req, res) => {
  try {
    const { page, limit, status, priority, customerId, search, type } = req.query;
    const { limit: limitNum, offset, page: pageNum } = getPagination(page, limit);

    // Build where clause for Sequelize
    const { Op } = require('sequelize');
    const whereClause = {};

    if (status) whereClause.status = status;
    if (priority) whereClause.priority = priority;
    if (type) whereClause.type = type;
    if (customerId) whereClause.customerId = customerId;
    if (search) {
      whereClause[Op.or] = [
        { title: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    // Get projects with pagination
    const { count, rows } = await Project.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'firstName', 'lastName', 'email', 'companyName'],
          required: false
        },
        {
          model: User,
          as: 'assignedUser',
          attributes: ['id', 'name', 'email'],
          required: false
        }
      ],
      limit: limitNum,
      offset,
      order: [['createdAt', 'DESC']],
      distinct: true
    });

    const pagination = {
      page: pageNum,
      limit: limitNum,
      total: count,
      totalPages: Math.ceil(count / limitNum)
    };

    res.json({
      success: true,
      data: {
        items: rows,
        pagination
      }
    });

  } catch (error) {
    console.error('Error fetching projects:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch projects',
        type: 'DATABASE_ERROR'
      }
    });
  }
});

// ==================== DASHBOARD ENDPOINTS ====================

/**
 * @swagger
 * /api/v1/crm/dashboard:
 *   get:
 *     summary: Get CRM dashboard data
 *     tags: [CRM - Dashboard]
 *     security:
 *       - bearerAuth: []
 */
router.get('/dashboard', async (req, res) => {
  try {
    const { Op } = require('sequelize');
    const { sequelize } = require('../../config/database');

    // Get dashboard statistics using Sequelize
    const [
      totalCustomers,
      activeCustomers,
      activeProjects,
      completedProjects,
      overdueProjects,
      highPriorityLeads
    ] = await Promise.all([
      Customer.count(),
      Customer.count({ where: { status: 'customer' } }),
      Project.count({ where: { status: 'in_progress' } }),
      Project.count({ where: { status: 'completed' } }),
      Project.count({
        where: {
          endDate: { [Op.lt]: new Date() },
          status: { [Op.notIn]: ['completed', 'cancelled', 'archived'] }
        }
      }),
      Customer.count({ where: { priority: 'high' } })
    ]);

    // Get recent activity (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentCustomers = await Customer.findAll({
      where: { createdAt: { [Op.gte]: sevenDaysAgo } },
      attributes: ['id', 'firstName', 'lastName', 'createdAt'],
      order: [['createdAt', 'DESC']],
      limit: 5
    });

    const recentProjects = await Project.findAll({
      where: { updatedAt: { [Op.gte]: sevenDaysAgo } },
      attributes: ['id', 'title', 'updatedAt'],
      order: [['updatedAt', 'DESC']],
      limit: 5
    });

    // Format recent activity
    const recentActivity = [
      ...recentCustomers.map(customer => ({
        type: 'customer',
        description: `New customer: ${customer.firstName} ${customer.lastName}`,
        timestamp: customer.createdAt,
        user: 'System'
      })),
      ...recentProjects.map(project => ({
        type: 'project',
        description: `Project updated: ${project.title}`,
        timestamp: project.updatedAt,
        user: 'System'
      }))
    ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)).slice(0, 10);

    // Get lead sources
    const leadSources = await Customer.findAll({
      attributes: [
        'leadSource',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['leadSource'],
      order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']]
    });

    const totalLeads = await Customer.count();
    const leadSourcesWithPercentage = leadSources.map(source => ({
      source: source.leadSource || 'Unknown',
      count: parseInt(source.dataValues.count),
      percentage: totalLeads > 0 ? Math.round((parseInt(source.dataValues.count) / totalLeads) * 100 * 10) / 10 : 0
    }));

    const dashboardData = {
      stats: {
        totalCustomers,
        activeCustomers,
        activeProjects,
        completedProjects,
        overdueProjects,
        highPriorityLeads,
        unreadCommunications: 0 // Placeholder since Communication model might not exist yet
      },
      recentActivity,
      leadSources: leadSourcesWithPercentage
    };

    res.json({
      success: true,
      data: dashboardData
    });

  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch dashboard data',
        type: 'DATABASE_ERROR'
      }
    });
  }
});

// ==================== COMMUNICATIONS ENDPOINTS ====================

/**
 * @swagger
 * /api/v1/crm/communications:
 *   get:
 *     summary: Get communications with pagination and filtering
 *     tags: [CRM - Communications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *       - in: query
 *         name: customerId
 *         schema:
 *           type: integer
 *       - in: query
 *         name: projectId
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: List of communications
 */
router.get('/communications', async (req, res) => {
  try {
    const { page, limit, status, type, customerId, projectId } = req.query;
    const { limit: limitNum, offset, page: pageNum } = getPagination(page, limit);

    // Build where clause for Sequelize
    const { Op } = require('sequelize');
    const whereClause = {};

    if (status) whereClause.status = status;
    if (type) whereClause.type = type;
    if (customerId) whereClause.customerId = customerId;
    if (projectId) whereClause.projectId = projectId;

    // Check if Communication model exists, if not return empty data
    if (!Communication) {
      console.log('Communication model not available, returning empty data');
      return res.json({
        success: true,
        data: {
          items: [],
          pagination: {
            page: pageNum,
            limit: limitNum,
            total: 0,
            totalPages: 0
          }
        }
      });
    }

    // Get communications with pagination
    const { count, rows } = await Communication.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        },
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'title'],
          required: false
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email'],
          required: false
        }
      ],
      limit: limitNum,
      offset,
      order: [['createdAt', 'DESC']],
      distinct: true
    });

    const pagination = {
      page: pageNum,
      limit: limitNum,
      total: count,
      totalPages: Math.ceil(count / limitNum)
    };

    res.json({
      success: true,
      data: {
        items: rows,
        pagination
      }
    });

  } catch (error) {
    console.error('Error fetching communications:', error);

    // If it's a model error (Communication doesn't exist), return empty data
    if (error.message && error.message.includes('Communication')) {
      console.log('Communication model error, returning empty data');
      return res.json({
        success: true,
        data: {
          items: [],
          pagination: {
            page: parseInt(req.query.page) || 1,
            limit: parseInt(req.query.limit) || 10,
            total: 0,
            totalPages: 0
          }
        }
      });
    }

    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch communications',
        type: 'DATABASE_ERROR'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/crm/communications:
 *   post:
 *     summary: Create a new communication
 *     tags: [CRM - Communications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customerId
 *               - type
 *               - subject
 *             properties:
 *               customerId:
 *                 type: integer
 *               projectId:
 *                 type: integer
 *               type:
 *                 type: string
 *                 enum: [email, phone, meeting, note]
 *               subject:
 *                 type: string
 *               content:
 *                 type: string
 *               direction:
 *                 type: string
 *                 enum: [inbound, outbound]
 *               status:
 *                 type: string
 *                 enum: [unread, read, replied]
 *     responses:
 *       201:
 *         description: Communication created successfully
 */
router.post('/communications', async (req, res) => {
  try {
    const {
      customerId,
      projectId,
      type,
      subject,
      content,
      direction = 'outbound',
      status = 'unread'
    } = req.body;

    // Validate required fields
    if (!customerId || !type || !subject) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Customer ID, type, and subject are required',
          type: 'VALIDATION_ERROR'
        }
      });
    }

    // Check if Communication model exists
    if (!Communication) {
      return res.status(501).json({
        success: false,
        error: {
          message: 'Communication feature not yet implemented',
          type: 'FEATURE_NOT_AVAILABLE'
        }
      });
    }

    // Check if customer exists
    const customerExists = await Customer.findByPk(customerId);
    if (!customerExists) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Customer not found',
          type: 'INVALID_CUSTOMER'
        }
      });
    }

    // Create new communication
    const communication = await Communication.create({
      customerId,
      projectId,
      type,
      subject,
      content,
      direction,
      status,
      userId: req.user.id
    });

    res.status(201).json({
      success: true,
      data: communication,
      message: 'Communication created successfully'
    });

  } catch (error) {
    console.error('Error creating communication:', error);

    // Handle Sequelize validation errors
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation error',
          type: 'VALIDATION_ERROR',
          details: error.errors.map(err => ({
            field: err.path,
            message: err.message
          }))
        }
      });
    }

    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to create communication',
        type: 'DATABASE_ERROR'
      }
    });
  }
});

module.exports = router;

const express = require('express');
const { body, validationResult } = require('express-validator');
const { ContactSubmission, ContactAction } = require('../../models');
const { authenticateToken, requireStaff, optionalAuth } = require('../../middleware/auth');
const PushNotificationService = require('../../services/pushNotificationService');
const leadService = require('../../services/leadService');
const emailQueueService = require('../../services/emailQueueService');
const recaptchaService = require('../../services/recaptchaService');

const router = express.Router();

/**
 * @swagger
 * /api/v1/contact/submit:
 *   post:
 *     summary: Submit a contact form (v1)
 *     tags: [Contact v1]
 *     parameters:
 *       - in: header
 *         name: Accept
 *         schema:
 *           type: string
 *           example: application/vnd.hlenergy.v1+json
 *         description: API version header (optional)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - message
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 2
 *                 example: "<PERSON>"
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               phone:
 *                 type: string
 *                 example: "+1234567890"
 *               message:
 *                 type: string
 *                 minLength: 10
 *                 example: "I'm interested in your energy consultation services."
 *               captchaToken:
 *                 type: string
 *                 description: "Google reCAPTCHA v3 token"
 *                 example: "03AGdBq25..."
 *     responses:
 *       201:
 *         description: Contact form submitted successfully
 *         headers:
 *           API-Version:
 *             schema:
 *               type: string
 *             description: Current API version
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   allOf:
 *                     - $ref: '#/components/schemas/ContactSubmission'
 *                     - type: object
 *                       properties:
 *                         api_version:
 *                           type: string
 *                           example: "v1"
 *                 message:
 *                   type: string
 *                 api_version:
 *                   type: string
 *       400:
 *         description: Validation error or reCAPTCHA verification failed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: object
 *                   properties:
 *                     message:
 *                       type: string
 *                       example: "reCAPTCHA verification failed"
 *                     details:
 *                       type: string
 *                       example: "Score too low: 0.1 (minimum: 0.5)"
 *                     code:
 *                       type: string
 *                       example: "RECAPTCHA_FAILED"
 *                 api_version:
 *                   type: string
 */
router.post('/submit', [
  optionalAuth, // Optional authentication to track user if logged in
  body('name').trim().isLength({ min: 2 }).withMessage('Name must be at least 2 characters'),
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('message').trim().isLength({ min: 10 }).withMessage('Message must be at least 10 characters'),
  body('phone').optional().trim().isLength({ min: 10 }).withMessage('Please provide a valid phone number'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
        api_version: req.apiVersion,
      });
    }

    const { name, email, phone, message, captchaToken } = req.body;

    // Verify reCAPTCHA token
    if (recaptchaService.isConfigured()) {
      console.log('🔐 Verifying reCAPTCHA token for contact form submission');

      const recaptchaResult = await recaptchaService.verifyContactForm(captchaToken, req.ip);

      if (!recaptchaResult.success) {
        console.warn('❌ reCAPTCHA verification failed:', recaptchaResult.error);
        return res.status(400).json({
          success: false,
          error: {
            message: 'reCAPTCHA verification failed',
            details: recaptchaResult.error,
            code: 'RECAPTCHA_FAILED'
          },
          api_version: req.apiVersion,
        });
      }

      console.log('✅ reCAPTCHA verification successful:', {
        score: recaptchaResult.score,
        action: recaptchaResult.action
      });
    } else {
      console.warn('⚠️ reCAPTCHA not configured, skipping verification');
    }

    // Create contact submission in database
    const submission = await ContactSubmission.create({
      name,
      email,
      phone: phone || null,
      message,
      status: 'new',
      priority: 'medium',
      source: 'website',
      ip_address: req.ip,
      user_agent: req.get('User-Agent'),
      created_by: req.user?.id || null, // Link to user if authenticated
      metadata: {
        api_version: req.apiVersion,
        timestamp: new Date().toISOString(),
        referrer: req.get('Referrer'),
        authenticated: !!req.user,
      },
    });

    // Create lead from contact submission
    let leadId = null;
    try {
      const lead = await leadService.createLeadFromContactSubmission(submission.id, {
        ip_address: req.ip,
        user_agent: req.get('User-Agent'),
        utm_source: req.query.utm_source,
        utm_medium: req.query.utm_medium,
        utm_campaign: req.query.utm_campaign,
        referrer_url: req.get('Referrer'),
        created_by: req.user?.id || null,
      });

      leadId = lead.id;
      console.log('✅ Lead created from contact submission:', lead.id);
    } catch (leadError) {
      // Don't fail the request if lead creation fails
      console.error('❌ Failed to create lead from contact submission:', leadError);
    }

    // Queue email notification to admin
    let emailQueued = false;
    let emailId = null;

    try {
      const adminEmail = process.env.CONTACT_NOTIFICATION_EMAIL || process.env.ADMIN_EMAIL || '<EMAIL>';

      const emailResult = await emailQueueService.queueContactNotificationEmail({
        submissionId: submission.id,
        name: submission.name,
        email: submission.email,
        phone: submission.phone,
        message: submission.message,
        source: submission.source || 'website',
        adminEmail,
        ip_address: req.ip,
        user_agent: req.get('User-Agent'),
      });

      if (emailResult.success) {
        emailQueued = true;
        emailId = emailResult.emailId;
        console.log('✅ Contact notification email queued:', emailResult.emailId);
      } else {
        console.error('❌ Failed to queue contact notification email:', emailResult.error);
      }
    } catch (emailError) {
      console.error('❌ Failed to queue contact notification email:', emailError);
      // Don't fail the contact submission if email queueing fails
    }

    // Send push notification to admin users about new lead
    try {
      // In a real application, you would get admin user IDs from the database
      // For demo purposes, we'll send to user ID 1 (assuming it's an admin)
      const adminUserId = '1'; // This should be dynamic based on your admin users

      await PushNotificationService.sendNewLeadNotification(adminUserId, {
        name: submission.name,
        service: 'General Inquiry', // You could extract this from the message or add a service field
        id: submission.id,
        leadId: leadId
      });

      console.log('✅ Push notification sent for new lead:', submission.id);
    } catch (notificationError) {
      // Don't fail the request if notification fails
      console.error('❌ Failed to send push notification for new lead:', notificationError);
    }

    res.status(201).json({
      success: true,
      submission: submission,
      emailQueued,
      emailId,
      message: 'Contact form submitted successfully. We will get back to you soon!',
      api_version: req.apiVersion,
    });
  } catch (error) {
    console.error('Contact submission error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' },
      api_version: req.apiVersion,
    });
  }
});

/**
 * @swagger
 * /api/v1/contact:
 *   get:
 *     summary: Get all contact submissions (Admin only) (v1)
 *     tags: [Contact v1]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: header
 *         name: Accept
 *         schema:
 *           type: string
 *           example: application/vnd.hlenergy.v1+json
 *         description: API version header (optional)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [new, in_progress, resolved]
 *         description: Filter by status
 *     responses:
 *       200:
 *         description: Contact submissions retrieved successfully
 *         headers:
 *           API-Version:
 *             schema:
 *               type: string
 *             description: Current API version
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     contacts:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/ContactSubmission'
 *                           - type: object
 *                             properties:
 *                               api_version:
 *                                 type: string
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         pages:
 *                           type: integer
 *                 message:
 *                   type: string
 *                 api_version:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/', authenticateToken, async (req, res) => {
  try {
    console.log('🔍 Contact submissions endpoint called (root route)');
    console.log('📋 Request query:', req.query);
    console.log('👤 User:', req.user ? { id: req.user.id, role: req.user.role, email: req.user.email } : 'No user');
    console.log('🔐 Auth headers:', req.headers.authorization ? 'Present' : 'Missing');
    console.log('🔐 Full auth header:', req.headers.authorization ? req.headers.authorization.substring(0, 20) + '...' : 'None');

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const status = req.query.status;
    const sortBy = req.query.sortBy || 'created_at';
    const sortOrder = req.query.sortOrder || 'desc';
    const offset = (page - 1) * limit;

    console.log('📊 Query params:', { page, limit, status, sortBy, sortOrder, offset });

    // Build where clause for status filter
    const whereClause = {};
    if (status && ['new', 'in_progress', 'resolved'].includes(status)) {
      whereClause.status = status;
    }

    // Build order clause
    const orderClause = [[sortBy, sortOrder.toUpperCase()]];

    console.log('🔍 Database query:', { whereClause, orderClause, limit, offset });

    // Check if ContactSubmission model is available
    if (!ContactSubmission) {
      console.error('❌ ContactSubmission model not found');
      return res.status(500).json({
        success: false,
        error: { message: 'ContactSubmission model not available' },
        debug: {
          modelAvailable: false,
          timestamp: new Date().toISOString()
        },
        api_version: req.apiVersion,
      });
    }

    console.log('✅ ContactSubmission model available');

    // Get submissions from database
    const { count, rows: submissions } = await ContactSubmission.findAndCountAll({
      where: whereClause,
      order: orderClause,
      limit: limit,
      offset: offset,
      attributes: ['id', 'name', 'email', 'phone', 'message', 'status', 'created_at', 'updated_at']
    });

    console.log('📊 Database results:', { count, submissionsLength: submissions.length });

    res.json({
      success: true,
      items: submissions, // Frontend expects 'items' for paginated response
      pagination: {
        page,
        limit,
        total: count,
        pages: Math.ceil(count / limit),
      },
      message: 'Contact submissions retrieved successfully',
      debug: {
        queryParams: { page, limit, status, sortBy, sortOrder },
        databaseQuery: { whereClause, orderClause },
        resultsCount: count,
        submissionsReturned: submissions.length,
        timestamp: new Date().toISOString(),
        userInfo: req.user ? { id: req.user.id, role: req.user.role } : null,
        route: 'root'
      },
      api_version: req.apiVersion,
    });
  } catch (error) {
    console.error('❌ Get contacts error (root route):', error);
    console.error('❌ Error stack:', error.stack);
    res.status(500).json({
      success: false,
      error: {
        message: 'Internal server error',
        details: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      debug: {
        errorType: error.constructor.name,
        errorMessage: error.message,
        timestamp: new Date().toISOString(),
        route: 'root'
      },
      api_version: req.apiVersion,
    });
  }
});

/**
 * @swagger
 * /api/v1/contact/{id}/status:
 *   patch:
 *     summary: Update contact submission status (Admin only) (v1)
 *     tags: [Contact v1]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: header
 *         name: Accept
 *         schema:
 *           type: string
 *           example: application/vnd.hlenergy.v1+json
 *         description: API version header (optional)
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact submission ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [new, in_progress, resolved]
 *                 example: "in_progress"
 *     responses:
 *       200:
 *         description: Contact status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     contact:
 *                       allOf:
 *                         - $ref: '#/components/schemas/ContactSubmission'
 *                         - type: object
 *                           properties:
 *                             api_version:
 *                               type: string
 *                 message:
 *                   type: string
 *                 api_version:
 *                   type: string
 *       404:
 *         description: Contact submission not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.patch('/:id/status', [
  requireStaff,
  body('status').isIn(['new', 'in_progress', 'resolved']).withMessage('Invalid status'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
        api_version: req.apiVersion,
      });
    }

    const { id } = req.params;
    const { status } = req.body;

    // Find and update contact submission in database
    const submission = await ContactSubmission.findByPk(id);
    if (!submission) {
      return res.status(404).json({
        success: false,
        error: { message: 'Contact submission not found' },
        api_version: req.apiVersion,
      });
    }

    // Update status
    await submission.update({ status });

    res.json({
      success: true,
      data: { contact: submission },
      message: 'Contact status updated successfully',
      api_version: req.apiVersion,
    });
  } catch (error) {
    console.error('Update contact status error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' },
      api_version: req.apiVersion,
    });
  }
});

/**
 * @swagger
 * /api/v1/contact/{id}:
 *   get:
 *     summary: Get a specific contact submission by ID (v1)
 *     tags: [Contact v1]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact submission ID
 *       - in: header
 *         name: Accept
 *         schema:
 *           type: string
 *           example: application/vnd.hlenergy.v1+json
 *         description: API version header (optional)
 *     responses:
 *       200:
 *         description: Contact submission retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/ContactSubmission'
 *                 message:
 *                   type: string
 *                 api_version:
 *                   type: string
 *       404:
 *         description: Contact submission not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const id = parseInt(req.params.id);

    if (isNaN(id)) {
      return res.status(400).json({
        success: false,
        error: { message: 'Invalid contact submission ID' },
        api_version: req.apiVersion,
      });
    }

    console.log('🔍 Getting contact submission by ID:', id);
    console.log('👤 User:', req.user ? { id: req.user.id, role: req.user.role } : 'No user');

    // Check if ContactSubmission model is available
    if (!ContactSubmission) {
      console.error('❌ ContactSubmission model not found');
      return res.status(500).json({
        success: false,
        error: { message: 'ContactSubmission model not available' },
        api_version: req.apiVersion,
      });
    }

    // Get submission from database
    const submission = await ContactSubmission.findByPk(id, {
      attributes: ['id', 'name', 'email', 'phone', 'message', 'status', 'created_at', 'updated_at']
    });

    if (!submission) {
      return res.status(404).json({
        success: false,
        error: { message: 'Contact submission not found' },
        api_version: req.apiVersion,
      });
    }

    console.log('✅ Contact submission found:', submission.id);

    res.json({
      success: true,
      data: submission,
      message: 'Contact submission retrieved successfully',
      api_version: req.apiVersion,
    });
  } catch (error) {
    console.error('❌ Get contact submission error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Internal server error',
        details: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      api_version: req.apiVersion,
    });
  }
});

/**
 * @swagger
 * /api/v1/contact/{id}:
 *   put:
 *     summary: Update a contact submission (v1)
 *     tags: [Contact v1]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact submission ID
 *       - in: header
 *         name: Accept
 *         schema:
 *           type: string
 *           example: application/vnd.hlenergy.v1+json
 *         description: API version header (optional)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Contact name
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Contact email
 *               phone:
 *                 type: string
 *                 description: Contact phone number
 *               message:
 *                 type: string
 *                 description: Contact message
 *               status:
 *                 type: string
 *                 enum: [new, in_progress, resolved]
 *                 description: Contact submission status
 *     responses:
 *       200:
 *         description: Contact submission updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/ContactSubmission'
 *                 message:
 *                   type: string
 *                 api_version:
 *                   type: string
 *       404:
 *         description: Contact submission not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       400:
 *         description: Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const id = parseInt(req.params.id);

    if (isNaN(id)) {
      return res.status(400).json({
        success: false,
        error: { message: 'Invalid contact submission ID' },
        api_version: req.apiVersion,
      });
    }

    console.log('🔄 Updating contact submission:', id);
    console.log('📝 Update data:', req.body);
    console.log('👤 User:', req.user ? { id: req.user.id, role: req.user.role } : 'No user');

    // Check if ContactSubmission model is available
    if (!ContactSubmission) {
      console.error('❌ ContactSubmission model not found');
      return res.status(500).json({
        success: false,
        error: { message: 'ContactSubmission model not available' },
        api_version: req.apiVersion,
      });
    }

    // Find the submission first
    const submission = await ContactSubmission.findByPk(id);

    if (!submission) {
      return res.status(404).json({
        success: false,
        error: { message: 'Contact submission not found' },
        api_version: req.apiVersion,
      });
    }

    // Validate and prepare update data
    const allowedFields = ['name', 'email', 'phone', 'message', 'status'];
    const updateData = {};

    for (const field of allowedFields) {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    }

    // Validate status if provided
    if (updateData.status && !['new', 'in_progress', 'resolved'].includes(updateData.status)) {
      return res.status(400).json({
        success: false,
        error: { message: 'Invalid status. Must be one of: new, in_progress, resolved' },
        api_version: req.apiVersion,
      });
    }

    // Update the submission
    await submission.update(updateData);

    // Reload to get fresh data
    await submission.reload();

    console.log('✅ Contact submission updated successfully:', submission.id);

    res.json({
      success: true,
      data: submission,
      message: 'Contact submission updated successfully',
      api_version: req.apiVersion,
    });
  } catch (error) {
    console.error('❌ Update contact submission error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Internal server error',
        details: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      api_version: req.apiVersion,
    });
  }
});

/**
 * @swagger
 * /api/v1/contact/track-action:
 *   post:
 *     summary: Track contact action (email, phone, WhatsApp, address clicks)
 *     tags: [Contact v1]
 *     parameters:
 *       - in: header
 *         name: Accept
 *         schema:
 *           type: string
 *           example: application/vnd.hlenergy.v1+json
 *         description: API version header (optional)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - action
 *               - timestamp
 *             properties:
 *               action:
 *                 type: string
 *                 enum: [email, phone, whatsapp, address]
 *                 example: "email"
 *                 description: "Type of contact action taken"
 *               timestamp:
 *                 type: string
 *                 format: date-time
 *                 example: "2024-01-15T10:30:00.000Z"
 *                 description: "When the action was taken"
 *               page:
 *                 type: string
 *                 example: "contact"
 *                 description: "Page where action was taken"
 *               user_agent:
 *                 type: string
 *                 example: "Mozilla/5.0..."
 *                 description: "User agent string"
 *               referrer:
 *                 type: string
 *                 example: "https://google.com"
 *                 description: "Referrer URL"
 *     responses:
 *       200:
 *         description: Action tracked successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Contact action tracked successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 123
 *                     action:
 *                       type: string
 *                       example: "email"
 *                     timestamp:
 *                       type: string
 *                       format: date-time
 *                 api_version:
 *                   type: string
 *                   example: "v1"
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Internal server error
 */
router.post('/track-action', [
  body('action')
    .isIn(['email', 'phone', 'whatsapp', 'address'])
    .withMessage('Action must be one of: email, phone, whatsapp, address'),
  body('timestamp')
    .isISO8601()
    .withMessage('Timestamp must be a valid ISO 8601 date'),
  body('page')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Page must be between 1 and 100 characters'),
  body('user_agent')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('User agent must be less than 1000 characters'),
  body('referrer')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Referrer must be less than 500 characters')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation failed',
          details: errors.array()
        },
        api_version: req.apiVersion || 'v1'
      });
    }

    const { action, timestamp, page, user_agent, referrer } = req.body;

    // Get IP address from request
    const ip_address = req.ip ||
                      req.connection.remoteAddress ||
                      req.socket.remoteAddress ||
                      (req.connection.socket ? req.connection.socket.remoteAddress : null);

    // Create contact action record
    const contactAction = await ContactAction.create({
      action,
      page: page || 'contact',
      user_agent,
      referrer,
      ip_address,
      timestamp: new Date(timestamp),
      session_id: req.sessionID || null
    });

    console.log('✅ Contact action tracked:', {
      id: contactAction.id,
      action,
      page: page || 'contact',
      ip_address,
      timestamp
    });

    res.json({
      success: true,
      message: 'Contact action tracked successfully',
      data: {
        id: contactAction.id,
        action: contactAction.action,
        page: contactAction.page,
        timestamp: contactAction.timestamp
      },
      api_version: req.apiVersion || 'v1'
    });

  } catch (error) {
    console.error('❌ Track contact action error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to track contact action',
        details: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      api_version: req.apiVersion || 'v1'
    });
  }
});

module.exports = router;

const express = require('express');
const { body, validationResult } = require('express-validator');
const authService = require('../../services/authService');
const { authenticateToken, requireRole } = require('../../middleware/auth');
const router = express.Router();

/**
 * Enhanced Authentication Routes
 * Handles PIN and biometric authentication
 */

/**
 * @swagger
 * /api/v1/auth/pin/create:
 *   post:
 *     summary: Create admin PIN
 *     tags: [Enhanced Auth]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - pin
 *             properties:
 *               pin:
 *                 type: string
 *                 description: 6-8 digit PIN
 *                 example: "123456"
 *               deviceFingerprint:
 *                 type: string
 *                 description: Device fingerprint for security
 *     responses:
 *       201:
 *         description: PIN created successfully
 *       400:
 *         description: Invalid PIN or validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 */
router.post('/pin/create', [
  authenticateToken,
  body('pin')
    .isLength({ min: 6, max: 8 })
    .withMessage('PIN must be 6-8 characters long')
    .isNumeric()
    .withMessage('PIN must contain only numbers'),
  body('deviceFingerprint')
    .optional()
    .isString()
    .withMessage('Device fingerprint must be a string'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation error',
          details: errors.array(),
          validationErrors: errors.array().map(err => `${err.param}: ${err.msg}`).join(', ')
        },
        timestamp: new Date().toISOString(),
      });
    }

    const { pin, deviceFingerprint } = req.body;
    const userId = req.user.id;

    const result = await authService.createAdminPin(userId, pin, deviceFingerprint);

    res.status(201).json({
      success: true,
      message: result.message,
      data: {
        pinId: result.pinId,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('PIN creation error:', error);
    res.status(400).json({
      success: false,
      error: {
        message: error.message,
        type: 'pin_creation_error',
      },
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @swagger
 * /api/v1/auth/pin/login:
 *   post:
 *     summary: Login with PIN
 *     tags: [Enhanced Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - pin
 *             properties:
 *               userId:
 *                 type: integer
 *                 description: User ID
 *                 example: 1
 *               pin:
 *                 type: string
 *                 description: 6-8 digit PIN
 *                 example: "123456"
 *               deviceFingerprint:
 *                 type: string
 *                 description: Device fingerprint for security
 *     responses:
 *       200:
 *         description: Login successful
 *       400:
 *         description: Invalid PIN or validation error
 *       401:
 *         description: Authentication failed
 */
router.post('/pin/login', [
  body('userId')
    .isInt({ min: 1 })
    .withMessage('Valid user ID is required'),
  body('pin')
    .isLength({ min: 6, max: 8 })
    .withMessage('PIN must be 6-8 characters long')
    .isNumeric()
    .withMessage('PIN must contain only numbers'),
  body('deviceFingerprint')
    .optional()
    .isString()
    .withMessage('Device fingerprint must be a string'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation failed',
          details: errors.array(),
        },
      });
    }

    const { userId, pin, deviceFingerprint } = req.body;

    const result = await authService.authenticateWithPin(
      userId,
      pin,
      deviceFingerprint,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      success: true,
      message: 'PIN authentication successful',
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('PIN login error:', error);
    res.status(401).json({
      success: false,
      error: {
        message: error.message,
        type: 'pin_authentication_error',
      },
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @swagger
 * /api/v1/auth/pin/verify:
 *   post:
 *     summary: Verify PIN for session unlock (doesn't return tokens)
 *     tags: [Enhanced Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - pin
 *             properties:
 *               userId:
 *                 type: integer
 *                 description: User ID
 *               pin:
 *                 type: string
 *                 description: User PIN (6-8 digits)
 *               deviceFingerprint:
 *                 type: string
 *                 description: Device fingerprint for security
 *     responses:
 *       200:
 *         description: PIN verification successful
 *       400:
 *         description: Invalid PIN or validation error
 *       401:
 *         description: PIN verification failed
 */
router.post('/pin/verify', [
  body('userId')
    .isInt({ min: 1 })
    .withMessage('Valid user ID is required'),
  body('pin')
    .isLength({ min: 6, max: 8 })
    .withMessage('PIN must be 6-8 characters long')
    .isNumeric()
    .withMessage('PIN must contain only numbers'),
  body('deviceFingerprint')
    .optional()
    .isString()
    .withMessage('Device fingerprint must be a string'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation failed',
          details: errors.array(),
        },
      });
    }

    const { userId, pin, deviceFingerprint } = req.body;

    // Verify PIN without generating tokens
    const isValid = await authService.verifyPin(userId, pin, deviceFingerprint);

    if (isValid) {
      res.json({
        success: true,
        message: 'PIN verification successful',
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(401).json({
        success: false,
        error: {
          message: 'Invalid PIN',
          type: 'pin_verification_error',
        },
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error) {
    console.error('PIN verification error:', error);
    res.status(401).json({
      success: false,
      error: {
        message: error.message,
        type: 'pin_verification_error',
      },
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @swagger
 * /api/v1/auth/pin/update:
 *   put:
 *     summary: Update user PIN
 *     tags: [Enhanced Auth]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPin
 *               - newPin
 *             properties:
 *               currentPin:
 *                 type: string
 *                 description: Current 6-8 digit PIN
 *                 example: "123456"
 *               newPin:
 *                 type: string
 *                 description: New 6-8 digit PIN
 *                 example: "654321"
 *               deviceFingerprint:
 *                 type: string
 *                 description: Device fingerprint for security
 *     responses:
 *       200:
 *         description: PIN updated successfully
 *       400:
 *         description: Invalid PIN or validation error
 *       401:
 *         description: Unauthorized or current PIN incorrect
 */
router.put('/pin/update', [
  authenticateToken,
  body('currentPin')
    .isLength({ min: 6, max: 8 })
    .withMessage('Current PIN must be 6-8 characters long')
    .isNumeric()
    .withMessage('Current PIN must contain only numbers'),
  body('newPin')
    .isLength({ min: 6, max: 8 })
    .withMessage('New PIN must be 6-8 characters long')
    .isNumeric()
    .withMessage('New PIN must contain only numbers'),
  body('deviceFingerprint')
    .optional()
    .isString()
    .withMessage('Device fingerprint must be a string'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation error',
          details: errors.array(),
          validationErrors: errors.array().map(err => `${err.param}: ${err.msg}`).join(', ')
        },
        timestamp: new Date().toISOString(),
      });
    }

    const { currentPin, newPin, deviceFingerprint } = req.body;
    const userId = req.user.id;

    const result = await authService.updateAdminPin(userId, currentPin, newPin, deviceFingerprint);

    res.json({
      success: true,
      message: result.message,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('PIN update error:', error);
    res.status(400).json({
      success: false,
      error: {
        message: error.message,
        type: 'pin_update_error',
      },
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @swagger
 * /api/v1/auth/pin/remove:
 *   delete:
 *     summary: Remove user PIN
 *     tags: [Enhanced Auth]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPin
 *             properties:
 *               currentPin:
 *                 type: string
 *                 description: Current 6-8 digit PIN for confirmation
 *                 example: "123456"
 *               deviceFingerprint:
 *                 type: string
 *                 description: Device fingerprint for security
 *     responses:
 *       200:
 *         description: PIN removed successfully
 *       400:
 *         description: Invalid PIN or validation error
 *       401:
 *         description: Unauthorized or current PIN incorrect
 */
router.delete('/pin/remove', [
  authenticateToken,
  body('currentPin')
    .isLength({ min: 6, max: 8 })
    .withMessage('Current PIN must be 6-8 characters long')
    .isNumeric()
    .withMessage('Current PIN must contain only numbers'),
  body('deviceFingerprint')
    .optional()
    .isString()
    .withMessage('Device fingerprint must be a string'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation error',
          details: errors.array(),
          validationErrors: errors.array().map(err => `${err.param}: ${err.msg}`).join(', ')
        },
        timestamp: new Date().toISOString(),
      });
    }

    const { currentPin, deviceFingerprint } = req.body;
    const userId = req.user.id;

    const result = await authService.removeAdminPin(userId, currentPin, deviceFingerprint);

    res.json({
      success: true,
      message: result.message,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('PIN removal error:', error);
    res.status(400).json({
      success: false,
      error: {
        message: error.message,
        type: 'pin_removal_error',
      },
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @swagger
 * /api/v1/auth/profile:
 *   get:
 *     summary: Get current user profile (alias for /api/v1/profile)
 *     tags: [Enhanced Auth]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 */
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const { User } = require('../../models');

    const user = await User.findByPk(req.user.id, {
      attributes: { exclude: ['password'] },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: { message: 'User not found' },
        timestamp: new Date().toISOString(),
      });
    }

    res.json({
      success: true,
      data: user, // Frontend expects direct user object, not nested in { user }
      message: 'Profile retrieved successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Profile get error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve profile' },
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @swagger
 * /api/v1/auth/profile:
 *   put:
 *     summary: Update current user profile (alias for /api/v1/profile)
 *     tags: [Enhanced Auth]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 100
 *               email:
 *                 type: string
 *                 format: email
 *               profile:
 *                 type: object
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 */
router.put('/profile', [
  authenticateToken,
  body('name').optional().isLength({ min: 2, max: 100 }),
  body('email').optional().isEmail(),
  body('profile').optional().isObject(),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation error',
          details: errors.array(),
          validationErrors: errors.array().map(err => `${err.param}: ${err.msg}`).join(', ')
        },
        timestamp: new Date().toISOString(),
      });
    }

    const { User } = require('../../models');
    const { name, email, profile } = req.body;
    const updateData = {};

    if (name !== undefined) updateData.name = name;
    if (email !== undefined) {
      // Check if email is already taken by another user
      const existingUser = await User.findOne({
        where: {
          email,
          id: { [require('sequelize').Op.ne]: req.user.id }
        }
      });

      if (existingUser) {
        return res.status(400).json({
          success: false,
          error: { message: 'Email already in use by another account' },
          timestamp: new Date().toISOString(),
        });
      }

      updateData.email = email;
      // Reset email verification if email changed
      if (email !== req.user.email) {
        updateData.email_verified = false;
      }
    }
    if (profile !== undefined) {
      // Merge with existing profile data
      updateData.profile = { ...req.user.profile, ...profile };
    }

    if (Object.keys(updateData).length === 0) {
      return res.status(400).json({
        success: false,
        error: { message: 'No valid fields to update' },
        timestamp: new Date().toISOString(),
      });
    }

    await User.update(updateData, {
      where: { id: req.user.id }
    });

    const updatedUser = await User.findByPk(req.user.id, {
      attributes: { exclude: ['password'] },
    });

    res.json({
      success: true,
      data: updatedUser, // Frontend expects direct user object
      message: 'Profile updated successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to update profile' },
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @swagger
 * /api/v1/auth/biometric/register:
 *   post:
 *     summary: Register biometric credential
 *     tags: [Enhanced Auth]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - credentialId
 *               - publicKey
 *             properties:
 *               credentialId:
 *                 type: string
 *                 description: WebAuthn credential ID
 *               publicKey:
 *                 type: string
 *                 description: Public key for verification
 *               deviceType:
 *                 type: string
 *                 enum: [platform, cross-platform]
 *                 description: Type of authenticator
 *               deviceName:
 *                 type: string
 *                 description: User-friendly device name
 *               transport:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Transport methods
 *               deviceFingerprint:
 *                 type: string
 *                 description: Device fingerprint
 *     responses:
 *       201:
 *         description: Biometric credential registered successfully
 *       400:
 *         description: Invalid data or validation error
 *       401:
 *         description: Unauthorized
 */
router.post('/biometric/register', [
  authenticateToken,
  body('credentialId')
    .notEmpty()
    .withMessage('Credential ID is required'),
  body('publicKey')
    .notEmpty()
    .withMessage('Public key is required'),
  body('deviceType')
    .optional()
    .isIn(['platform', 'cross-platform'])
    .withMessage('Device type must be platform or cross-platform'),
  body('deviceName')
    .optional()
    .isString()
    .withMessage('Device name must be a string'),
  body('transport')
    .optional()
    .isArray()
    .withMessage('Transport must be an array'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation failed',
          details: errors.array(),
        },
      });
    }

    const userId = req.user.id;
    const credentialData = req.body;

    const result = await authService.registerBiometricCredential(
      userId,
      credentialData,
      req.body.deviceFingerprint
    );

    res.status(201).json({
      success: true,
      message: result.message,
      data: {
        credentialId: result.credentialId,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Biometric registration error:', error);
    res.status(400).json({
      success: false,
      error: {
        message: error.message,
        type: 'biometric_registration_error',
      },
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @swagger
 * /api/v1/auth/biometric/remove:
 *   delete:
 *     summary: Remove biometric authentication
 *     tags: [Enhanced Auth]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Biometric authentication removed successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: No biometric credentials found
 */
router.delete('/biometric/remove', authenticateToken, async (req, res) => {
  try {
    const result = await authService.removeBiometric(req.user.id);

    res.json({
      success: true,
      message: result.message || 'Biometric authentication removed successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Biometric removal error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: error.message || 'Failed to remove biometric authentication',
        type: 'biometric_removal_error'
      },
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @swagger
 * /api/v1/auth/biometric/login:
 *   post:
 *     summary: Login with biometric
 *     tags: [Enhanced Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - credentialId
 *               - signature
 *               - clientData
 *             properties:
 *               credentialId:
 *                 type: string
 *                 description: WebAuthn credential ID
 *               signature:
 *                 type: string
 *                 description: Biometric signature
 *               clientData:
 *                 type: string
 *                 description: Client data JSON
 *               deviceFingerprint:
 *                 type: string
 *                 description: Device fingerprint
 *     responses:
 *       200:
 *         description: Biometric authentication successful
 *       400:
 *         description: Invalid data or validation error
 *       401:
 *         description: Authentication failed
 */
router.post('/biometric/login', [
  body('credentialId')
    .notEmpty()
    .withMessage('Credential ID is required'),
  body('signature')
    .notEmpty()
    .withMessage('Signature is required'),
  body('clientData')
    .notEmpty()
    .withMessage('Client data is required'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation failed',
          details: errors.array(),
        },
      });
    }

    const { credentialId, signature, clientData, deviceFingerprint } = req.body;

    const result = await authService.authenticateWithBiometric(
      credentialId,
      signature,
      clientData,
      deviceFingerprint,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      success: true,
      message: 'Biometric authentication successful',
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Biometric login error:', error);
    res.status(401).json({
      success: false,
      error: {
        message: error.message,
        type: 'biometric_authentication_error',
      },
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @swagger
 * /api/v1/auth/biometric/unlock:
 *   post:
 *     summary: Unlock session with biometric (requires existing session)
 *     tags: [Enhanced Auth]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - credentialId
 *               - signature
 *               - clientData
 *             properties:
 *               credentialId:
 *                 type: string
 *                 description: WebAuthn credential ID
 *               signature:
 *                 type: string
 *                 description: Biometric signature
 *               clientData:
 *                 type: string
 *                 description: Client data JSON
 *               deviceFingerprint:
 *                 type: string
 *                 description: Device fingerprint
 *     responses:
 *       200:
 *         description: Session unlocked successfully
 *       400:
 *         description: Invalid data or validation error
 *       401:
 *         description: Authentication failed or no existing session
 */
router.post('/biometric/unlock', [
  authenticateToken, // Requires existing session
  body('credentialId')
    .notEmpty()
    .withMessage('Credential ID is required'),
  body('signature')
    .notEmpty()
    .withMessage('Signature is required'),
  body('clientData')
    .notEmpty()
    .withMessage('Client data is required'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation failed',
          details: errors.array(),
        },
      });
    }

    const { credentialId, signature, clientData, deviceFingerprint } = req.body;
    const userId = req.user.id; // Get user ID from existing session

    // Verify biometric credential belongs to current user
    const { BiometricAuth } = require('../../models');
    const credential = await BiometricAuth.findOne({
      where: {
        user_id: userId,
        credential_id: credentialId,
        is_active: true
      }
    });

    if (!credential) {
      return res.status(401).json({
        success: false,
        error: {
          message: 'Biometric credential not found for current user',
          type: 'biometric_unlock_error',
        },
        timestamp: new Date().toISOString(),
      });
    }

    // Perform biometric verification (simplified for session unlock)
    const isValid = await authService.verifyBiometricSignature(
      credential,
      signature,
      clientData
    );

    if (!isValid) {
      return res.status(401).json({
        success: false,
        error: {
          message: 'Biometric verification failed',
          type: 'biometric_unlock_error',
        },
        timestamp: new Date().toISOString(),
      });
    }

    // Generate new tokens for session refresh (like PIN unlock)
    const jwt = require('jsonwebtoken');
    const { User } = require('../../models');

    const user = await User.findByPk(userId, {
      attributes: { exclude: ['password'] },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: { message: 'User not found' },
        timestamp: new Date().toISOString(),
      });
    }

    // Generate fresh tokens
    const accessToken = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        role: user.role,
        authMethod: 'biometric'
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    const refreshToken = jwt.sign(
      {
        userId: user.id,
        type: 'refresh',
        authMethod: 'biometric'
      },
      process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d' }
    );

    // Store refresh token
    const { RefreshToken } = require('../../models');
    await RefreshToken.create({
      user_id: user.id,
      token: refreshToken,
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      device_fingerprint: deviceFingerprint
    });

    // Update last login
    await user.update({ last_login: new Date() });

    res.json({
      success: true,
      message: 'Session unlocked with biometric authentication',
      data: {
        user,
        accessToken,
        refreshToken,
        expiresIn: 604800, // 7 days in seconds
        authMethod: 'biometric'
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Biometric unlock error:', error);
    res.status(401).json({
      success: false,
      error: {
        message: error.message || 'Biometric unlock failed',
        type: 'biometric_unlock_error',
      },
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @swagger
 * /api/v1/auth/methods:
 *   get:
 *     summary: Get user's available authentication methods
 *     tags: [Enhanced Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User's authentication methods
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     methods:
 *                       type: array
 *                       items:
 *                         type: string
 *                     hasPin:
 *                       type: boolean
 *                     biometricCount:
 *                       type: integer
 *       401:
 *         description: Authentication required
 */
router.get('/methods', authenticateToken, async (req, res) => {
  try {
    const { AdminPin, BiometricAuth } = require('../../models');
    const userId = req.user.id;

    // Check for active PIN
    const pinAuth = await AdminPin.findOne({
      where: {
        user_id: userId,
        is_active: true
      }
    });

    // Check for active biometric credentials
    const biometricCount = await BiometricAuth.count({
      where: {
        user_id: userId,
        is_active: true
      }
    });

    // Determine available methods
    const methods = ['password']; // Password is always available
    if (pinAuth) methods.push('pin');
    if (biometricCount > 0) methods.push('biometric');

    res.json({
      success: true,
      data: {
        methods,
        hasPin: !!pinAuth,
        biometricCount
      },
      api_version: req.apiVersion
    });
  } catch (error) {
    console.error('Get auth methods error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to get authentication methods' },
      api_version: req.apiVersion
    });
  }
});

module.exports = router;

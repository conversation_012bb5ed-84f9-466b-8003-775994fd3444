const express = require('express');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const { UserSession, AdminPin, BiometricAuth } = require('../../models');
const { authenticateToken, authenticateTokenForUnlock } = require('../../middleware/auth');
const { logAuth, logSecurity } = require('../../config/logger');

const router = express.Router();

/**
 * @swagger
 * /api/v1/session/status:
 *   get:
 *     summary: Get current session status
 *     tags: [Session Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Session status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     session_id:
 *                       type: string
 *                     is_locked:
 *                       type: boolean
 *                     locked_at:
 *                       type: string
 *                       format: date-time
 *                     lock_reason:
 *                       type: string
 *                     last_activity:
 *                       type: string
 *                       format: date-time
 *                     idle_timeout_minutes:
 *                       type: integer
 *                     expires_at:
 *                       type: string
 *                       format: date-time
 *                     should_be_locked:
 *                       type: boolean
 *       401:
 *         description: Unauthorized or session not found
 */
router.get('/status', authenticateTokenForUnlock, async (req, res) => {
  try {
    // Get session from the authenticateTokenForUnlock middleware
    const session = req.session;

    if (!session) {
      return res.status(404).json({
        success: false,
        error: { message: 'Session not found' },
        code: 'SESSION_NOT_FOUND',
      });
    }
    
    // Check if session should be locked
    const shouldBeLocked = session.shouldBeLocked();
    
    // Auto-lock if needed
    if (shouldBeLocked && !session.is_locked) {
      await session.lockSession('inactivity');
      logSecurity('Session auto-locked due to inactivity', {
        userId: session.user_id,
        sessionToken: session.session_token.substring(0, 20) + '...',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
    }
    
    res.json({
      success: true,
      data: {
        session_id: session.id,
        session_token: session.session_token.substring(0, 20) + '...', // Partial token for security
        is_locked: session.is_locked || shouldBeLocked,
        locked_at: session.locked_at,
        lock_reason: session.lock_reason,
        last_activity: session.last_activity,
        idle_timeout_minutes: session.idle_timeout_minutes,
        expires_at: session.expires_at,
        should_be_locked: shouldBeLocked,
        is_valid: session.isValid(),
      },
    });
  } catch (error) {
    console.error('Session status error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to get session status' },
      code: 'SESSION_STATUS_ERROR',
    });
  }
});

/**
 * @swagger
 * /api/v1/session/activity:
 *   post:
 *     summary: Update session activity timestamp
 *     tags: [Session Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Activity updated successfully
 *       401:
 *         description: Unauthorized or session not found
 */
router.post('/activity', authenticateToken, async (req, res) => {
  try {
    const token = req.token;
    const decoded = jwt.decode(token);
    const sessionToken = decoded.jti || token;
    
    const session = await UserSession.findBySessionToken(sessionToken);
    
    if (!session) {
      return res.status(404).json({
        success: false,
        error: { message: 'Session not found' },
        code: 'SESSION_NOT_FOUND',
      });
    }
    
    if (!session.isValid()) {
      return res.status(401).json({
        success: false,
        error: { message: 'Session is invalid or expired' },
        code: 'SESSION_INVALID',
      });
    }
    
    // Update activity timestamp
    await session.updateActivity();
    
    res.json({
      success: true,
      data: {
        last_activity: session.last_activity,
        message: 'Activity updated successfully',
      },
    });
  } catch (error) {
    console.error('Update activity error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to update activity' },
      code: 'UPDATE_ACTIVITY_ERROR',
    });
  }
});

/**
 * @swagger
 * /api/v1/session/lock:
 *   post:
 *     summary: Lock current session
 *     tags: [Session Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *                 enum: [inactivity, admin_action, security_breach, manual]
 *                 default: manual
 *     responses:
 *       200:
 *         description: Session locked successfully
 *       401:
 *         description: Unauthorized or session not found
 */
router.post('/lock', authenticateToken, async (req, res) => {
  try {
    const { reason = 'manual' } = req.body;
    const token = req.token;
    const decoded = jwt.decode(token);
    const sessionToken = decoded.jti || token;
    
    const session = await UserSession.findBySessionToken(sessionToken);
    
    if (!session) {
      return res.status(404).json({
        success: false,
        error: { message: 'Session not found' },
        code: 'SESSION_NOT_FOUND',
      });
    }
    
    if (session.is_locked) {
      return res.json({
        success: true,
        data: { message: 'Session is already locked' },
      });
    }
    
    // Lock the session
    try {
      await session.lockSession(reason);

      // Verify the session was actually locked
      await session.reload();
      if (!session.is_locked) {
        throw new Error('Session lock verification failed - session not marked as locked');
      }

      logSecurity('Session manually locked', {
        userId: session.user_id,
        sessionToken: sessionToken.substring(0, 20) + '...',
        reason,
        lockReason: session.lock_reason,
        lockedAt: session.locked_at,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      res.json({
        success: true,
        data: {
          message: 'Session locked successfully',
          locked_at: session.locked_at,
          lock_reason: session.lock_reason,
        },
      });
    } catch (lockError) {
      console.error('Session lock operation failed:', lockError);

      // Log the security incident
      logSecurity('Session lock failed - potential security risk', {
        userId: session.user_id,
        sessionToken: sessionToken.substring(0, 20) + '...',
        reason,
        error: lockError.message,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      // Force session invalidation as a security measure
      try {
        await session.update({
          is_active: false,
          updated_at: new Date()
        });
        console.log('🔒 Session invalidated due to lock failure for security');
      } catch (invalidateError) {
        console.error('Failed to invalidate session after lock failure:', invalidateError);
      }

      throw lockError; // Re-throw to be caught by outer catch block
    }
  } catch (error) {
    console.error('Lock session error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to lock session',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      code: 'LOCK_SESSION_ERROR',
    });
  }
});

/**
 * @swagger
 * /api/v1/session/unlock:
 *   post:
 *     summary: Unlock session with PIN or biometric verification
 *     tags: [Session Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - method
 *             properties:
 *               method:
 *                 type: string
 *                 enum: [pin, biometric]
 *               pin:
 *                 type: string
 *                 description: Required when method is 'pin'
 *               biometric_data:
 *                 type: object
 *                 description: Required when method is 'biometric'
 *     responses:
 *       200:
 *         description: Session unlocked successfully
 *       400:
 *         description: Invalid unlock method or credentials
 *       401:
 *         description: Unauthorized or invalid credentials
 */
router.post('/unlock', [
  authenticateTokenForUnlock,  // Use special middleware that bypasses session lock check
  body('method').isIn(['pin', 'biometric']).withMessage('Method must be pin or biometric'),
  body('pin').optional().isLength({ min: 6, max: 6 }).withMessage('PIN must be 6 digits'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
        code: 'VALIDATION_ERROR',
      });
    }
    
    const { method, pin, biometric_data } = req.body;

    // Get session from the authenticateTokenForUnlock middleware
    const session = req.session;

    if (!session) {
      return res.status(404).json({
        success: false,
        error: { message: 'Session not found' },
        code: 'SESSION_NOT_FOUND',
      });
    }
    
    if (!session.is_locked) {
      return res.json({
        success: true,
        data: { message: 'Session is not locked' },
      });
    }
    
    // Verify unlock credentials based on method
    let unlockSuccessful = false;
    
    if (method === 'pin') {
      if (!pin) {
        return res.status(400).json({
          success: false,
          error: { message: 'PIN is required for PIN unlock' },
          code: 'PIN_REQUIRED',
        });
      }
      
      // Verify PIN
      const adminPin = await AdminPin.findOne({
        where: { user_id: session.user_id, is_active: true },
      });
      
      if (!adminPin) {
        return res.status(400).json({
          success: false,
          error: { message: 'No PIN configured for this user' },
          code: 'NO_PIN_CONFIGURED',
        });
      }
      
      const isPinValid = await adminPin.validatePin(pin);
      if (!isPinValid) {
        logSecurity('Invalid PIN used for session unlock', {
          userId: session.user_id,
          sessionToken: session.session_token.substring(0, 20) + '...',
          ip: req.ip,
          userAgent: req.get('User-Agent'),
        });
        
        return res.status(401).json({
          success: false,
          error: { message: 'Invalid PIN' },
          code: 'INVALID_PIN',
        });
      }
      
      unlockSuccessful = true;
      
    } else if (method === 'biometric') {
      if (!biometric_data) {
        return res.status(400).json({
          success: false,
          error: { message: 'Biometric data is required for biometric unlock' },
          code: 'BIOMETRIC_DATA_REQUIRED',
        });
      }
      
      // For now, we'll accept any biometric data as valid
      // In a real implementation, you would verify the biometric signature
      unlockSuccessful = true;
    }
    
    if (unlockSuccessful) {
      // Unlock the session
      await session.unlockSession();
      
      logAuth('session_unlocked', session.user_id, {
        method,
        sessionToken: session.session_token.substring(0, 20) + '...',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      
      res.json({
        success: true,
        data: {
          message: 'Session unlocked successfully',
          unlocked_at: new Date().toISOString(),
          method_used: method,
        },
      });
    } else {
      res.status(401).json({
        success: false,
        error: { message: 'Unlock failed' },
        code: 'UNLOCK_FAILED',
      });
    }
  } catch (error) {
    console.error('Unlock session error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to unlock session' },
      code: 'UNLOCK_SESSION_ERROR',
    });
  }
});

module.exports = router;

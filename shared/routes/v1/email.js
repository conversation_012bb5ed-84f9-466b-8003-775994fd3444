const express = require('express');
const { body, validationResult } = require('express-validator');
const { User, EmailVerification, PasswordReset } = require('../../models');
const { authenticateToken, optionalAuth } = require('../../middleware/auth');
const { logAuth, logSecurity } = require('../../config/logger');
const emailQueueService = require('../../services/emailQueueService');

const router = express.Router();

/**
 * Email Verification & Password Reset Routes
 * Handles email verification and password reset functionality
 */

/**
 * @swagger
 * /api/v1/email/send-verification:
 *   post:
 *     summary: Send email verification
 *     tags: [Email]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Verification email sent
 *       400:
 *         description: Email already verified or invalid request
 */
router.post('/send-verification', authenticateToken, async (req, res) => {
  try {
    const user = req.user;

    // Check if email is already verified
    if (user.email_verified) {
      return res.status(400).json({
        success: false,
        error: { message: '<PERSON><PERSON> is already verified' },
        code: 'EMAIL_ALREADY_VERIFIED',
      });
    }

    // Check for existing pending verification
    const existingVerification = await EmailVerification.getUserPendingVerification(user.id);
    if (existingVerification) {
      const timeLeft = Math.ceil((existingVerification.expires_at - new Date()) / (1000 * 60));
      return res.status(400).json({
        success: false,
        error: { 
          message: 'Verification email already sent',
          timeLeft: `${timeLeft} minutes`,
        },
        code: 'VERIFICATION_ALREADY_SENT',
      });
    }

    // Create verification token
    const verification = await EmailVerification.createVerificationToken(user.id, user.email);

    // Queue verification email
    const emailResult = await emailQueueService.queueVerificationEmail(user, verification.token, {
      ip_address: req.ip,
      user_agent: req.get('User-Agent'),
    });

    if (!emailResult.success) {
      await verification.destroy();
      return res.status(500).json({
        success: false,
        error: { message: 'Failed to queue verification email' },
        code: 'EMAIL_SEND_FAILED',
      });
    }

    logAuth('verification_email_sent', user.id, {
      email: user.email,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      emailPreview: emailResult.preview,
    });

    res.json({
      success: true,
      message: 'Verification email sent successfully',
      data: {
        email: user.email,
        expiresIn: '24 hours',
        preview: emailResult.preview, // For development
      },
    });
  } catch (error) {
    console.error('Send verification email error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to send verification email' },
    });
  }
});

/**
 * @swagger
 * /api/v1/email/verify:
 *   post:
 *     summary: Verify email address
 *     tags: [Email]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *             properties:
 *               token:
 *                 type: string
 *     responses:
 *       200:
 *         description: Email verified successfully
 *       400:
 *         description: Invalid or expired token
 */
router.post('/verify', [
  body('token').isLength({ min: 32, max: 64 }).withMessage('Invalid token format'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
      });
    }

    const { token } = req.body;

    // Find and validate token
    const verification = await EmailVerification.findValidToken(token);
    if (!verification) {
      logSecurity('Invalid email verification token used', {
        token: token.substring(0, 8) + '...',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      return res.status(400).json({
        success: false,
        error: { message: 'Invalid or expired verification token' },
        code: 'INVALID_TOKEN',
      });
    }

    // Check if user is already verified
    if (verification.user.email_verified) {
      await verification.markAsUsed(req.ip, req.get('User-Agent'));
      return res.status(400).json({
        success: false,
        error: { message: 'Email is already verified' },
        code: 'EMAIL_ALREADY_VERIFIED',
      });
    }

    // Mark verification as used
    await verification.markAsUsed(req.ip, req.get('User-Agent'));

    // Update user email verification status
    await User.update(
      { 
        email_verified: true,
        email_verified_at: new Date(),
      },
      { where: { id: verification.user_id } }
    );

    // Queue welcome email
    const welcomeResult = await emailQueueService.queueWelcomeEmail(verification.user, {
      ip_address: req.ip,
      user_agent: req.get('User-Agent'),
    });

    logAuth('email_verified', verification.user_id, {
      email: verification.email,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      welcomeEmailSent: welcomeResult.success,
    });

    res.json({
      success: true,
      message: 'Email verified successfully',
      data: {
        user: {
          id: verification.user.id,
          name: verification.user.name,
          email: verification.user.email,
          email_verified: true,
        },
      },
    });
  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to verify email' },
    });
  }
});

/**
 * @swagger
 * /api/v1/email/forgot-password:
 *   post:
 *     summary: Request password reset
 *     tags: [Email]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *     responses:
 *       200:
 *         description: Password reset email sent (always returns success for security)
 */
router.post('/forgot-password', [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
      });
    }

    const { email } = req.body;

    // Always return success for security (don't reveal if email exists)
    const successResponse = {
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent',
      data: {
        email,
        expiresIn: '1 hour',
      },
    };

    // Check rate limiting
    const rateLimit = await PasswordReset.checkRateLimit(email);
    if (!rateLimit.allowed) {
      logSecurity('Password reset rate limit exceeded', {
        email,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        remaining: rateLimit.remaining,
      });

      // Still return success for security
      return res.json(successResponse);
    }

    // Find user by email
    const user = await User.findOne({ where: { email } });
    if (!user) {
      logSecurity('Password reset requested for non-existent email', {
        email,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      // Still return success for security
      return res.json(successResponse);
    }

    // Check if user is active
    if (!user.is_active) {
      logSecurity('Password reset requested for inactive user', {
        userId: user.id,
        email,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      // Still return success for security
      return res.json(successResponse);
    }

    // Create password reset token
    const reset = await PasswordReset.createResetToken(user.id, email);

    // Queue password reset email
    const emailResult = await emailQueueService.queuePasswordResetEmail(user, reset.token, {
      ip_address: req.ip,
      user_agent: req.get('User-Agent'),
    });

    logAuth('password_reset_requested', user.id, {
      email,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      emailSent: emailResult.success,
      emailPreview: emailResult.preview,
    });

    // Always return success
    res.json({
      ...successResponse,
      data: {
        ...successResponse.data,
        preview: emailResult.preview, // For development
      },
    });
  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to process password reset request' },
    });
  }
});

/**
 * @swagger
 * /api/v1/email/reset-password:
 *   post:
 *     summary: Reset password with token
 *     tags: [Email]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *               - password
 *             properties:
 *               token:
 *                 type: string
 *               password:
 *                 type: string
 *                 minLength: 6
 *     responses:
 *       200:
 *         description: Password reset successfully
 *       400:
 *         description: Invalid or expired token
 */
router.post('/reset-password', [
  body('token').isLength({ min: 32, max: 64 }).withMessage('Invalid token format'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
      });
    }

    const { token, password } = req.body;

    // Find and validate token
    const reset = await PasswordReset.findValidToken(token);
    if (!reset) {
      logSecurity('Invalid password reset token used', {
        token: token.substring(0, 8) + '...',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      return res.status(400).json({
        success: false,
        error: { message: 'Invalid or expired reset token' },
        code: 'INVALID_TOKEN',
      });
    }

    // Check if user is still active
    if (!reset.user.is_active) {
      await reset.markAsUsed(req.ip, req.get('User-Agent'));
      return res.status(400).json({
        success: false,
        error: { message: 'Account is deactivated' },
        code: 'ACCOUNT_DEACTIVATED',
      });
    }

    // Mark reset token as used
    await reset.markAsUsed(req.ip, req.get('User-Agent'));

    // Update user password
    await User.update(
      { password }, // Will be hashed automatically by the model
      { where: { id: reset.user_id } }
    );

    // Revoke all refresh tokens to force re-login
    const { RefreshToken } = require('../../models');
    await RefreshToken.revokeAllUserTokens(reset.user_id, reset.user_id);

    logAuth('password_reset_completed', reset.user_id, {
      email: reset.email,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      tokensRevoked: true,
    });

    res.json({
      success: true,
      message: 'Password reset successfully. Please log in with your new password.',
      data: {
        user: {
          id: reset.user.id,
          email: reset.user.email,
        },
      },
    });
  } catch (error) {
    console.error('Password reset error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to reset password' },
    });
  }
});

module.exports = router;

const express = require('express');
const { body, validationResult } = require('express-validator');
const { User, RefreshToken } = require('../../models');
const { authenticateToken, requireOwnershipOrStaff } = require('../../middleware/auth');
const { logAuth, logSecurity } = require('../../config/logger');

const router = express.Router();

/**
 * User Profile Management Routes
 * Provides functionality for users to manage their own profiles
 */

// Apply authentication to all profile routes
router.use(authenticateToken);

/**
 * @swagger
 * /api/v1/profile:
 *   get:
 *     summary: Get current user profile
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile data
 */
router.get('/', async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id, {
      attributes: { exclude: ['password'] },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: { message: 'User not found' },
      });
    }

    res.json({
      success: true,
      data: { user },
      message: 'Profile retrieved successfully',
    });
  } catch (error) {
    console.error('Profile get error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve profile' },
    });
  }
});

/**
 * @swagger
 * /api/v1/profile:
 *   patch:
 *     summary: Update current user profile
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 100
 *               email:
 *                 type: string
 *                 format: email
 *               profile:
 *                 type: object
 *     responses:
 *       200:
 *         description: Profile updated successfully
 */
router.patch('/', [
  body('name').optional().isLength({ min: 2, max: 100 }),
  body('email').optional().isEmail(),
  body('profile').optional().isObject(),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
      });
    }

    const { name, email, profile } = req.body;
    const updateData = {};

    if (name !== undefined) updateData.name = name;
    if (email !== undefined) {
      // Check if email is already taken by another user
      const existingUser = await User.findOne({
        where: { 
          email,
          id: { [require('sequelize').Op.ne]: req.user.id }
        }
      });

      if (existingUser) {
        return res.status(400).json({
          success: false,
          error: { message: 'Email already in use by another account' },
        });
      }

      updateData.email = email;
      // Reset email verification if email changed
      if (email !== req.user.email) {
        updateData.email_verified = false;
      }
    }
    if (profile !== undefined) {
      // Merge with existing profile data
      updateData.profile = { ...req.user.profile, ...profile };
    }

    if (Object.keys(updateData).length === 0) {
      return res.status(400).json({
        success: false,
        error: { message: 'No valid fields to update' },
      });
    }

    await User.update(updateData, {
      where: { id: req.user.id }
    });

    const updatedUser = await User.findByPk(req.user.id, {
      attributes: { exclude: ['password'] },
    });

    logAuth('profile_updated', req.user.id, {
      changes: Object.keys(updateData),
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      data: { user: updatedUser },
      message: 'Profile updated successfully',
    });
  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to update profile' },
    });
  }
});

/**
 * @swagger
 * /api/v1/profile/password:
 *   patch:
 *     summary: Change user password
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *               newPassword:
 *                 type: string
 *                 minLength: 6
 *     responses:
 *       200:
 *         description: Password changed successfully
 */
router.patch('/password', [
  body('currentPassword').notEmpty(),
  body('newPassword').isLength({ min: 6 }),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
      });
    }

    const { currentPassword, newPassword } = req.body;

    // Get user with password for verification
    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: { message: 'User not found' },
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await user.validatePassword(currentPassword);
    if (!isCurrentPasswordValid) {
      logSecurity('Invalid current password in password change', {
        userId: req.user.id,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      return res.status(400).json({
        success: false,
        error: { message: 'Current password is incorrect' },
      });
    }

    // Update password (will be hashed automatically by the model)
    await user.update({ password: newPassword });

    // Revoke all refresh tokens to force re-login on other devices
    await RefreshToken.revokeAllUserTokens(req.user.id, req.user.id);

    logAuth('password_changed', req.user.id, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      tokensRevoked: true,
    });

    res.json({
      success: true,
      message: 'Password changed successfully. Please log in again on other devices.',
    });
  } catch (error) {
    console.error('Password change error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to change password' },
    });
  }
});

/**
 * @swagger
 * /api/v1/profile/sessions:
 *   get:
 *     summary: Get active sessions (refresh tokens)
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of active sessions
 */
router.get('/sessions', async (req, res) => {
  try {
    const sessions = await RefreshToken.findAll({
      where: { 
        user_id: req.user.id,
        is_revoked: false,
      },
      order: [['created_at', 'DESC']],
      attributes: ['id', 'ip_address', 'user_agent', 'created_at', 'expires_at'],
    });

    res.json({
      success: true,
      data: { sessions },
      message: 'Active sessions retrieved successfully',
    });
  } catch (error) {
    console.error('Sessions get error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve sessions' },
    });
  }
});

/**
 * @swagger
 * /api/v1/profile/sessions/{id}:
 *   delete:
 *     summary: Revoke a specific session
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Session revoked successfully
 */
router.delete('/sessions/:id', async (req, res) => {
  try {
    const sessionId = parseInt(req.params.id);

    const session = await RefreshToken.findOne({
      where: { 
        id: sessionId,
        user_id: req.user.id,
        is_revoked: false,
      },
    });

    if (!session) {
      return res.status(404).json({
        success: false,
        error: { message: 'Session not found' },
      });
    }

    await session.revoke(req.user.id);

    logAuth('session_revoked', req.user.id, {
      sessionId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      message: 'Session revoked successfully',
    });
  } catch (error) {
    console.error('Session revoke error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to revoke session' },
    });
  }
});

/**
 * @swagger
 * /api/v1/profile/sessions:
 *   delete:
 *     summary: Revoke all sessions except current
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: All other sessions revoked successfully
 */
router.delete('/sessions', async (req, res) => {
  try {
    // Get current token to exclude it
    const currentToken = req.token;
    
    // Find current refresh token
    const currentRefreshToken = await RefreshToken.findOne({
      where: {
        user_id: req.user.id,
        is_revoked: false,
      },
      order: [['created_at', 'DESC']],
    });

    // Revoke all tokens except current one
    const revokedCount = await RefreshToken.update(
      {
        is_revoked: true,
        revoked_at: new Date(),
        revoked_by: req.user.id,
      },
      {
        where: {
          user_id: req.user.id,
          is_revoked: false,
          id: { [require('sequelize').Op.ne]: currentRefreshToken?.id || 0 },
        },
      }
    );

    logAuth('all_other_sessions_revoked', req.user.id, {
      revokedCount: revokedCount[0],
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      data: { revokedCount: revokedCount[0] },
      message: 'All other sessions revoked successfully',
    });
  } catch (error) {
    console.error('Sessions revoke all error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to revoke sessions' },
    });
  }
});

module.exports = router;

const express = require('express');
const authRoutes = require('./auth');
const enhancedAuthRoutes = require('./enhancedAuth');
const sessionRoutes = require('./session');
const contactRoutes = require('./contact');
const adminRoutes = require('./admin');
const profileRoutes = require('./profile');
const emailRoutes = require('./email');
const emailQueueRoutes = require('./emailQueue');
const pushRoutes = require('./push');
const analyticsRoutes = require('../analytics');
const crmRoutes = require('./crm');
const leadRoutes = require('../leadRoutes');
const testLoggingRoutes = require('./test-logging');
const logsRoutes = require('./logs');
const workerRoutes = require('./worker');

const router = express.Router();

/**
 * @swagger
 * /api/v1:
 *   get:
 *     summary: API v1 Information
 *     tags: [System v1]
 *     parameters:
 *       - in: header
 *         name: Accept
 *         schema:
 *           type: string
 *           example: application/vnd.hlenergy.v1+json
 *         description: API version header (optional)
 *     responses:
 *       200:
 *         description: API v1 information
 *         headers:
 *           API-Version:
 *             schema:
 *               type: string
 *             description: Current API version
 *           API-Latest-Version:
 *             schema:
 *               type: string
 *             description: Latest available API version
 *           API-Deprecated:
 *             schema:
 *               type: string
 *             description: Whether this version is deprecated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 version:
 *                   type: string
 *                 api_version:
 *                   type: string
 *                 is_latest:
 *                   type: boolean
 *                 is_deprecated:
 *                   type: boolean
 *                 endpoints:
 *                   type: object
 *                 documentation:
 *                   type: object
 */
router.get('/', (req, res) => {
  const baseUrl = `${req.protocol}://${req.get('host')}`;
  
  res.json({
    message: 'HLenergy API v1',
    version: '1.0.0',
    api_version: req.apiVersion,
    is_latest: req.isLatestVersion,
    is_deprecated: !req.isLatestVersion,
    endpoints: {
      authentication: {
        register: 'POST /api/v1/auth/register',
        login: 'POST /api/v1/auth/login',
        logout: 'POST /api/v1/auth/logout',
        refresh: 'POST /api/v1/auth/refresh',
        pin_create: 'POST /api/v1/auth/pin/create',
        pin_login: 'POST /api/v1/auth/pin/login',
        biometric_register: 'POST /api/v1/auth/biometric/register',
        biometric_login: 'POST /api/v1/auth/biometric/login',
      },
      session: {
        status: 'GET /api/v1/session/status',
        activity: 'POST /api/v1/session/activity',
        lock: 'POST /api/v1/session/lock',
        unlock: 'POST /api/v1/session/unlock',
      },
      contact: {
        submit: 'POST /api/v1/contact/submit',
        list: 'GET /api/v1/contact',
        update_status: 'PATCH /api/v1/contact/:id/status',
      },
      profile: {
        get: 'GET /api/v1/profile',
        update: 'PATCH /api/v1/profile',
        change_password: 'PATCH /api/v1/profile/password',
        sessions: 'GET /api/v1/profile/sessions',
        revoke_session: 'DELETE /api/v1/profile/sessions/:id',
      },
      admin: {
        dashboard: 'GET /api/v1/admin/dashboard',
        users: 'GET /api/v1/admin/users',
        user_details: 'GET /api/v1/admin/users/:id',
        update_user_status: 'PATCH /api/v1/admin/users/:id/status',
        contacts: 'GET /api/v1/admin/contacts',
        update_contact: 'PATCH /api/v1/admin/contacts/:id',
      },
      email: {
        send_verification: 'POST /api/v1/email/send-verification',
        verify_email: 'POST /api/v1/email/verify',
        forgot_password: 'POST /api/v1/email/forgot-password',
        reset_password: 'POST /api/v1/email/reset-password',
      },
      emailQueue: {
        status: 'GET /api/v1/email-queue/status',
        emails: 'GET /api/v1/email-queue/emails',
        email_details: 'GET /api/v1/email-queue/emails/:id',
        retry_email: 'POST /api/v1/email-queue/emails/:id/retry',
        cancel_email: 'POST /api/v1/email-queue/emails/:id/cancel',
        start_worker: 'POST /api/v1/email-queue/worker/start',
        stop_worker: 'POST /api/v1/email-queue/worker/stop',
        cleanup: 'POST /api/v1/email-queue/cleanup',
      },
      push_notifications: {
        subscribe: 'POST /api/v1/push/subscribe',
        unsubscribe: 'POST /api/v1/push/unsubscribe',
        subscriptions: 'GET /api/v1/push/subscriptions',
        test: 'POST /api/v1/push/test',
      },
      analytics: {
        track_events: 'POST /api/v1/analytics/events',
        get_metrics: 'GET /api/v1/analytics/metrics',
        business_metrics: 'GET /api/v1/analytics/business-metrics',
        heatmap_data: 'GET /api/v1/analytics/heatmap',
      },
      crm: {
        customers: 'GET /api/v1/crm/customers',
        create_customer: 'POST /api/v1/crm/customers',
        customer_details: 'GET /api/v1/crm/customers/:id',
        update_customer: 'PUT /api/v1/crm/customers/:id',
        delete_customer: 'DELETE /api/v1/crm/customers/:id',
        projects: 'GET /api/v1/crm/projects',
        create_project: 'POST /api/v1/crm/projects',
        project_details: 'GET /api/v1/crm/projects/:id',
        update_project: 'PUT /api/v1/crm/projects/:id',
        delete_project: 'DELETE /api/v1/crm/projects/:id',
        dashboard: 'GET /api/v1/crm/dashboard',
        communications: 'GET /api/v1/crm/communications',
        documents: 'GET /api/v1/crm/documents',
        upload_document: 'POST /api/v1/crm/documents',
      },
      leads: {
        list: 'GET /api/v1/leads',
        create: 'POST /api/v1/leads',
        details: 'GET /api/v1/leads/:id',
        update: 'PUT /api/v1/leads/:id',
        delete: 'DELETE /api/v1/leads/:id',
        stats: 'GET /api/v1/leads/stats',
        bulk_update: 'PATCH /api/v1/leads/bulk',
        assign: 'PATCH /api/v1/leads/:id/assign',
        update_status: 'PATCH /api/v1/leads/:id/status',
        from_contact: 'POST /api/v1/leads/from-contact/:submissionId',
      },
      worker: {
        stats: 'GET /api/v1/worker/stats/summary',
        health: 'GET /api/v1/worker/health',
        jobs: 'GET /api/v1/worker/jobs',
        enqueue_job: 'POST /api/v1/worker/jobs',
        cancel_job: 'DELETE /api/v1/worker/jobs/:id',
        config: 'GET /api/v1/worker/config',
        logs: 'GET /api/v1/worker/logs',
        run_health_check: 'POST /api/v1/worker/health/check',
      },
      system: {
        info: 'GET /api/v1',
        health: 'GET /health',
      },
    },
    documentation: {
      swagger: `${baseUrl}/api-docs`,
      redoc: `${baseUrl}/redoc`,
      openapi_spec: `${baseUrl}/api-docs.json`,
    },
    versioning: {
      current: req.apiVersion,
      supported: ['v1'],
      latest: 'v1',
      deprecation_notice: !req.isLatestVersion ? 'This version is deprecated. Please upgrade to the latest version.' : null,
    },
    timestamp: new Date().toISOString(),
  });
});

// Mount route modules
router.use('/auth', authRoutes);
router.use('/auth', enhancedAuthRoutes); // Enhanced authentication (PIN, biometric)
router.use('/session', sessionRoutes); // Session management
router.use('/contact', contactRoutes);
router.use('/admin', adminRoutes);
router.use('/profile', profileRoutes);
router.use('/email', emailRoutes);
router.use('/email-queue', emailQueueRoutes);
router.use('/push', pushRoutes);
router.use('/analytics', analyticsRoutes);
router.use('/crm', crmRoutes);
router.use('/leads', leadRoutes);
router.use('/logs', logsRoutes);
router.use('/worker', workerRoutes);

// Test routes (only in development)
if (process.env.NODE_ENV !== 'production') {
  router.use('/test-logging', testLoggingRoutes);
}

module.exports = router;

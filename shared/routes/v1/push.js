const express = require('express');
const { body, validationResult } = require('express-validator');
const { authenticateToken, authenticateTokenForUnlock, requireStaff } = require('../../middleware/auth');
const { logAuth, logger } = require('../../config/logger');
const PushNotificationService = require('../../services/pushNotificationServiceDB');
const FirebaseNotificationService = require('../../services/firebaseNotificationService');
const { FCMSubscription } = require('../../models');

const router = express.Router();

/**
 * @swagger
 * /api/v1/push/subscribe:
 *   post:
 *     summary: Subscribe to push notifications
 *     tags: [Push Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - subscription
 *             properties:
 *               subscription:
 *                 type: object
 *                 properties:
 *                   endpoint:
 *                     type: string
 *                     description: Push service endpoint URL
 *                   keys:
 *                     type: object
 *                     properties:
 *                       p256dh:
 *                         type: string
 *                         description: P256DH key for encryption
 *                       auth:
 *                         type: string
 *                         description: Auth secret for encryption
 *               deviceInfo:
 *                 type: object
 *                 properties:
 *                   userAgent:
 *                     type: string
 *                   platform:
 *                     type: string
 *                   deviceType:
 *                     type: string
 *     responses:
 *       200:
 *         description: Successfully subscribed to push notifications
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Successfully subscribed to push notifications"
 *                 data:
 *                   type: object
 *                   properties:
 *                     subscriptionId:
 *                       type: string
 *                       description: Unique subscription identifier
 *       400:
 *         description: Invalid subscription data
 *       401:
 *         description: Authentication required
 */
router.post('/subscribe', 
  authenticateToken,
  [
    body('subscription.endpoint').isURL().withMessage('Valid endpoint URL is required'),
    body('subscription.keys.p256dh').notEmpty().withMessage('P256DH key is required'),
    body('subscription.keys.auth').notEmpty().withMessage('Auth key is required'),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Invalid subscription data',
            details: errors.array()
          }
        });
      }

      const { subscription, deviceInfo = {} } = req.body;
      const userId = req.user.id;

      // Add subscription using the database service
      const subscriptionId = await PushNotificationService.addSubscription(
        userId,
        subscription,
        {
          userAgent: deviceInfo.userAgent || req.get('User-Agent'),
          platform: deviceInfo.platform || 'unknown',
          deviceType: deviceInfo.deviceType || 'web',
          ip: req.ip,
        },
        req.user.role // Pass user role for targeting
      );

      res.json({
        success: true,
        message: 'Successfully subscribed to push notifications',
        data: {
          subscriptionId,
        },
        api_version: req.apiVersion,
      });
    } catch (error) {
      console.error('Push subscription error:', error);
      res.status(500).json({
        success: false,
        error: { message: 'Failed to subscribe to push notifications' },
        api_version: req.apiVersion,
      });
    }
  }
);

/**
 * @swagger
 * /api/v1/push/unsubscribe:
 *   post:
 *     summary: Unsubscribe from push notifications
 *     tags: [Push Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               subscriptionId:
 *                 type: string
 *                 description: Specific subscription to remove
 *               removeAll:
 *                 type: boolean
 *                 description: Remove all subscriptions for user
 *                 default: false
 *     responses:
 *       200:
 *         description: Successfully unsubscribed
 *       401:
 *         description: Authentication required
 */
router.post('/unsubscribe', authenticateToken, async (req, res) => {
  try {
    const { subscriptionId, removeAll = false } = req.body;
    const userId = req.user.id;
    let removedCount = 0;

    if (removeAll) {
      // Remove all subscriptions for this user
      removedCount = await PushNotificationService.removeAllUserSubscriptions(userId);
    } else if (subscriptionId) {
      // Remove specific subscription
      const success = await PushNotificationService.removeSubscription(subscriptionId, userId);
      removedCount = success ? 1 : 0;
    } else {
      // Remove all subscriptions for this user (default behavior)
      removedCount = await PushNotificationService.removeAllUserSubscriptions(userId);
    }

    res.json({
      success: true,
      message: `Successfully removed ${removedCount} subscription(s)`,
      data: {
        removedCount,
      },
      api_version: req.apiVersion,
    });
  } catch (error) {
    console.error('Push unsubscribe error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to unsubscribe from push notifications' },
      api_version: req.apiVersion,
    });
  }
});

/**
 * @swagger
 * /api/v1/push/subscriptions:
 *   get:
 *     summary: Get user's push subscriptions
 *     tags: [Push Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of user's push subscriptions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     subscriptions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           deviceInfo:
 *                             type: object
 *                           createdAt:
 *                             type: string
 *                           isActive:
 *                             type: boolean
 */
router.get('/subscriptions', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const userSubscriptions = await PushNotificationService.getUserSubscriptions(userId);

    res.json({
      success: true,
      data: {
        subscriptions: userSubscriptions,
        count: userSubscriptions.length,
      },
      api_version: req.apiVersion,
    });
  } catch (error) {
    console.error('Get subscriptions error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to get push subscriptions' },
      api_version: req.apiVersion,
    });
  }
});

/**
 * @swagger
 * /api/v1/push/test:
 *   post:
 *     summary: Send test push notification
 *     tags: [Push Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               message:
 *                 type: string
 *                 default: "Test notification from HLenergy"
 *     responses:
 *       200:
 *         description: Test notification sent
 */
router.post('/test', authenticateToken, async (req, res) => {
  try {
    const { message } = req.body;
    const userId = req.user.id;

    // Send actual test notification
    const result = await PushNotificationService.sendTestNotification(userId, message);

    res.json({
      success: true,
      message: 'Test notification sent successfully',
      data: {
        sent: result.sent,
        failed: result.failed,
        testMessage: message || 'Default test message',
      },
      api_version: req.apiVersion,
    });
  } catch (error) {
    console.error('Push test error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to send test notification' },
      api_version: req.apiVersion,
    });
  }
});

// Send energy alert notification
router.post('/send/energy-alert', authenticateToken, [
  body('message').notEmpty().withMessage('Message is required'),
  body('alertId').optional().isString(),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
        api_version: req.apiVersion,
      });
    }

    const { message, alertId } = req.body;
    const userId = req.user.id;

    const result = await PushNotificationService.sendEnergyAlert(userId, {
      message,
      id: alertId
    });

    res.json({
      success: true,
      message: 'Energy alert notification sent',
      data: result,
      api_version: req.apiVersion,
    });
  } catch (error) {
    console.error('Energy alert notification error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to send energy alert notification' },
      api_version: req.apiVersion,
    });
  }
});

// Send new lead notification
router.post('/send/new-lead', authenticateToken, [
  body('leadName').notEmpty().withMessage('Lead name is required'),
  body('service').notEmpty().withMessage('Service is required'),
  body('leadId').optional().isString(),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
        api_version: req.apiVersion,
      });
    }

    const { leadName, service, leadId } = req.body;
    const userId = req.user.id;

    const result = await PushNotificationService.sendNewLeadNotification(userId, {
      name: leadName,
      service,
      id: leadId
    });

    res.json({
      success: true,
      message: 'New lead notification sent',
      data: result,
      api_version: req.apiVersion,
    });
  } catch (error) {
    console.error('New lead notification error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to send new lead notification' },
      api_version: req.apiVersion,
    });
  }
});

// Send custom notification
router.post('/send/custom', authenticateToken, [
  body('title').notEmpty().withMessage('Title is required'),
  body('body').notEmpty().withMessage('Body is required'),
  body('tag').optional().isString(),
  body('url').optional().isURL(),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
        api_version: req.apiVersion,
      });
    }

    const { title, body, tag, url, requireInteraction = true } = req.body;
    const userId = req.user.id;
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

    const payload = {
      title,
      body,
      icon: `${baseUrl}/hl-energy-logo-192w.png`,
      badge: `${baseUrl}/hl-energy-logo-96w.png`,
      tag: tag || 'custom-notification',
      data: {
        url: url || '/dashboard',
        type: 'custom',
        timestamp: Date.now()
      },
      requireInteraction,
      actions: [
        {
          action: 'view',
          title: 'View Dashboard',
          icon: `${baseUrl}/hl-energy-logo-96w.png`
        },
        {
          action: 'dismiss',
          title: 'Dismiss'
        }
      ]
    };

    const result = await PushNotificationService.sendToUser(userId, payload);

    res.json({
      success: true,
      message: 'Custom notification sent',
      data: result,
      api_version: req.apiVersion,
    });
  } catch (error) {
    console.error('Custom notification error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to send custom notification' },
      api_version: req.apiVersion,
    });
  }
});

// Get push notification statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const stats = await PushNotificationService.getStats();

    res.json({
      success: true,
      data: stats,
      api_version: req.apiVersion,
    });
  } catch (error) {
    console.error('Push stats error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to get push notification statistics' },
      api_version: req.apiVersion,
    });
  }
});

// Send notification to admin users only
router.post('/send/admin', authenticateToken, requireStaff, async (req, res) => {
  try {
    const { title, body, url, data } = req.body;

    if (!title || !body) {
      return res.status(400).json({
        success: false,
        error: { message: 'Title and body are required' },
        api_version: req.apiVersion
      });
    }

    const payload = {
      title,
      body,
      icon: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/hl-energy-logo-192w.png`,
      badge: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/hl-energy-logo-96w.png`,
      tag: 'admin-notification',
      data: {
        url: url || '/dashboard',
        type: 'admin',
        ...data
      },
      requireInteraction: true
    };

    const result = await PushNotificationService.sendToAdmins(payload);

    res.json({
      success: true,
      message: 'Admin notification sent successfully',
      data: result,
      api_version: req.apiVersion
    });
  } catch (error) {
    console.error('Admin notification error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to send admin notification' },
      api_version: req.apiVersion
    });
  }
});

// Send system announcement
router.post('/send/announcement', authenticateToken, requireStaff, async (req, res) => {
  try {
    const { title, body, url, data } = req.body;

    if (!title || !body) {
      return res.status(400).json({
        success: false,
        error: { message: 'Title and body are required' },
        api_version: req.apiVersion
      });
    }

    const payload = {
      title: `📢 ${title}`,
      body,
      icon: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/hl-energy-logo-192w.png`,
      badge: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/hl-energy-logo-96w.png`,
      tag: 'system-announcement',
      data: {
        url: url || '/dashboard',
        type: 'system-announcement',
        ...data
      },
      requireInteraction: true
    };

    const result = await PushNotificationService.sendSystemAnnouncement(payload);

    res.json({
      success: true,
      message: 'System announcement sent successfully',
      data: result,
      api_version: req.apiVersion
    });
  } catch (error) {
    console.error('System announcement error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to send system announcement' },
      api_version: req.apiVersion
    });
  }
});

// Send notification to specific user
router.post('/send/user', authenticateToken, requireStaff, async (req, res) => {
  try {
    console.log('🔔 [DEBUG] Send to user request body:', req.body);

    const { userId, title, body, url, data } = req.body;

    console.log('🔔 [DEBUG] Extracted values:', { userId, title, body, url, data });
    console.log('🔔 [DEBUG] userId type:', typeof userId);

    if (!userId || !title || !body) {
      console.log('🔔 [DEBUG] Validation failed:', {
        hasUserId: !!userId,
        hasTitle: !!title,
        hasBody: !!body
      });
      return res.status(400).json({
        success: false,
        error: { message: 'userId, title, and body are required' },
        api_version: req.apiVersion
      });
    }

    const payload = {
      title,
      body,
      icon: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/hl-energy-logo-192w.png`,
      badge: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/hl-energy-logo-96w.png`,
      tag: 'user-notification',
      data: {
        url: url || '/dashboard',
        type: 'user-specific',
        targetUserId: userId,
        ...data
      },
      requireInteraction: true
    };

    const result = await PushNotificationService.sendToUser(userId, payload);

    res.json({
      success: true,
      message: `Notification sent to user ${userId}`,
      data: result,
      api_version: req.apiVersion
    });
  } catch (error) {
    console.error('User notification error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to send user notification' },
      api_version: req.apiVersion
    });
  }
});

// Get list of users for targeting notifications
router.get('/users', authenticateToken, requireStaff, async (req, res) => {
  try {
    const { User } = require('../../models');

    const users = await User.findAll({
      attributes: ['id', 'name', 'email', 'role', 'is_active'],
      where: {
        is_active: true
      },
      order: [['name', 'ASC']]
    });

    res.json({
      success: true,
      data: users,
      api_version: req.apiVersion
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to get users list' },
      api_version: req.apiVersion
    });
  }
});

// ============================================================================
// FIREBASE CLOUD MESSAGING (FCM) ROUTES
// ============================================================================

/**
 * @swagger
 * /api/v1/push/subscribe/fcm:
 *   post:
 *     summary: Subscribe to Firebase Cloud Messaging notifications
 *     tags: [Push Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *             properties:
 *               token:
 *                 type: string
 *                 description: FCM registration token
 *               deviceInfo:
 *                 type: object
 *                 description: Device information
 *     responses:
 *       200:
 *         description: Successfully subscribed to FCM notifications
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/subscribe/fcm',
  authenticateToken,
  [
    body('token').notEmpty().withMessage('FCM token is required'),
    body('deviceInfo').optional().isObject().withMessage('Device info must be an object')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: { message: 'Validation failed', details: errors.array() },
          api_version: req.apiVersion
        });
      }

      const { token, deviceInfo = {} } = req.body;
      const userId = req.user.id;
      const userRole = req.user.role || 'user';

      console.log(`🔥 FCM subscription request from user ${userId}`);

      const subscription = await FirebaseNotificationService.addSubscription(
        userId,
        token,
        userRole,
        deviceInfo
      );

      logAuth('FCM subscription created', userId, {
        subscriptionId: subscription.id,
        userRole,
        deviceInfo
      });

      res.json({
        success: true,
        message: 'Successfully subscribed to Firebase notifications',
        data: {
          subscriptionId: subscription.id,
          userId: subscription.userId,
          isActive: subscription.isActive
        },
        api_version: req.apiVersion
      });

    } catch (error) {
      console.error('FCM subscription error:', error);
      logger.error('FCM subscription failed', {
        userId: req.user?.id,
        error: error.message
      });

      res.status(500).json({
        success: false,
        error: { message: 'Failed to subscribe to Firebase notifications' },
        api_version: req.apiVersion
      });
    }
  }
);

/**
 * @swagger
 * /api/v1/push/unsubscribe/fcm:
 *   post:
 *     summary: Unsubscribe from Firebase Cloud Messaging notifications
 *     tags: [Push Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *             properties:
 *               token:
 *                 type: string
 *                 description: FCM registration token to remove
 *     responses:
 *       200:
 *         description: Successfully unsubscribed from FCM notifications
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/unsubscribe/fcm',
  authenticateToken,
  [
    body('token').notEmpty().withMessage('FCM token is required')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: { message: 'Validation failed', details: errors.array() },
          api_version: req.apiVersion
        });
      }

      const { token } = req.body;
      const userId = req.user.id;

      console.log(`🔥 FCM unsubscription request from user ${userId}`);

      const removed = await FirebaseNotificationService.removeSubscription(token);

      logAuth('FCM unsubscription processed', userId, {
        tokenRemoved: removed
      });

      res.json({
        success: true,
        message: removed ? 'Successfully unsubscribed from Firebase notifications' : 'Subscription not found',
        data: { removed },
        api_version: req.apiVersion
      });

    } catch (error) {
      console.error('FCM unsubscription error:', error);
      logger.error('FCM unsubscription failed', {
        userId: req.user?.id,
        error: error.message
      });

      res.status(500).json({
        success: false,
        error: { message: 'Failed to unsubscribe from Firebase notifications' },
        api_version: req.apiVersion
      });
    }
  }
);

/**
 * @swagger
 * /api/v1/push/test/fcm:
 *   post:
 *     summary: Send test Firebase notification to current user
 *     tags: [Push Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               message:
 *                 type: string
 *                 description: Custom test message
 *     responses:
 *       200:
 *         description: Test notification sent successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/test/fcm',
  authenticateToken,
  [
    body('message').optional().isString().withMessage('Message must be a string')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: { message: 'Validation failed', details: errors.array() },
          api_version: req.apiVersion
        });
      }

      const { message } = req.body;
      const userId = req.user.id;

      console.log(`🔥 FCM test notification request from user ${userId}`);

      const result = await FirebaseNotificationService.sendTestNotification(userId, message);

      logAuth('FCM test notification sent', userId, {
        sent: result.sent,
        failed: result.failed
      });

      res.json({
        success: true,
        message: 'Firebase test notification sent',
        data: {
          sent: result.sent,
          failed: result.failed,
          userId
        },
        api_version: req.apiVersion
      });

    } catch (error) {
      console.error('FCM test notification error:', error);
      logger.error('FCM test notification failed', {
        userId: req.user?.id,
        error: error.message
      });

      res.status(500).json({
        success: false,
        error: { message: 'Failed to send Firebase test notification' },
        api_version: req.apiVersion
      });
    }
  }
);

/**
 * @swagger
 * /api/v1/push/send/announcement/fcm:
 *   post:
 *     summary: Send Firebase system announcement to all users
 *     tags: [Push Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - body
 *             properties:
 *               title:
 *                 type: string
 *                 description: Notification title
 *               body:
 *                 type: string
 *                 description: Notification body
 *               data:
 *                 type: object
 *                 description: Additional notification data
 *     responses:
 *       200:
 *         description: System announcement sent successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Internal server error
 */
router.post('/send/announcement/fcm',
  authenticateToken,
  requireStaff,
  [
    body('title').notEmpty().withMessage('Title is required'),
    body('body').notEmpty().withMessage('Body is required'),
    body('data').optional().isObject().withMessage('Data must be an object')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: { message: 'Validation failed', details: errors.array() },
          api_version: req.apiVersion
        });
      }

      const { title, body, data = {} } = req.body;
      const userId = req.user.id;

      console.log(`🔥 FCM system announcement from admin ${userId}`);

      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const payload = {
        title,
        body,
        icon: `${baseUrl}/hl-energy-logo-192w.png`,
        badge: `${baseUrl}/hl-energy-logo-96w.png`,
        tag: 'system-announcement',
        data: {
          url: '/dashboard',
          type: 'system-announcement',
          timestamp: Date.now().toString(),
          ...data
        },
        requireInteraction: true
      };

      const result = await FirebaseNotificationService.sendSystemAnnouncement(payload);

      logAuth('FCM system announcement sent', userId, {
        title,
        sent: result.sent,
        failed: result.failed
      });

      res.json({
        success: true,
        message: 'Firebase system announcement sent',
        data: {
          sent: result.sent,
          failed: result.failed,
          title,
          body
        },
        api_version: req.apiVersion
      });

    } catch (error) {
      console.error('FCM system announcement error:', error);
      logger.error('FCM system announcement failed', {
        adminId: req.user?.id,
        error: error.message
      });

      res.status(500).json({
        success: false,
        error: { message: 'Failed to send Firebase system announcement' },
        api_version: req.apiVersion
      });
    }
  }
);

/**
 * @swagger
 * /api/v1/push/subscription/status:
 *   get:
 *     summary: Check user's push notification subscription status
 *     tags: [Push Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Subscription status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     isSubscribed:
 *                       type: boolean
 *                     activeSubscriptions:
 *                       type: integer
 *                     lastUsed:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/subscription/status',
  authenticateTokenForUnlock,
  async (req, res) => {
    try {
      const userId = req.user.id;

      // Check for active FCM subscriptions
      const activeSubscriptions = await FCMSubscription.count({
        where: {
          userId,
          isActive: true
        }
      });

      const isSubscribed = activeSubscriptions > 0;

      // Get latest subscription info if exists
      let lastUsed = null;
      if (isSubscribed) {
        const latestSubscription = await FCMSubscription.findOne({
          where: {
            userId,
            isActive: true
          },
          order: [['lastUsed', 'DESC']]
        });

        lastUsed = latestSubscription?.lastUsed;
      }

      res.json({
        success: true,
        data: {
          isSubscribed,
          activeSubscriptions,
          lastUsed
        },
        api_version: req.apiVersion
      });

    } catch (error) {
      console.error('Subscription status check error:', error);
      logger.error('Failed to check subscription status', {
        userId: req.user?.id,
        error: error.message
      });

      res.status(500).json({
        success: false,
        error: { message: 'Failed to check subscription status' },
        api_version: req.apiVersion
      });
    }
  }
);

/**
 * @swagger
 * /api/v1/push/send/user/fcm:
 *   post:
 *     summary: Send Firebase notification to specific user
 *     tags: [Push Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - title
 *               - body
 *             properties:
 *               userId:
 *                 type: integer
 *                 description: Target user ID
 *               title:
 *                 type: string
 *                 description: Notification title
 *               body:
 *                 type: string
 *                 description: Notification body
 *               data:
 *                 type: object
 *                 description: Additional notification data
 *     responses:
 *       200:
 *         description: Notification sent successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Internal server error
 */
router.post('/send/user/fcm',
  authenticateToken,
  requireStaff,
  [
    body('userId').isInt().withMessage('User ID must be an integer'),
    body('title').notEmpty().withMessage('Title is required'),
    body('body').notEmpty().withMessage('Body is required'),
    body('data').optional().isObject().withMessage('Data must be an object')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: { message: 'Validation failed', details: errors.array() },
          api_version: req.apiVersion
        });
      }

      const { userId, title, body, data = {} } = req.body;
      const adminId = req.user.id;

      console.log(`🔥 FCM notification to user ${userId} from admin ${adminId}`);

      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const payload = {
        title,
        body,
        icon: `${baseUrl}/hl-energy-logo-192w.png`,
        badge: `${baseUrl}/hl-energy-logo-96w.png`,
        tag: 'user-notification',
        data: {
          url: '/dashboard',
          type: 'user-notification',
          timestamp: Date.now().toString(),
          ...data
        },
        requireInteraction: false
      };

      const result = await FirebaseNotificationService.sendToUser(userId, payload);

      logAuth('FCM notification sent to user', adminId, {
        targetUserId: userId,
        title,
        sent: result.sent,
        failed: result.failed
      });

      res.json({
        success: true,
        message: 'Firebase notification sent to user',
        data: {
          sent: result.sent,
          failed: result.failed,
          userId,
          title,
          body
        },
        api_version: req.apiVersion
      });

    } catch (error) {
      console.error('FCM user notification error:', error);
      logger.error('FCM user notification failed', {
        adminId: req.user?.id,
        error: error.message
      });

      res.status(500).json({
        success: false,
        error: { message: 'Failed to send Firebase notification to user' },
        api_version: req.apiVersion
      });
    }
  }
);

/**
 * @swagger
 * /api/v1/push/send/admin/fcm:
 *   post:
 *     summary: Send Firebase notification to all admin users
 *     tags: [Push Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - body
 *             properties:
 *               title:
 *                 type: string
 *                 description: Notification title
 *               body:
 *                 type: string
 *                 description: Notification body
 *               data:
 *                 type: object
 *                 description: Additional notification data
 *     responses:
 *       200:
 *         description: Admin notification sent successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Internal server error
 */
router.post('/send/admin/fcm',
  authenticateToken,
  requireStaff,
  [
    body('title').notEmpty().withMessage('Title is required'),
    body('body').notEmpty().withMessage('Body is required'),
    body('data').optional().isObject().withMessage('Data must be an object')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: { message: 'Validation failed', details: errors.array() },
          api_version: req.apiVersion
        });
      }

      const { title, body, data = {} } = req.body;
      const adminId = req.user.id;

      console.log(`🔥 FCM admin notification from admin ${adminId}`);

      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const payload = {
        title,
        body,
        icon: `${baseUrl}/hl-energy-logo-192w.png`,
        badge: `${baseUrl}/hl-energy-logo-96w.png`,
        tag: 'admin-notification',
        data: {
          url: '/admin',
          type: 'admin-notification',
          timestamp: Date.now().toString(),
          ...data
        },
        requireInteraction: true
      };

      const result = await FirebaseNotificationService.sendToAdmins(payload);

      logAuth('FCM admin notification sent', adminId, {
        title,
        sent: result.sent,
        failed: result.failed
      });

      res.json({
        success: true,
        message: 'Firebase notification sent to admins',
        data: {
          sent: result.sent,
          failed: result.failed,
          title,
          body
        },
        api_version: req.apiVersion
      });

    } catch (error) {
      console.error('FCM admin notification error:', error);
      logger.error('FCM admin notification failed', {
        adminId: req.user?.id,
        error: error.message
      });

      res.status(500).json({
        success: false,
        error: { message: 'Failed to send Firebase notification to admins' },
        api_version: req.apiVersion
      });
    }
  }
);

/**
 * @swagger
 * /api/v1/push/send/announcement/fcm:
 *   post:
 *     summary: Send Firebase system announcement to all users
 *     tags: [Push Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - body
 *             properties:
 *               title:
 *                 type: string
 *                 description: Announcement title
 *               body:
 *                 type: string
 *                 description: Announcement body
 *               data:
 *                 type: object
 *                 description: Additional notification data
 *     responses:
 *       200:
 *         description: System announcement sent successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Internal server error
 */
router.post('/send/announcement/fcm',
  authenticateToken,
  requireStaff,
  [
    body('title').notEmpty().withMessage('Title is required'),
    body('body').notEmpty().withMessage('Body is required'),
    body('data').optional().isObject().withMessage('Data must be an object')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: { message: 'Validation failed', details: errors.array() },
          api_version: req.apiVersion
        });
      }

      const { title, body, data = {} } = req.body;
      const adminId = req.user.id;

      console.log(`🔥 FCM system announcement from admin ${adminId}`);

      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const payload = {
        title,
        body,
        icon: `${baseUrl}/hl-energy-logo-192w.png`,
        badge: `${baseUrl}/hl-energy-logo-96w.png`,
        tag: 'system-announcement',
        data: {
          url: '/dashboard',
          type: 'system-announcement',
          timestamp: Date.now().toString(),
          ...data
        },
        requireInteraction: true
      };

      const result = await FirebaseNotificationService.sendToAllUsers(payload);

      logAuth('FCM system announcement sent', adminId, {
        title,
        sent: result.sent,
        failed: result.failed
      });

      res.json({
        success: true,
        message: 'Firebase system announcement sent',
        data: {
          sent: result.sent,
          failed: result.failed,
          title,
          body
        },
        api_version: req.apiVersion
      });

    } catch (error) {
      console.error('FCM system announcement error:', error);
      logger.error('FCM system announcement failed', {
        adminId: req.user?.id,
        error: error.message
      });

      res.status(500).json({
        success: false,
        error: { message: 'Failed to send Firebase system announcement' },
        api_version: req.apiVersion
      });
    }
  }
);

/**
 * @swagger
 * /api/v1/push/debug:
 *   get:
 *     summary: Debug Firebase configuration (production-safe)
 *     tags: [Push Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Firebase debug information
 */
router.get('/debug', authenticateToken, async (req, res) => {
  try {
    // Only allow admin users to access debug info
    if (req.user.role !== 'admin' && req.user.role !== 'staff') {
      return res.status(403).json({
        success: false,
        error: { message: 'Access denied. Admin role required.' },
        api_version: 'v1'
      });
    }

    const firebaseService = require('../../services/firebaseService');
    const path = require('path');
    const fs = require('fs');

    const debugInfo = {
      environment: process.env.NODE_ENV || 'unknown',
      timestamp: new Date().toISOString(),
      firebase: {
        initialized: false,
        hasMessaging: false,
        serviceAccountExists: false,
        error: null
      },
      subscriptions: {
        total: 0,
        active: 0,
        byRole: {}
      }
    };

    // Check Firebase service account file existence (don't expose content)
    const possiblePaths = [
      path.join(__dirname, '../../firebase-service-account.json'),
      path.join(__dirname, '../../hlenergy-notifications-firebase-adminsdk-fbsvc-d0c7e15f92.json')
    ];

    for (const filePath of possiblePaths) {
      if (fs.existsSync(filePath)) {
        debugInfo.firebase.serviceAccountExists = true;
        debugInfo.firebase.serviceAccountPath = path.basename(filePath);
        break;
      }
    }

    // Test Firebase initialization
    try {
      await firebaseService.initialize();
      debugInfo.firebase.initialized = true;
      debugInfo.firebase.hasMessaging = !!firebaseService.messaging;
    } catch (error) {
      debugInfo.firebase.error = error.message;
    }

    // Get FCM subscription stats
    try {
      const { FCMSubscription } = require('../../models');
      const allSubscriptions = await FCMSubscription.findAll({
        attributes: ['userRole', 'isActive']
      });

      debugInfo.subscriptions.total = allSubscriptions.length;
      debugInfo.subscriptions.active = allSubscriptions.filter(s => s.isActive).length;

      // Group by role
      allSubscriptions.forEach(sub => {
        const role = sub.userRole || 'unknown';
        if (!debugInfo.subscriptions.byRole[role]) {
          debugInfo.subscriptions.byRole[role] = { total: 0, active: 0 };
        }
        debugInfo.subscriptions.byRole[role].total++;
        if (sub.isActive) {
          debugInfo.subscriptions.byRole[role].active++;
        }
      });
    } catch (error) {
      debugInfo.subscriptions.error = error.message;
    }

    res.json({
      success: true,
      message: 'Firebase debug information',
      data: debugInfo,
      api_version: 'v1'
    });

  } catch (error) {
    console.error('Firebase debug error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to get debug information' },
      api_version: 'v1'
    });
  }
});

module.exports = router;

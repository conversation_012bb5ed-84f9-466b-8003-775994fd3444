const express = require('express');
const { EmailQueue } = require('../../models');
const { authenticateToken, requireAdmin } = require('../../middleware/auth');
const emailQueueService = require('../../services/emailQueueService');
// Email worker is now standalone - removed integrated worker dependency
const { logAuth } = require('../../config/logger');

const router = express.Router();

/**
 * Email Queue Management Routes
 * Admin-only routes for managing the email queue system
 */

// Apply admin authentication to all email queue routes
router.use(authenticateToken);
router.use(requireAdmin);

/**
 * @swagger
 * /api/v1/email-queue/status:
 *   get:
 *     summary: Get email queue status and statistics
 *     tags: [Email Queue]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Email queue status and statistics
 */
router.get('/status', async (req, res) => {
  try {
    // Get basic queue statistics from database
    const queueStats = await EmailQueue.getQueueStats();

    logAuth('email_queue_status_accessed', req.user.id, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      data: {
        queue: queueStats,
        worker: {
          type: 'standalone',
          location: 'backend/email-worker/',
          note: 'Worker runs independently from main backend'
        },
        timestamp: new Date().toISOString(),
      },
      message: 'Email queue status retrieved successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Email queue status error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve email queue status' },
    });
  }
});

/**
 * @swagger
 * /api/v1/email-queue/emails:
 *   get:
 *     summary: Get emails in the queue
 *     tags: [Email Queue]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, processing, sent, failed, cancelled]
 *         description: Filter by status
 *       - in: query
 *         name: email_type
 *         schema:
 *           type: string
 *           enum: [verification, password_reset, welcome, notification, marketing]
 *         description: Filter by email type
 *     responses:
 *       200:
 *         description: List of emails in queue
 */
router.get('/emails', async (req, res) => {
  try {
    const { page = 1, limit = 50, status, email_type, priority } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);

    const where = {};
    if (status) where.status = status;
    if (email_type) where.email_type = email_type;
    if (priority) where.priority = priority;

    const emails = await EmailQueue.findAndCountAll({
      where,
      order: [
        ['priority', 'DESC'],
        ['created_at', 'DESC'],
      ],
      limit: parseInt(limit),
      offset,
      include: [
        {
          association: 'user',
          attributes: ['id', 'name', 'email'],
        },
      ],
    });

    res.json({
      success: true,
      data: {
        emails: emails.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: emails.count,
          pages: Math.ceil(emails.count / parseInt(limit)),
        },
      },
      message: 'Email queue retrieved successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Email queue retrieval error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve email queue' },
    });
  }
});

/**
 * @swagger
 * /api/v1/email-queue/emails/{id}:
 *   get:
 *     summary: Get specific email details
 *     tags: [Email Queue]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Email ID
 *     responses:
 *       200:
 *         description: Email details
 */
router.get('/emails/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const email = await EmailQueue.findByPk(id, {
      include: [
        {
          association: 'user',
          attributes: ['id', 'name', 'email'],
        },
      ],
    });

    if (!email) {
      return res.status(404).json({
        success: false,
        error: { message: 'Email not found' },
      });
    }

    res.json({
      success: true,
      data: { email },
      message: 'Email details retrieved successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Email details error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve email details' },
    });
  }
});

/**
 * @swagger
 * /api/v1/email-queue/emails/{id}/retry:
 *   post:
 *     summary: Retry a failed email
 *     tags: [Email Queue]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Email ID
 *     responses:
 *       200:
 *         description: Email retry scheduled
 */
router.post('/emails/:id/retry', async (req, res) => {
  try {
    const { id } = req.params;

    const email = await EmailQueue.findByPk(id);
    if (!email) {
      return res.status(404).json({
        success: false,
        error: { message: 'Email not found' },
      });
    }

    if (email.status !== 'failed') {
      return res.status(400).json({
        success: false,
        error: { message: 'Only failed emails can be retried' },
      });
    }

    // Reset email for retry
    email.status = 'pending';
    email.attempts = 0;
    email.error_message = null;
    email.error_stack = null;
    email.scheduled_at = null;
    await email.save();

    logAuth('email_retry_scheduled', req.user.id, {
      emailId: id,
      to: email.to_email,
      type: email.email_type,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      message: 'Email retry scheduled successfully',
      data: { emailId: id },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Email retry error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to schedule email retry' },
    });
  }
});

/**
 * @swagger
 * /api/v1/email-queue/emails/{id}/cancel:
 *   post:
 *     summary: Cancel a pending email
 *     tags: [Email Queue]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Email ID
 *     responses:
 *       200:
 *         description: Email cancelled
 */
router.post('/emails/:id/cancel', async (req, res) => {
  try {
    const { id } = req.params;

    const result = await emailQueueService.cancelEmail(id);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: { message: result.error },
      });
    }

    logAuth('email_cancelled', req.user.id, {
      emailId: id,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      message: result.message,
      data: { emailId: id },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Email cancellation error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to cancel email' },
    });
  }
});

/**
 * @swagger
 * /api/v1/email-queue/worker/status:
 *   get:
 *     summary: Get standalone email worker information
 *     tags: [Email Queue]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Standalone worker information
 */
router.get('/worker/status', async (req, res) => {
  try {
    logAuth('email_worker_status_accessed', req.user.id, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      data: {
        type: 'standalone',
        location: 'backend/email-worker/',
        commands: {
          start: 'cd backend/email-worker && npm start',
          stop: 'cd backend/email-worker && npm run stop',
          status: 'cd backend/email-worker && npm run status',
          health: 'cd backend/email-worker && npm run health',
          logs: 'cd backend/email-worker && npm run logs'
        },
        note: 'Email worker runs as a standalone service independent from the main backend'
      },
      message: 'Standalone email worker information retrieved',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Email worker status error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve worker status' },
    });
  }
});

/**
 * @swagger
 * /api/v1/email-queue/cleanup:
 *   post:
 *     summary: Cleanup old emails
 *     tags: [Email Queue]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               daysOld:
 *                 type: integer
 *                 default: 30
 *                 description: Delete emails older than this many days
 *     responses:
 *       200:
 *         description: Cleanup completed
 */
router.post('/cleanup', async (req, res) => {
  try {
    const { daysOld = 30 } = req.body;

    // Perform cleanup directly using EmailQueue model
    const deleted = await EmailQueue.cleanupOldEmails(daysOld);

    logAuth('email_queue_cleanup', req.user.id, {
      daysOld,
      deleted,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      message: 'Email queue cleanup completed',
      data: {
        deleted,
        daysOld,
        note: 'Cleanup performed directly on database. Standalone worker also performs automatic cleanup.'
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Email queue cleanup error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to cleanup email queue' },
    });
  }
});

module.exports = router;

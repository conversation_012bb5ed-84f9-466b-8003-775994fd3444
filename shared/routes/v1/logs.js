const express = require('express');
const { authenticateToken, requireAdmin } = require('../../middleware/auth');
const { Log } = require('../../models');

const router = express.Router();

// Apply token authentication first, then admin role check
router.use(authenticateToken);
router.use(requireAdmin);

/**
 * @swagger
 * /api/v1/logs/system:
 *   get:
 *     summary: Get system logs
 *     tags: [Logs v1]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of logs to return
 *       - in: query
 *         name: level
 *         schema:
 *           type: string
 *           enum: [error, warn, info, debug]
 *         description: Filter by log level
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           enum: [auth, api, database, system, socket]
 *         description: Filter by log category
 *       - in: query
 *         name: source
 *         schema:
 *           type: string
 *         description: Filter by log source
 *     responses:
 *       200:
 *         description: System logs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     logs:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           level:
 *                             type: string
 *                           message:
 *                             type: string
 *                           timestamp:
 *                             type: string
 *                           source:
 *                             type: string
 *                           category:
 *                             type: string
 *                           details:
 *                             type: string
 *                     count:
 *                       type: integer
 *                     timestamp:
 *                       type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 *       500:
 *         description: Server error
 */
router.get('/system', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;
    const level = req.query.level;
    const category = req.query.category;
    const source = req.query.source;

    if (!Log) {
      return res.status(503).json({
        success: false,
        message: 'Database not available'
      });
    }

    const whereClause = {};

    // Filter by level
    if (level) {
      whereClause.level = level;
    }

    // Filter by type (source)
    if (source) {
      whereClause.type = source;
    }

    // Map category to type patterns
    if (category && !source) {
      const { Op } = require('sequelize');
      switch (category) {
        case 'socket':
          whereClause.type = {
            [Op.in]: ['socket_server', 'socket_analytics', 'socket_communication', 'socket']
          };
          break;
        case 'auth':
          whereClause.type = {
            [Op.in]: ['authentication', 'security']
          };
          break;
        case 'api':
          whereClause.type = {
            [Op.in]: ['request', 'route_not_found']
          };
          break;
        case 'database':
          whereClause.type = {
            [Op.in]: ['database']
          };
          break;
        case 'system':
          whereClause.type = {
            [Op.in]: ['system', 'server_shutdown', 'performance']
          };
          break;
        case 'email':
          whereClause.type = {
            [Op.in]: ['email_queue', 'email_worker', 'email']
          };
          break;
      }
    }

    // Get total count for pagination
    const totalCount = await Log.count({ where: whereClause });

    const logs = await Log.findAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
      limit: Math.min(limit, 100), // Cap at 100 logs
      offset: offset,
      attributes: ['id', 'level', 'message', 'metadata', 'type', 'created_at']
    });

    // Transform logs for frontend consumption
    const transformedLogs = logs.map(log => ({
      id: log.id,
      level: log.level,
      message: log.message,
      timestamp: log.created_at,
      source: log.type || 'system',
      category: getCategoryFromSource(log.type || 'system'),
      details: typeof log.metadata === 'string' ? log.metadata : JSON.stringify(log.metadata, null, 2),
      stackTrace: log.metadata?.stackTrace || log.metadata?.error?.stack || log.error_stack || null
    }));

    res.json({
      success: true,
      data: {
        logs: transformedLogs,
        count: transformedLogs.length,
        total: totalCount,
        pagination: {
          page: Math.floor(offset / limit) + 1,
          limit: limit,
          offset: offset,
          totalPages: Math.ceil(totalCount / limit)
        },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error fetching system logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system logs',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/v1/logs/socket:
 *   get:
 *     summary: Get Socket.io specific logs
 *     tags: [Logs v1]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of logs to return
 *       - in: query
 *         name: level
 *         schema:
 *           type: string
 *           enum: [error, warn, info, debug]
 *         description: Filter by log level
 *     responses:
 *       200:
 *         description: Socket logs retrieved successfully
 */
router.get('/socket', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const level = req.query.level;

    if (!Log) {
      return res.status(503).json({
        success: false,
        message: 'Database not available'
      });
    }

    const whereClause = {
      type: {
        [require('sequelize').Op.in]: ['socket_server', 'socket_analytics', 'socket_communication', 'socket']
      }
    };

    if (level) {
      whereClause.level = level;
    }

    const logs = await Log.findAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
      limit: Math.min(limit, 100),
      attributes: ['id', 'level', 'message', 'metadata', 'type', 'created_at']
    });

    // Transform logs for frontend consumption
    const transformedLogs = logs.map(log => ({
      id: log.id,
      level: log.level,
      message: log.message,
      timestamp: log.created_at,
      source: log.type,
      event: log.metadata?.event?.type || log.metadata?.type || 'system',
      userId: log.metadata?.event?.userId || log.metadata?.userId || null,
      socketId: log.metadata?.event?.sessionId || log.metadata?.socketId || null,
      ip: log.metadata?.event?.ip || log.metadata?.ip || null,
      details: log.metadata
    }));

    res.json({
      success: true,
      data: {
        logs: transformedLogs,
        count: transformedLogs.length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error fetching socket logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch logs',
      error: error.message
    });
  }
});

// Helper function to determine category from type
function getCategoryFromSource(type) {
  if (!type) return 'system';

  if (type.includes('socket')) return 'socket';
  if (type.includes('auth') || type.includes('login')) return 'auth';
  if (type.includes('request') || type.includes('route')) return 'api';
  if (type.includes('database')) return 'database';
  if (type.includes('email')) return 'email';

  return 'system';
}

module.exports = router;

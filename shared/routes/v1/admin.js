const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { User, ContactSubmission, RefreshToken, UserSession, Log } = require('../../models');
const { authenticateToken, requireAdmin, requireStaff } = require('../../middleware/auth');
const { logAuth, logSecurity } = require('../../config/logger');

const router = express.Router();

/**
 * Admin Routes
 * Provides administrative functionality for managing users, contacts, and system
 */

// Apply authentication to all admin routes
router.use(authenticateToken);

/**
 * @swagger
 * /api/v1/admin/auth-methods:
 *   get:
 *     summary: Get available authentication methods for admin user
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Available authentication methods
 *       403:
 *         description: Insufficient permissions
 */
router.get('/auth-methods', requireAdmin, async (req, res) => {
  try {
    const { AdminPin, BiometricAuth } = require('../../models');
    const userId = req.user.id;

    // Check for active PIN
    const hasPin = await AdminPin.findOne({
      where: {
        user_id: userId,
        is_active: true
      }
    });

    // Check for active biometric credentials
    const biometricCount = await BiometricAuth.count({
      where: {
        user_id: userId,
        is_active: true
      }
    });

    const methods = [];
    if (hasPin) methods.push('pin');
    if (biometricCount > 0) methods.push('biometric');

    res.json({
      success: true,
      data: {
        methods,
        hasPin: !!hasPin,
        biometricCount,
        setupComplete: methods.length > 0
      },
      message: 'Authentication methods retrieved successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Auth methods check error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to check authentication methods' },
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/dashboard/stats:
 *   get:
 *     summary: Get comprehensive dashboard stats cards data
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Comprehensive dashboard statistics for stats cards
 *       403:
 *         description: Insufficient permissions
 */
router.get('/dashboard/stats', requireAdmin, async (req, res) => {
  try {
    const { Op } = require('sequelize');
    const { EmailQueue, Lead } = require('../../models');

    // Time ranges for calculations
    const now = new Date();
    const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const last7d = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const last30d = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Parallel data fetching for better performance
    const [
      // User statistics
      totalUsers,
      activeUsers,
      newUsersToday,
      newUsersThisWeek,

      // Contact statistics
      totalContacts,
      newContacts,
      contactsToday,
      contactsThisWeek,

      // Lead statistics
      totalLeads,
      newLeadsToday,
      newLeadsThisWeek,
      hotLeads,

      // Email statistics
      emailsPending,
      emailsSent,
      emailsFailed,
      emailsToday,

      // System statistics
      totalLogs,
      errorLogsToday,
      warningLogsToday,
      activeSessions,

      // Performance metrics
      avgResponseTime,
      systemUptime
    ] = await Promise.all([
      // Users
      User.count(),
      User.count({ where: { is_active: true } }),
      User.count({ where: { created_at: { [Op.gte]: last24h } } }),
      User.count({ where: { created_at: { [Op.gte]: last7d } } }),

      // Contacts
      ContactSubmission.count(),
      ContactSubmission.count({ where: { status: 'new' } }),
      ContactSubmission.count({ where: { created_at: { [Op.gte]: last24h } } }),
      ContactSubmission.count({ where: { created_at: { [Op.gte]: last7d } } }),

      // Leads
      Lead.count().catch(() => 0), // Handle if Lead model doesn't exist
      Lead.count({ where: { created_at: { [Op.gte]: last24h } } }).catch(() => 0),
      Lead.count({ where: { created_at: { [Op.gte]: last7d } } }).catch(() => 0),
      Lead.count({ where: { priority: 'high' } }).catch(() => 0),

      // Emails
      EmailQueue.count({ where: { status: 'pending' } }).catch(() => 0),
      EmailQueue.count({ where: { status: 'sent' } }).catch(() => 0),
      EmailQueue.count({ where: { status: 'failed' } }).catch(() => 0),
      EmailQueue.count({ where: { created_at: { [Op.gte]: last24h } } }).catch(() => 0),

      // System
      Log.count(),
      Log.count({ where: { level: 'error', created_at: { [Op.gte]: last24h } } }),
      Log.count({ where: { level: 'warn', created_at: { [Op.gte]: last24h } } }),
      RefreshToken.count({ where: { is_revoked: false, expires_at: { [Op.gt]: now } } }),

      // Performance
      Log.findOne({
        attributes: [[require('sequelize').fn('AVG', require('sequelize').col('response_time')), 'avg_time']],
        where: { response_time: { [Op.ne]: null }, created_at: { [Op.gte]: last24h } },
        raw: true
      }).then(result => result?.avg_time || 0),
      Promise.resolve(process.uptime())
    ]);

    // Calculate trends (percentage change from previous period)
    const [
      usersLastWeek,
      contactsLastWeek,
      leadsLastWeek
    ] = await Promise.all([
      User.count({ where: { created_at: { [Op.between]: [new Date(last7d.getTime() - 7 * 24 * 60 * 60 * 1000), last7d] } } }),
      ContactSubmission.count({ where: { created_at: { [Op.between]: [new Date(last7d.getTime() - 7 * 24 * 60 * 60 * 1000), last7d] } } }),
      Lead.count({ where: { created_at: { [Op.between]: [new Date(last7d.getTime() - 7 * 24 * 60 * 60 * 1000), last7d] } } }).catch(() => 0)
    ]);

    // Calculate percentage changes
    const userTrend = usersLastWeek > 0 ? ((newUsersThisWeek - usersLastWeek) / usersLastWeek * 100) : 0;
    const contactTrend = contactsLastWeek > 0 ? ((contactsThisWeek - contactsLastWeek) / contactsLastWeek * 100) : 0;
    const leadTrend = leadsLastWeek > 0 ? ((newLeadsThisWeek - leadsLastWeek) / leadsLastWeek * 100) : 0;

    // System health score calculation
    const healthScore = Math.max(0, Math.min(100,
      100 - (errorLogsToday * 2) - (emailsFailed * 0.5) - (warningLogsToday * 0.5)
    ));

    // Response time in milliseconds
    const responseTime = Math.round(parseFloat(avgResponseTime) || 0);

    const statsData = {
      // User Statistics
      users: {
        total: totalUsers,
        active: activeUsers,
        newToday: newUsersToday,
        newThisWeek: newUsersThisWeek,
        trend: Math.round(userTrend * 100) / 100,
        growthRate: totalUsers > 0 ? Math.round((newUsersThisWeek / totalUsers * 100) * 100) / 100 : 0
      },

      // Contact Statistics
      contacts: {
        total: totalContacts,
        new: newContacts,
        today: contactsToday,
        thisWeek: contactsThisWeek,
        trend: Math.round(contactTrend * 100) / 100,
        conversionRate: totalContacts > 0 ? Math.round((totalLeads / totalContacts * 100) * 100) / 100 : 0
      },

      // Lead Statistics
      leads: {
        total: totalLeads,
        hot: hotLeads,
        newToday: newLeadsToday,
        newThisWeek: newLeadsThisWeek,
        trend: Math.round(leadTrend * 100) / 100,
        conversionRate: totalContacts > 0 ? Math.round((totalLeads / totalContacts * 100) * 100) / 100 : 0
      },

      // Email Statistics
      emails: {
        pending: emailsPending,
        sent: emailsSent,
        failed: emailsFailed,
        today: emailsToday,
        successRate: (emailsSent + emailsFailed) > 0 ? Math.round((emailsSent / (emailsSent + emailsFailed) * 100) * 100) / 100 : 100,
        queueHealth: emailsPending < 100 ? 'healthy' : emailsPending < 500 ? 'warning' : 'critical'
      },

      // System Health
      system: {
        health: healthScore,
        status: healthScore >= 90 ? 'excellent' : healthScore >= 75 ? 'good' : healthScore >= 50 ? 'warning' : 'critical',
        uptime: Math.round(systemUptime),
        uptimeFormatted: formatUptime(systemUptime),
        activeSessions: activeSessions,
        responseTime: responseTime,
        memoryUsage: Math.round((process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100),
        memoryUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024), // MB
        cpuUsage: Math.round(Math.random() * 20 + 10) // Simulated for now
      },

      // Log Statistics
      logs: {
        total: totalLogs,
        errors: errorLogsToday,
        warnings: warningLogsToday,
        info: Math.max(0, totalLogs - errorLogsToday - warningLogsToday),
        errorRate: totalLogs > 0 ? Math.round((errorLogsToday / totalLogs * 100) * 100) / 100 : 0
      },

      // Performance Metrics
      performance: {
        avgResponseTime: responseTime,
        requestsToday: Math.round(Math.random() * 1000 + 500), // Simulated
        errorRate: errorLogsToday,
        successRate: Math.max(95, 100 - errorLogsToday * 0.1),
        throughput: Math.round(Math.random() * 50 + 20) // Requests per second (simulated)
      },

      // Real-time metrics
      realtime: {
        onlineUsers: activeSessions,
        activeConnections: Math.round(Math.random() * 20 + 5), // Simulated socket connections
        eventsPerSecond: Math.round(Math.random() * 10 + 2),
        lastUpdated: new Date().toISOString()
      }
    };

    logAuth('admin_dashboard_stats_accessed', req.user.id, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      data: statsData,
      message: 'Dashboard statistics retrieved successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Admin dashboard stats error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve dashboard statistics' },
    });
  }
});

// Helper function to format uptime
function formatUptime(seconds) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
}

/**
 * @swagger
 * /api/v1/admin/dashboard:
 *   get:
 *     summary: Get admin dashboard statistics (legacy)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard statistics
 *       403:
 *         description: Insufficient permissions
 */
router.get('/dashboard', requireAdmin, async (req, res) => {
  try {
    const [
      totalUsers,
      activeUsers,
      totalContacts,
      newContacts,
      totalLogs,
      recentErrors,
      sessionStats,
    ] = await Promise.all([
      User.count(),
      User.count({ where: { is_active: true } }),
      ContactSubmission.count(),
      ContactSubmission.count({ where: { status: 'new' } }),
      Log.count(),
      Log.count({
        where: {
          level: 'error',
          created_at: {
            [require('sequelize').Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        }
      }),
      UserSession.getSessionStats(),
    ]);

    const usersByRole = await User.findAll({
      attributes: [
        'role',
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count'],
      ],
      group: ['role'],
      raw: true,
    });

    const contactsByStatus = await ContactSubmission.findAll({
      attributes: [
        'status',
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count'],
      ],
      group: ['status'],
      raw: true,
    });

    logAuth('admin_dashboard_accessed', req.user.id, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      data: {
        overview: {
          totalUsers,
          activeUsers,
          totalContacts,
          newContacts,
          totalLogs,
          recentErrors,
        },
        sessions: sessionStats,
        usersByRole: usersByRole.reduce((acc, item) => {
          acc[item.role] = parseInt(item.count);
          return acc;
        }, {}),
        contactsByStatus: contactsByStatus.reduce((acc, item) => {
          acc[item.status] = parseInt(item.count);
          return acc;
        }, {}),
      },
      message: 'Dashboard statistics retrieved successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Admin dashboard error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve dashboard statistics' },
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/users:
 *   get:
 *     summary: Get all users (admin only)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Items per page
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *         description: Filter by role
 *       - in: query
 *         name: active
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *     responses:
 *       200:
 *         description: List of users
 */
router.get('/users', [
  requireAdmin,
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('role').optional().isIn(['admin', 'staff', 'client']),
  query('active').optional().isBoolean(),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    const where = {};
    if (req.query.role) where.role = req.query.role;
    if (req.query.active !== undefined) where.is_active = req.query.active === 'true';

    const users = await User.findAndCountAll({
      where,
      limit,
      offset,
      order: [['created_at', 'DESC']],
      attributes: { exclude: ['password'] },
    });

    logAuth('admin_users_list_accessed', req.user.id, {
      filters: where,
      page,
      limit,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      data: {
        users: users.rows,
        pagination: {
          page,
          limit,
          total: users.count,
          pages: Math.ceil(users.count / limit),
        },
      },
      message: 'Users retrieved successfully',
    });
  } catch (error) {
    console.error('Admin users list error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve users' },
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/users/{id}:
 *   get:
 *     summary: Get user details (admin only)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: User details
 *       404:
 *         description: User not found
 */
router.get('/users/:id', requireAdmin, async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    
    const user = await User.findByPk(userId, {
      attributes: { exclude: ['password'] },
      include: [
        {
          model: ContactSubmission,
          as: 'contactSubmissions',
          limit: 5,
          order: [['created_at', 'DESC']],
        },
        {
          model: RefreshToken,
          as: 'refreshTokens',
          where: { is_revoked: false },
          required: false,
        },
      ],
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: { message: 'User not found' },
      });
    }

    // Get user's recent activity logs
    const recentLogs = await Log.findAll({
      where: { user_id: userId },
      limit: 10,
      order: [['created_at', 'DESC']],
    });

    logAuth('admin_user_details_accessed', req.user.id, {
      targetUserId: userId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      data: {
        user,
        recentActivity: recentLogs,
      },
      message: 'User details retrieved successfully',
    });
  } catch (error) {
    console.error('Admin user details error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve user details' },
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/users/{id}/status:
 *   patch:
 *     summary: Update user status (admin only)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               is_active:
 *                 type: boolean
 *               reason:
 *                 type: string
 *     responses:
 *       200:
 *         description: User status updated
 *       404:
 *         description: User not found
 */
router.patch('/users/:id/status', [
  requireAdmin,
  body('is_active').isBoolean(),
  body('reason').optional().isLength({ min: 1, max: 500 }),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
      });
    }

    const userId = parseInt(req.params.id);
    const { is_active, reason } = req.body;

    // Prevent admin from deactivating themselves
    if (userId === req.user.id && !is_active) {
      return res.status(400).json({
        success: false,
        error: { message: 'Cannot deactivate your own account' },
      });
    }

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: { message: 'User not found' },
      });
    }

    const oldStatus = user.is_active;
    await user.update({ is_active });

    // Revoke all refresh tokens if deactivating
    if (!is_active) {
      await RefreshToken.revokeAllUserTokens(userId, req.user.id);
    }

    logAuth('admin_user_status_changed', req.user.id, {
      targetUserId: userId,
      targetEmail: user.email,
      oldStatus,
      newStatus: is_active,
      reason: reason || 'No reason provided',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          is_active,
        },
      },
      message: `User ${is_active ? 'activated' : 'deactivated'} successfully`,
    });
  } catch (error) {
    console.error('Admin user status update error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to update user status' },
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/contacts:
 *   get:
 *     summary: Get all contact submissions (staff/admin)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by status
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *         description: Filter by priority
 *     responses:
 *       200:
 *         description: List of contact submissions
 */
router.get('/contacts', [
  requireStaff,
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['new', 'in_progress', 'resolved', 'closed']),
  query('priority').optional().isIn(['low', 'medium', 'high', 'urgent']),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    const where = {};
    if (req.query.status) where.status = req.query.status;
    if (req.query.priority) where.priority = req.query.priority;

    const contacts = await ContactSubmission.findAndCountAll({
      where,
      limit,
      offset,
      order: [['created_at', 'DESC']],
      include: [
        {
          model: User,
          as: 'assignedUser',
          attributes: ['id', 'name', 'email'],
        },
        {
          model: User,
          as: 'createdByUser',
          attributes: ['id', 'name', 'email'],
        },
      ],
    });

    logAuth('admin_contacts_list_accessed', req.user.id, {
      filters: where,
      page,
      limit,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      data: {
        contacts: contacts.rows,
        pagination: {
          page,
          limit,
          total: contacts.count,
          pages: Math.ceil(contacts.count / limit),
        },
      },
      message: 'Contact submissions retrieved successfully',
    });
  } catch (error) {
    console.error('Admin contacts list error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve contact submissions' },
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/contacts/{id}:
 *   patch:
 *     summary: Update contact submission (staff/admin)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [new, in_progress, resolved, closed]
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high, urgent]
 *               assigned_to:
 *                 type: integer
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Contact submission updated
 *       404:
 *         description: Contact submission not found
 */
router.patch('/contacts/:id', [
  requireStaff,
  body('status').optional().isIn(['new', 'in_progress', 'resolved', 'closed']),
  body('priority').optional().isIn(['low', 'medium', 'high', 'urgent']),
  body('assigned_to').optional().isInt(),
  body('notes').optional().isLength({ max: 5000 }),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: { message: 'Validation failed', details: errors.array() },
      });
    }

    const contactId = parseInt(req.params.id);
    const { status, priority, assigned_to, notes } = req.body;

    const contact = await ContactSubmission.findByPk(contactId);
    if (!contact) {
      return res.status(404).json({
        success: false,
        error: { message: 'Contact submission not found' },
      });
    }

    const updateData = {};
    if (status !== undefined) updateData.status = status;
    if (priority !== undefined) updateData.priority = priority;
    if (assigned_to !== undefined) updateData.assigned_to = assigned_to;
    if (notes !== undefined) {
      // Append notes with timestamp and user info
      const timestamp = new Date().toISOString();
      const newNote = `[${timestamp}] ${req.user.name}: ${notes}`;
      updateData.notes = contact.notes ? `${contact.notes}\n\n${newNote}` : newNote;
    }

    // Set resolved/closed timestamps
    if (status === 'resolved' && contact.status !== 'resolved') {
      updateData.resolved_at = new Date();
    }
    if (status === 'closed' && contact.status !== 'closed') {
      updateData.closed_at = new Date();
    }

    await contact.update(updateData);

    logAuth('admin_contact_updated', req.user.id, {
      contactId,
      changes: updateData,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      data: { contact },
      message: 'Contact submission updated successfully',
    });
  } catch (error) {
    console.error('Admin contact update error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to update contact submission' },
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/analytics:
 *   get:
 *     summary: Get comprehensive analytics (admin only)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [24h, 7d, 30d, 90d]
 *         description: Analytics timeframe
 *     responses:
 *       200:
 *         description: Comprehensive analytics data
 */
router.get('/analytics', requireAdmin, async (req, res) => {
  try {
    const { timeframe = '7d' } = req.query;
    const { Op } = require('sequelize');

    // Calculate date range
    let startDate;
    switch (timeframe) {
      case '24h':
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    }

    // User analytics
    const userRegistrations = await User.findAll({
      attributes: [
        [require('sequelize').fn('DATE', require('sequelize').col('created_at')), 'date'],
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count'],
      ],
      where: {
        created_at: { [Op.gte]: startDate },
      },
      group: [require('sequelize').fn('DATE', require('sequelize').col('created_at'))],
      order: [[require('sequelize').fn('DATE', require('sequelize').col('created_at')), 'ASC']],
      raw: true,
    });

    // Contact submissions analytics
    const contactSubmissions = await ContactSubmission.findAll({
      attributes: [
        [require('sequelize').fn('DATE', require('sequelize').col('created_at')), 'date'],
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count'],
        'status',
      ],
      where: {
        created_at: { [Op.gte]: startDate },
      },
      group: [
        require('sequelize').fn('DATE', require('sequelize').col('created_at')),
        'status',
      ],
      order: [[require('sequelize').fn('DATE', require('sequelize').col('created_at')), 'ASC']],
      raw: true,
    });

    // Authentication analytics
    const authEvents = await Log.findAll({
      attributes: [
        [require('sequelize').fn('DATE', require('sequelize').col('created_at')), 'date'],
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count'],
        'metadata',
      ],
      where: {
        type: 'authentication',
        created_at: { [Op.gte]: startDate },
      },
      group: [require('sequelize').fn('DATE', require('sequelize').col('created_at'))],
      order: [[require('sequelize').fn('DATE', require('sequelize').col('created_at')), 'ASC']],
      raw: true,
    });

    // Performance analytics
    const performanceMetrics = await Log.findAll({
      attributes: [
        [require('sequelize').fn('AVG', require('sequelize').col('response_time')), 'avg_response_time'],
        [require('sequelize').fn('MAX', require('sequelize').col('response_time')), 'max_response_time'],
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'request_count'],
        [require('sequelize').fn('DATE', require('sequelize').col('created_at')), 'date'],
      ],
      where: {
        type: 'request',
        created_at: { [Op.gte]: startDate },
        response_time: { [Op.ne]: null },
      },
      group: [require('sequelize').fn('DATE', require('sequelize').col('created_at'))],
      order: [[require('sequelize').fn('DATE', require('sequelize').col('created_at')), 'ASC']],
      raw: true,
    });

    logAuth('admin_analytics_accessed', req.user.id, {
      timeframe,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      data: {
        timeframe,
        analytics: {
          userRegistrations: userRegistrations.map(item => ({
            date: item.date,
            count: parseInt(item.count),
          })),
          contactSubmissions: contactSubmissions.reduce((acc, item) => {
            const date = item.date;
            if (!acc[date]) acc[date] = {};
            acc[date][item.status] = parseInt(item.count);
            return acc;
          }, {}),
          authEvents: authEvents.map(item => ({
            date: item.date,
            count: parseInt(item.count),
          })),
          performance: performanceMetrics.map(item => ({
            date: item.date,
            avgResponseTime: Math.round(parseFloat(item.avg_response_time) || 0),
            maxResponseTime: parseInt(item.max_response_time) || 0,
            requestCount: parseInt(item.request_count),
          })),
        },
      },
      message: 'Analytics data retrieved successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Admin analytics error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve analytics data' },
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/system/health:
 *   get:
 *     summary: Get comprehensive system health (admin only)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: System health information
 */
router.get('/system/health', requireAdmin, async (req, res) => {
  try {
    const { healthCheck } = require('../../config/database');
    const { getLoggingStats } = require('../../config/logger');

    // Database health
    const dbHealth = await healthCheck();

    // Logging system health
    const loggingStats = getLoggingStats();

    // System metrics
    const systemMetrics = {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      platform: process.platform,
      nodeVersion: process.version,
      pid: process.pid,
    };

    // Recent error count
    const { Op } = require('sequelize');
    const last24h = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentErrors = await Log.count({
      where: {
        level: 'error',
        created_at: { [Op.gte]: last24h },
      },
    });

    // Active sessions count
    const activeSessions = await RefreshToken.count({
      where: {
        is_revoked: false,
        expires_at: { [Op.gt]: new Date() },
      },
    });

    res.json({
      success: true,
      data: {
        database: dbHealth,
        logging: loggingStats,
        system: {
          ...systemMetrics,
          uptimeFormatted: formatUptime(systemMetrics.uptime),
          memoryFormatted: {
            used: `${Math.round(systemMetrics.memory.heapUsed / 1024 / 1024)}MB`,
            total: `${Math.round(systemMetrics.memory.heapTotal / 1024 / 1024)}MB`,
            external: `${Math.round(systemMetrics.memory.external / 1024 / 1024)}MB`,
          },
        },
        health: {
          recentErrors,
          activeSessions,
          status: dbHealth.status === 'healthy' && recentErrors < 10 ? 'healthy' : 'warning',
        },
      },
      message: 'System health retrieved successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('System health check error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve system health' },
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/alerts:
 *   get:
 *     summary: Get system alerts (admin only)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: System alerts
 */
router.get('/alerts', requireAdmin, async (req, res) => {
  try {
    const { Op } = require('sequelize');
    const alerts = [];

    // Memory usage alert
    const memoryUsage = process.memoryUsage();
    const memoryPercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    if (memoryPercent > 80) {
      alerts.push({
        id: 1,
        level: 'warning',
        title: 'High Memory Usage',
        message: `Memory usage is at ${memoryPercent.toFixed(1)}%. Consider optimizing or scaling.`,
        timestamp: new Date(),
        category: 'system'
      });
    }

    // Recent errors
    const last24h = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const errorCount = await Log.count({
      where: {
        level: 'error',
        created_at: { [Op.gte]: last24h },
      },
    });

    if (errorCount > 10) {
      alerts.push({
        id: 2,
        level: 'error',
        title: 'High Error Rate',
        message: `${errorCount} errors detected in the last 24 hours.`,
        timestamp: new Date(Date.now() - 600000),
        category: 'errors'
      });
    }

    // Low user activity
    const activeUsers = await RefreshToken.count({
      where: {
        is_revoked: false,
        expires_at: { [Op.gt]: new Date() },
      },
    });

    if (activeUsers < 5) {
      alerts.push({
        id: 3,
        level: 'info',
        title: 'Low User Activity',
        message: 'User activity is lower than usual. This might be normal for off-peak hours.',
        timestamp: new Date(Date.now() - 1800000),
        category: 'users'
      });
    }

    res.json({
      success: true,
      data: { alerts },
      message: 'System alerts retrieved successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('System alerts error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to fetch system alerts' },
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/activity:
 *   get:
 *     summary: Get live activity feed (admin only)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of activities to return
 *     responses:
 *       200:
 *         description: Live activity feed
 */
router.get('/activity', requireAdmin, async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const { Op } = require('sequelize');

    // Get recent logs for activity feed
    const recentLogs = await Log.findAll({
      where: {
        type: { [Op.in]: ['authentication', 'admin', 'user'] },
        created_at: { [Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000) },
      },
      order: [['created_at', 'DESC']],
      limit,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email'],
        },
      ],
    });

    // Transform logs into activity format
    const activities = recentLogs.map((log, index) => {
      let title, description, type, severity;

      switch (log.action) {
        case 'login':
          title = 'User Login';
          description = `${log.user?.name || 'Unknown user'} logged in`;
          type = 'user_login';
          severity = 'info';
          break;
        case 'logout':
          title = 'User Logout';
          description = `${log.user?.name || 'Unknown user'} logged out`;
          type = 'user_logout';
          severity = 'info';
          break;
        case 'admin_user_status_changed':
          title = 'User Status Changed';
          description = `Admin modified user status`;
          type = 'admin_action';
          severity = 'warning';
          break;
        case 'contact_created':
          title = 'New Contact Submission';
          description = 'New contact form submitted';
          type = 'contact';
          severity = 'info';
          break;
        default:
          title = 'System Activity';
          description = log.action || 'Unknown activity';
          type = 'system';
          severity = 'info';
      }

      return {
        id: log.id,
        type,
        title,
        description,
        timestamp: log.created_at,
        severity,
        user: log.user,
        metadata: log.metadata
      };
    });

    res.json({
      success: true,
      data: { activities },
      message: 'Live activity retrieved successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Live activity error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to fetch live activity' },
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/performance:
 *   get:
 *     summary: Get performance metrics (admin only)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [1h, 6h, 24h, 7d]
 *         description: Time period for metrics
 *     responses:
 *       200:
 *         description: Performance metrics
 */
router.get('/performance', requireAdmin, async (req, res) => {
  try {
    const period = req.query.period || '24h';
    const { Op } = require('sequelize');

    // Calculate time range
    let startDate;
    switch (period) {
      case '1h':
        startDate = new Date(Date.now() - 60 * 60 * 1000);
        break;
      case '6h':
        startDate = new Date(Date.now() - 6 * 60 * 60 * 1000);
        break;
      case '24h':
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
    }

    // Get performance data from logs
    const performanceData = await Log.findAll({
      attributes: [
        [require('sequelize').fn('DATE_FORMAT', require('sequelize').col('created_at'), '%Y-%m-%d %H:00:00'), 'hour'],
        [require('sequelize').fn('AVG', require('sequelize').col('response_time')), 'avg_response_time'],
        [require('sequelize').fn('MAX', require('sequelize').col('response_time')), 'max_response_time'],
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'request_count'],
      ],
      where: {
        type: 'request',
        created_at: { [Op.gte]: startDate },
        response_time: { [Op.ne]: null },
      },
      group: [require('sequelize').fn('DATE_FORMAT', require('sequelize').col('created_at'), '%Y-%m-%d %H:00:00')],
      order: [[require('sequelize').fn('DATE_FORMAT', require('sequelize').col('created_at'), '%Y-%m-%d %H:00:00'), 'ASC']],
      raw: true,
    });

    // Current system metrics
    const currentMetrics = {
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      uptime: process.uptime(),
      timestamp: new Date().toISOString()
    };

    // Calculate CPU and memory trends
    const memoryPercent = (currentMetrics.memory.heapUsed / currentMetrics.memory.heapTotal) * 100;

    res.json({
      success: true,
      data: {
        period,
        current: {
          memoryUsage: Math.round(memoryPercent * 100) / 100,
          memoryUsed: Math.round(currentMetrics.memory.heapUsed / 1024 / 1024),
          cpuUsage: Math.round((currentMetrics.cpu.user + currentMetrics.cpu.system) / 1000000 * 100) / 100,
          uptime: currentMetrics.uptime,
          timestamp: currentMetrics.timestamp
        },
        historical: performanceData.map(item => ({
          timestamp: item.hour,
          avgResponseTime: Math.round(parseFloat(item.avg_response_time) || 0),
          maxResponseTime: parseInt(item.max_response_time) || 0,
          requestCount: parseInt(item.request_count)
        }))
      },
      message: 'Performance metrics retrieved successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Performance metrics error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to fetch performance metrics' },
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/actions/{action}:
 *   post:
 *     summary: Execute quick admin actions (admin only)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: action
 *         required: true
 *         schema:
 *           type: string
 *           enum: [restart_server, clear_cache, backup_db, view_logs, security_scan, performance]
 *     responses:
 *       200:
 *         description: Action executed successfully
 */
router.post('/actions/:action', requireAdmin, async (req, res) => {
  try {
    const { action } = req.params;
    const { Op } = require('sequelize');
    let result = {};

    switch (action) {
      case 'clear_cache':
        // Simulate cache clearing
        result = {
          message: 'Cache cleared successfully',
          itemsCleared: Math.floor(Math.random() * 1000) + 100,
          memoryFreed: `${Math.floor(Math.random() * 50) + 10}MB`
        };
        break;

      case 'backup_db':
        // Simulate database backup
        result = {
          message: 'Database backup initiated',
          backupId: `backup_${Date.now()}`,
          estimatedTime: '5-10 minutes',
          size: `${Math.floor(Math.random() * 500) + 100}MB`
        };
        break;

      case 'security_scan':
        // Simulate security scan
        result = {
          message: 'Security scan initiated',
          scanId: `scan_${Date.now()}`,
          estimatedTime: '2-5 minutes',
          scope: 'Full system scan'
        };
        break;

      case 'performance':
        // Get current performance snapshot
        const memoryUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        result = {
          message: 'Performance snapshot captured',
          snapshot: {
            memory: {
              used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
              total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
              percent: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)
            },
            cpu: {
              user: Math.round(cpuUsage.user / 1000000),
              system: Math.round(cpuUsage.system / 1000000)
            },
            uptime: Math.round(process.uptime())
          }
        };
        break;

      case 'view_logs':
        // Get recent error logs
        const recentErrors = await Log.findAll({
          where: {
            level: 'error',
            created_at: { [Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000) },
          },
          order: [['created_at', 'DESC']],
          limit: 10,
        });
        result = {
          message: 'Recent error logs retrieved',
          errorCount: recentErrors.length,
          logs: recentErrors.map(log => ({
            id: log.id,
            message: log.message,
            timestamp: log.created_at,
            action: log.action
          }))
        };
        break;

      default:
        return res.status(400).json({
          success: false,
          error: { message: `Unknown action: ${action}` },
        });
    }

    // Log the admin action
    await Log.create({
      type: 'admin',
      action: `quick_action_${action}`,
      user_id: req.user.id,
      message: `Admin executed quick action: ${action}`,
      metadata: {
        action,
        result,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      },
    });

    res.json({
      success: true,
      data: {
        action,
        result,
        executedBy: {
          id: req.user.id,
          name: req.user.name,
          email: req.user.email
        }
      },
      message: `Action '${action}' executed successfully`,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Quick action error:', error);
    res.status(500).json({
      success: false,
      error: { message: `Failed to execute action: ${req.params.action}` },
    });
  }
});

// Helper function to format uptime
function formatUptime(seconds) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  return `${days}d ${hours}h ${minutes}m ${secs}s`;
}

/**
 * @swagger
 * /api/v1/admin/socket/metrics:
 *   get:
 *     summary: Get Socket.io metrics and statistics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Socket.io metrics
 */
router.get('/socket/metrics', requireAdmin, async (req, res) => {
  try {
    // Get the socket server instance
    const socketServer = req.app.get('socketServer');

    let metrics = {
      totalConnections: 0,
      activeConnections: 0,
      peakConnections: 0,
      totalEvents: 0,
      eventsPerSecond: 0,
      averageResponseTime: 0,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      lastActivity: new Date().toISOString(),
      events: {
        totalEvents: 0,
        eventsPerSecond: 0,
        averageResponseTime: 0
      },
      rooms: {
        totalRooms: 0,
        adminRooms: 0,
        userRooms: 0
      },
      performance: {
        memoryUsage: Math.round((process.memoryUsage().heapUsed / 1024 / 1024) * 100) / 100,
        cpuUsage: 0,
        averageResponseTime: 0
      }
    };

    if (socketServer && socketServer.io) {
      // Get real socket metrics
      const sockets = await socketServer.io.fetchSockets();
      const connectedUsers = socketServer.getConnectedUsers();

      // Active connections
      metrics.activeConnections = sockets.length;

      // Total connections (from connected users map)
      metrics.totalConnections = socketServer.connectedUsers.size;

      // Peak connections (estimate based on current + some buffer)
      metrics.peakConnections = Math.max(metrics.activeConnections, metrics.totalConnections);

      // Room statistics
      const rooms = socketServer.io.sockets.adapter.rooms;
      metrics.rooms.totalRooms = rooms.size;

      // Count admin and user rooms
      let adminRooms = 0;
      let userRooms = 0;

      for (const [roomName] of rooms) {
        if (roomName.startsWith('admin') || roomName === 'admin') {
          adminRooms++;
        } else if (roomName.startsWith('user:') || roomName.startsWith('role:')) {
          userRooms++;
        }
      }

      metrics.rooms.adminRooms = adminRooms;
      metrics.rooms.userRooms = userRooms;

      // Performance metrics
      const memUsage = process.memoryUsage();
      metrics.performance.memoryUsage = Math.round((memUsage.heapUsed / 1024 / 1024) * 100) / 100;

      // Events metrics (basic implementation)
      metrics.events.totalEvents = metrics.activeConnections * 10; // Estimate
      metrics.events.eventsPerSecond = Math.round(metrics.activeConnections * 0.5 * 100) / 100;
      metrics.events.averageResponseTime = Math.round(Math.random() * 50 + 10); // Simulated for now

      metrics.performance.averageResponseTime = metrics.events.averageResponseTime;

      // Legacy fields for backward compatibility
      metrics.totalEvents = metrics.events.totalEvents;
      metrics.eventsPerSecond = metrics.events.eventsPerSecond;
      metrics.averageLatency = metrics.events.averageResponseTime;
      metrics.averageResponseTime = metrics.events.averageResponseTime;

      console.log(`📊 Socket metrics: ${metrics.activeConnections} active connections, ${metrics.rooms.totalRooms} rooms`);
    } else {
      console.warn('Socket server not available for metrics');
    }

    res.json({
      success: true,
      data: metrics
    });

  } catch (error) {
    console.error('Error fetching socket metrics:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch socket metrics',
        type: 'SERVER_ERROR'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/email-worker/status:
 *   get:
 *     summary: Get email worker status and statistics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Email worker status
 */
router.get('/email-worker/status', requireAdmin, async (req, res) => {
  try {
    const { EmailQueue, WorkerStatus } = require('../../models');

    // Get queue statistics
    const [pending, processing, sent, failed] = await Promise.all([
      EmailQueue.count({ where: { status: 'pending' } }),
      EmailQueue.count({ where: { status: 'processing' } }),
      EmailQueue.count({ where: { status: 'sent' } }),
      EmailQueue.count({ where: { status: 'failed' } })
    ]);

    // Get recent emails
    const recentEmails = await EmailQueue.findAll({
      limit: 10,
      order: [['created_at', 'DESC']],
      attributes: ['id', 'to_email', 'subject', 'status', 'attempts', 'created_at', 'sent_at', 'failed_at', 'error_message']
    });

    // Get worker status from database
    let workerStatus = {
      isRunning: false,
      lastProcessed: null,
      processId: null,
      uptime: null,
      method: 'database',
      details: {},
      status: 'stopped',
      started_at: null,
      stopped_at: null,
      last_heartbeat: null,
      uptime_seconds: 0
    };

    try {
      // Get worker status from database
      const dbWorkerStatus = await WorkerStatus.findOne({
        where: { worker_type: 'email' }
      });

      if (dbWorkerStatus) {
        const now = new Date();
        const lastHeartbeat = dbWorkerStatus.last_heartbeat;

        // Check if worker is considered stale (no heartbeat for 2 minutes)
        const isStale = lastHeartbeat && (now - new Date(lastHeartbeat)) > 2 * 60 * 1000;

        if (isStale && dbWorkerStatus.status === 'running') {
          // Mark as stopped if stale
          await dbWorkerStatus.update({
            status: 'stopped',
            stopped_at: now,
          });
          workerStatus.isRunning = false;
          workerStatus.status = 'stopped';
        } else {
          workerStatus.isRunning = dbWorkerStatus.status === 'running';
          workerStatus.status = dbWorkerStatus.status;
        }

        workerStatus.processId = dbWorkerStatus.pid;
        workerStatus.started_at = dbWorkerStatus.started_at;
        workerStatus.stopped_at = dbWorkerStatus.stopped_at;
        workerStatus.last_heartbeat = dbWorkerStatus.last_heartbeat;
        workerStatus.uptime_seconds = dbWorkerStatus.uptime_seconds;
        workerStatus.processed_count = dbWorkerStatus.processed_count;
        workerStatus.error_count = dbWorkerStatus.error_count;
        workerStatus.last_error = dbWorkerStatus.last_error;
        workerStatus.metadata = dbWorkerStatus.metadata;

        // Calculate uptime string if running
        if (workerStatus.uptime_seconds > 0) {
          const hours = Math.floor(workerStatus.uptime_seconds / 3600);
          const minutes = Math.floor((workerStatus.uptime_seconds % 3600) / 60);
          const seconds = workerStatus.uptime_seconds % 60;

          if (hours > 0) {
            workerStatus.uptime = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
          } else {
            workerStatus.uptime = `${minutes}:${seconds.toString().padStart(2, '0')}`;
          }
        }

        // Set last processed time
        if (lastHeartbeat) {
          workerStatus.lastProcessed = lastHeartbeat;
        } else if (dbWorkerStatus.stopped_at) {
          workerStatus.lastProcessed = dbWorkerStatus.stopped_at;
        }

        workerStatus.details = {
          database_status: dbWorkerStatus.status,
          last_heartbeat_age: lastHeartbeat ? Math.round((now - new Date(lastHeartbeat)) / 1000) : null,
          is_stale: isStale
        };
      } else {
        // Create default worker status if not exists
        await WorkerStatus.create({
          worker_type: 'email',
          status: 'stopped',
          metadata: {
            version: '1.0.0',
            location: 'backend/email-worker/'
          }
        });
        workerStatus.details.created_default = true;
      }

    } catch (statusError) {
      console.error('Error checking worker status:', statusError);
      workerStatus.details.statusError = statusError.message;
    }

    // Additional health indicators
    const healthIndicators = {
      queueBacklog: pending > 50,
      oldFailures: failed > 10,
      recentFailures: 0,
      processingStuck: false
    };

    // Check for recent failures (last hour)
    const recentFailureCount = await EmailQueue.count({
      where: {
        status: 'failed',
        failed_at: {
          [require('sequelize').Op.gte]: new Date(Date.now() - 60 * 60 * 1000)
        }
      }
    });
    healthIndicators.recentFailures = recentFailureCount;

    // Check for stuck processing emails (processing for more than 10 minutes)
    const stuckProcessing = await EmailQueue.count({
      where: {
        status: 'processing',
        updated_at: {
          [require('sequelize').Op.lt]: new Date(Date.now() - 10 * 60 * 1000)
        }
      }
    });
    healthIndicators.processingStuck = stuckProcessing > 0;

    res.json({
      success: true,
      data: {
        status: workerStatus,
        statistics: {
          pending,
          processing,
          sent,
          failed,
          total: pending + processing + sent + failed
        },
        health: healthIndicators,
        recentEmails,
        worker: {
          location: 'backend/email-worker/',
          commands: {
            start: 'cd backend/email-worker && npm start',
            stop: 'cd backend/email-worker && npm run stop',
            status: 'cd backend/email-worker && npm run status',
            logs: 'cd backend/email-worker && npm run logs'
          }
        }
      }
    });

  } catch (error) {
    console.error('Error fetching email worker status:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch email worker status',
        type: 'SERVER_ERROR',
        details: error.message
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/email-worker/queue:
 *   get:
 *     summary: Get email queue with pagination and filters
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 */
router.get('/email-worker/queue', requireAdmin, async (req, res) => {
  try {
    const { EmailQueue } = require('../../models');
    const { page = 1, limit = 20, status, search } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    if (status) {
      whereClause.status = status;
    }

    if (search) {
      const { Op } = require('sequelize');
      whereClause[Op.or] = [
        { to_email: { [Op.like]: `%${search}%` } },
        { subject: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows } = await EmailQueue.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset,
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        items: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          totalPages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    console.error('Error fetching email queue:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch email queue',
        type: 'SERVER_ERROR'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/email-worker/retry/{id}:
 *   post:
 *     summary: Retry failed email
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 */
router.post('/email-worker/retry/:id', requireAdmin, async (req, res) => {
  try {
    const { EmailQueue } = require('../../models');
    const { id } = req.params;

    const email = await EmailQueue.findByPk(id);
    if (!email) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Email not found',
          type: 'NOT_FOUND'
        }
      });
    }

    // Reset email for retry
    await email.update({
      status: 'pending',
      attempts: 0,
      error_message: null,
      sent_at: null,
      failed_at: null
    });

    res.json({
      success: true,
      data: {
        message: 'Email queued for retry',
        email
      }
    });

  } catch (error) {
    console.error('Error retrying email:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to retry email',
        type: 'SERVER_ERROR'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/email-worker/clear-failed:
 *   post:
 *     summary: Clear all failed emails from queue
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 */
router.post('/email-worker/clear-failed', requireAdmin, async (req, res) => {
  try {
    const { EmailQueue } = require('../../models');

    const deletedCount = await EmailQueue.destroy({
      where: { status: 'failed' }
    });

    res.json({
      success: true,
      data: {
        message: `Cleared ${deletedCount} failed emails`,
        deletedCount
      }
    });

  } catch (error) {
    console.error('Error clearing failed emails:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to clear failed emails',
        type: 'SERVER_ERROR'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/sessions:
 *   get:
 *     summary: Get active user sessions
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of sessions per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, locked, expired, all]
 *           default: all
 *         description: Filter sessions by status
 *     responses:
 *       200:
 *         description: Active sessions retrieved successfully
 *       403:
 *         description: Insufficient permissions
 */
router.get('/sessions', requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 20, status = 'all' } = req.query;
    const offset = (page - 1) * limit;

    let whereCondition = {};
    const { Op } = require('sequelize');

    // Filter by status
    if (status === 'active') {
      whereCondition = {
        is_active: true,
        is_locked: false,
        expires_at: { [Op.gt]: new Date() }
      };
    } else if (status === 'locked') {
      whereCondition = {
        is_active: true,
        is_locked: true,
        expires_at: { [Op.gt]: new Date() }
      };
    } else if (status === 'expired') {
      whereCondition = {
        expires_at: { [Op.lt]: new Date() }
      };
    } else if (status !== 'all') {
      return res.status(400).json({
        success: false,
        error: { message: 'Invalid status filter' },
        code: 'INVALID_STATUS_FILTER',
      });
    }

    const { count, rows: sessions } = await UserSession.findAndCountAll({
      where: whereCondition,
      include: [
        {
          association: 'user',
          attributes: ['id', 'name', 'email', 'role'],
        },
      ],
      order: [['last_activity', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    });

    // Add computed status for each session
    const sessionsWithStatus = sessions.map(session => {
      let sessionStatus = 'active';
      if (session.expires_at < new Date()) {
        sessionStatus = 'expired';
      } else if (session.is_locked) {
        sessionStatus = 'locked';
      } else if (!session.is_active) {
        sessionStatus = 'terminated';
      }

      return {
        ...session.toJSON(),
        status: sessionStatus,
        minutes_since_activity: Math.floor((Date.now() - session.last_activity.getTime()) / (1000 * 60)),
      };
    });

    logAuth('admin_sessions_accessed', req.user.id, {
      page,
      limit,
      status,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      data: {
        sessions: sessionsWithStatus,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit),
        },
        stats: await UserSession.getSessionStats(),
      },
      message: 'Sessions retrieved successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Admin sessions error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve sessions' },
      code: 'SESSIONS_RETRIEVAL_ERROR',
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/sessions/{sessionId}/terminate:
 *   post:
 *     summary: Terminate a user session
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Session ID to terminate
 *     responses:
 *       200:
 *         description: Session terminated successfully
 *       404:
 *         description: Session not found
 *       403:
 *         description: Insufficient permissions
 */
router.post('/sessions/:sessionId/terminate', requireAdmin, async (req, res) => {
  try {
    const { sessionId } = req.params;

    const session = await UserSession.findByPk(sessionId, {
      include: [
        {
          association: 'user',
          attributes: ['id', 'name', 'email'],
        },
      ],
    });

    if (!session) {
      return res.status(404).json({
        success: false,
        error: { message: 'Session not found' },
        code: 'SESSION_NOT_FOUND',
      });
    }

    if (!session.is_active) {
      return res.json({
        success: true,
        data: { message: 'Session is already terminated' },
      });
    }

    // Terminate the session
    await session.terminate();

    logSecurity('Admin terminated user session', {
      adminId: req.user.id,
      adminEmail: req.user.email,
      targetUserId: session.user_id,
      targetUserEmail: session.user?.email,
      sessionId: session.id,
      sessionToken: session.session_token.substring(0, 20) + '...',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      data: {
        message: 'Session terminated successfully',
        session: {
          id: session.id,
          user: session.user,
          terminated_at: new Date().toISOString(),
        },
      },
    });
  } catch (error) {
    console.error('Session termination error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to terminate session' },
      code: 'SESSION_TERMINATION_ERROR',
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/sessions/cleanup:
 *   post:
 *     summary: Cleanup expired sessions
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Sessions cleaned up successfully
 *       403:
 *         description: Insufficient permissions
 */
router.post('/sessions/cleanup', requireAdmin, async (req, res) => {
  try {
    const expiredCount = await UserSession.cleanupExpiredSessions();
    const lockedCount = await UserSession.lockInactiveSessions();

    logAuth('admin_sessions_cleanup', req.user.id, {
      expiredSessionsCleaned: expiredCount,
      inactiveSessionsLocked: lockedCount,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      data: {
        message: 'Session cleanup completed',
        expired_sessions_cleaned: expiredCount,
        inactive_sessions_locked: lockedCount,
      },
    });
  } catch (error) {
    console.error('Session cleanup error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to cleanup sessions' },
      code: 'SESSION_CLEANUP_ERROR',
    });
  }
});

/**
 * @swagger
 * /api/v1/admin/user-dashboard/stats:
 *   get:
 *     summary: Get user dashboard statistics (simplified for regular users)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User dashboard statistics
 *       403:
 *         description: Insufficient permissions
 */
router.get('/user-dashboard/stats', authenticateToken, async (req, res) => {
  try {
    const { Op } = require('sequelize');
    const { EmailQueue, Lead } = require('../../models');

    // Time ranges for calculations
    const now = new Date();
    const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const last7d = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Basic stats that regular users can see
    const [
      totalUsers,
      totalContacts,
      totalLeads,
      newContactsToday,
      newLeadsToday,
      systemUptime
    ] = await Promise.all([
      User.count(),
      ContactSubmission.count(),
      Lead.count().catch(() => 0), // Handle if Lead model doesn't exist
      ContactSubmission.count({ where: { created_at: { [Op.gte]: last24h } } }),
      Lead.count({ where: { created_at: { [Op.gte]: last24h } } }).catch(() => 0),
      Promise.resolve(process.uptime())
    ]);

    // Calculate conversion rate
    const conversionRate = totalContacts > 0 ? (totalLeads / totalContacts * 100) : 0;

    // Simple stats for user dashboard
    const statsData = {
      users: {
        total: totalUsers,
        active: Math.round(totalUsers * 0.8), // Estimate 80% active
      },
      contacts: {
        total: totalContacts,
        today: newContactsToday,
      },
      leads: {
        total: totalLeads,
        today: newLeadsToday,
        qualified: Math.round(totalLeads * 0.3), // Estimate 30% qualified
      },
      system: {
        uptime: Math.round(systemUptime),
        status: 'healthy',
        responseTime: Math.round(Math.random() * 50 + 20), // Simulated
      },
      performance: {
        conversionRate: Math.round(conversionRate * 100) / 100,
        successRate: 98.5,
      }
    };

    res.json({
      success: true,
      data: statsData,
      message: 'User dashboard statistics retrieved successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('User dashboard stats error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve dashboard statistics' },
    });
  }
});

module.exports = router;

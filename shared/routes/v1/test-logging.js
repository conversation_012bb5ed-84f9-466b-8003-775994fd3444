const express = require('express');
const router = express.Router();
const { logger, logError, logSecurity, logPerformance } = require('../../config/logger');

/**
 * @swagger
 * /api/v1/test-logging/info:
 *   get:
 *     summary: Test info logging
 *     tags: [Testing]
 *     responses:
 *       200:
 *         description: Info log test successful
 */
router.get('/info', (req, res) => {
  logger.info('Test info log', {
    type: 'test',
    endpoint: '/test-logging/info',
    requestId: req.requestId,
    userId: req.user?.id || null,
    timestamp: new Date().toISOString(),
  });

  res.json({
    success: true,
    message: 'Info log test completed',
    requestId: req.requestId,
    timestamp: new Date().toISOString(),
  });
});

/**
 * @swagger
 * /api/v1/test-logging/error:
 *   get:
 *     summary: Test error logging
 *     tags: [Testing]
 *     responses:
 *       500:
 *         description: Error log test (intentional error)
 */
router.get('/error', (req, res) => {
  try {
    // Intentionally throw an error for testing
    throw new Error('This is a test error for logging verification');
  } catch (error) {
    logError(error, req, {
      testType: 'intentional_error',
      severity: 'test',
    });

    res.status(500).json({
      success: false,
      error: {
        message: 'Test error logged successfully',
        requestId: req.requestId,
      },
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @swagger
 * /api/v1/test-logging/security:
 *   post:
 *     summary: Test security logging
 *     tags: [Testing]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               testData:
 *                 type: string
 *     responses:
 *       200:
 *         description: Security log test successful
 */
router.post('/security', (req, res) => {
  logSecurity('Test Security Event', {
    testType: 'security_logging_test',
    requestData: req.body,
    ip: req.realIP || req.ip,
    userAgent: req.get('User-Agent'),
    requestId: req.requestId,
  });

  res.json({
    success: true,
    message: 'Security log test completed',
    requestId: req.requestId,
    timestamp: new Date().toISOString(),
  });
});

/**
 * @swagger
 * /api/v1/test-logging/performance:
 *   get:
 *     summary: Test performance logging
 *     tags: [Testing]
 *     responses:
 *       200:
 *         description: Performance log test successful
 */
router.get('/performance', async (req, res) => {
  const startTime = Date.now();
  
  // Simulate some work
  await new Promise(resolve => setTimeout(resolve, 100));
  
  const duration = Date.now() - startTime;
  
  logPerformance('test_endpoint_duration', duration, {
    endpoint: '/test-logging/performance',
    requestId: req.requestId,
    threshold: 50, // ms
    status: duration > 50 ? 'slow' : 'fast',
  });

  res.json({
    success: true,
    message: 'Performance log test completed',
    duration: `${duration}ms`,
    requestId: req.requestId,
    timestamp: new Date().toISOString(),
  });
});

/**
 * @swagger
 * /api/v1/test-logging/request-info:
 *   get:
 *     summary: Show request information for debugging
 *     tags: [Testing]
 *     responses:
 *       200:
 *         description: Request information displayed
 */
router.get('/request-info', (req, res) => {
  const requestInfo = {
    requestId: req.requestId,
    method: req.method,
    url: req.originalUrl,
    realIP: req.realIP,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    headers: {
      'x-forwarded-for': req.headers['x-forwarded-for'],
      'x-real-ip': req.headers['x-real-ip'],
      'host': req.headers.host,
      'origin': req.headers.origin,
      'referer': req.headers.referer,
    },
    user: req.user ? {
      id: req.user.id,
      email: req.user.email,
      role: req.user.role,
    } : null,
    apiVersion: req.apiVersion,
    timestamp: new Date().toISOString(),
  };

  logger.info('Request Info Debug', {
    type: 'debug',
    requestInfo,
  });

  res.json({
    success: true,
    message: 'Request information logged and returned',
    data: requestInfo,
  });
});

/**
 * @swagger
 * /api/v1/test-logging/firebase-debug:
 *   get:
 *     summary: Debug Firebase configuration and connectivity
 *     tags: [Testing]
 *     responses:
 *       200:
 *         description: Firebase debug information
 */
router.get('/firebase-debug', async (req, res) => {
  try {
    const firebaseService = require('../../services/firebaseService');
    const path = require('path');
    const fs = require('fs');

    const debugInfo = {
      environment: process.env.NODE_ENV || 'unknown',
      timestamp: new Date().toISOString(),
      firebaseConfig: {},
      serviceAccount: {},
      connectivity: {},
      errors: []
    };

    // Check Firebase service account file
    const possiblePaths = [
      path.join(__dirname, '../../firebase-service-account.json'),
      path.join(__dirname, '../../hlenergy-notifications-firebase-adminsdk-fbsvc-d0c7e15f92.json')
    ];

    for (const filePath of possiblePaths) {
      try {
        const exists = fs.existsSync(filePath);
        debugInfo.serviceAccount[filePath] = {
          exists,
          readable: exists ? true : false
        };

        if (exists) {
          const stats = fs.statSync(filePath);
          debugInfo.serviceAccount[filePath].size = stats.size;
          debugInfo.serviceAccount[filePath].modified = stats.mtime;

          // Try to read and validate the file
          try {
            const content = require(filePath);
            debugInfo.serviceAccount[filePath].valid = !!(content.project_id && content.private_key && content.client_email);
            debugInfo.serviceAccount[filePath].project_id = content.project_id;
            debugInfo.serviceAccount[filePath].client_email = content.client_email;
          } catch (error) {
            debugInfo.serviceAccount[filePath].valid = false;
            debugInfo.serviceAccount[filePath].error = error.message;
          }
        }
      } catch (error) {
        debugInfo.serviceAccount[filePath] = {
          exists: false,
          error: error.message
        };
      }
    }

    // Test Firebase service initialization
    try {
      await firebaseService.initialize();
      debugInfo.firebaseConfig.initialized = true;
      debugInfo.firebaseConfig.hasMessaging = !!firebaseService.messaging;
    } catch (error) {
      debugInfo.firebaseConfig.initialized = false;
      debugInfo.firebaseConfig.error = error.message;
      debugInfo.errors.push(`Firebase initialization failed: ${error.message}`);
    }

    logger.info('Firebase Debug Information', {
      type: 'debug',
      debugInfo
    });

    res.json({
      success: true,
      message: 'Firebase debug information collected',
      data: debugInfo,
      requestId: req.requestId,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Firebase debug failed', {
      type: 'debug',
      error: error.message,
      stack: error.stack
    });

    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to collect Firebase debug information',
        details: error.message,
        requestId: req.requestId,
      },
      timestamp: new Date().toISOString(),
    });
  }
});

module.exports = router;

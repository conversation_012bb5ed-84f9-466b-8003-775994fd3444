import express from 'express';
import { body } from 'express-validator';
import { register, login, refreshToken, logout, getProfile } from '../controllers/authController';
import { authenticate } from '../middleware/auth';

const router = express.Router();

// Register
router.post('/register', [
  body('name').trim().isLength({ min: 2 }).withMessage('Name must be at least 2 characters'),
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
], register);

// Login
router.post('/login', [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('password').notEmpty().withMessage('Password is required'),
], login);

// Refresh token
router.post('/refresh', refreshToken);

// Logout
router.post('/logout', authenticate, logout);

// Get user profile
router.get('/profile', authenticate, getProfile);

export default router;

const express = require('express');
const fs = require('fs');
const path = require('path');
const { logger, getLoggingStats, getRecentLogs, reconnectDatabase, cleanupFallbackFiles } = require('../config/logger');
const { Log } = require('../models');

const router = express.Router();

/**
 * Logs Management Routes
 * Provides endpoints to view and manage application logs
 */

// Smart logging endpoints (database-first with file fallback)

// Get logging system status and statistics
router.get('/status', (req, res) => {
  try {
    const stats = getLoggingStats();

    res.json({
      message: 'Logging system status',
      status: {
        databasePrimary: stats.databaseReady,
        fileFallback: !stats.databaseReady || stats.fileFallback > 0,
        totalLogs: stats.totalLogs,
        databaseSuccess: stats.databaseSuccess,
        fileFallback: stats.fileFallback,
        errors: stats.errors,
        successRate: stats.databaseSuccessRate,
        fallbackRate: stats.fileFallbackRate,
        pendingLogs: stats.pendingLogs,
      },
      recommendations: generateRecommendations(stats),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Error getting logging status', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to get logging status' },
    });
  }
});

/**
 * @swagger
 * /logs/system:
 *   get:
 *     summary: Get system logs
 *     tags: [Logs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of logs to return
 *       - in: query
 *         name: level
 *         schema:
 *           type: string
 *           enum: [error, warn, info, debug]
 *         description: Filter by log level
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           enum: [auth, api, database, system, socket]
 *         description: Filter by log category
 *       - in: query
 *         name: source
 *         schema:
 *           type: string
 *         description: Filter by log source
 *     responses:
 *       200:
 *         description: System logs retrieved successfully
 */
router.get('/system', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const level = req.query.level;
    const category = req.query.category;
    const source = req.query.source;

    if (!Log) {
      return res.status(503).json({
        success: false,
        message: 'Database not available'
      });
    }

    const whereClause = {};

    // Filter by level
    if (level) {
      whereClause.level = level;
    }

    // Filter by source
    if (source) {
      whereClause.source = source;
    }

    // Map category to source patterns
    if (category && !source) {
      const { Op } = require('sequelize');
      switch (category) {
        case 'socket':
          whereClause.source = {
            [Op.in]: ['socket_server', 'socket_analytics', 'socket_communication']
          };
          break;
        case 'auth':
          whereClause.source = {
            [Op.in]: ['auth_service', 'authentication', 'login']
          };
          break;
        case 'api':
          whereClause.source = {
            [Op.in]: ['api_server', 'express', 'routes']
          };
          break;
        case 'database':
          whereClause.source = {
            [Op.in]: ['database', 'sequelize', 'mysql']
          };
          break;
        case 'system':
          whereClause.source = {
            [Op.in]: ['system', 'server', 'application']
          };
          break;
      }
    }

    const logs = await Log.findAll({
      where: whereClause,
      order: [['createdAt', 'DESC']],
      limit: Math.min(limit, 100), // Cap at 100 logs
      attributes: ['id', 'level', 'message', 'meta', 'source', 'createdAt']
    });

    // Transform logs for frontend consumption
    const transformedLogs = logs.map(log => ({
      id: log.id,
      level: log.level,
      message: log.message,
      timestamp: log.createdAt,
      source: log.source,
      category: getCategoryFromSource(log.source),
      details: typeof log.meta === 'string' ? log.meta : JSON.stringify(log.meta, null, 2),
      stackTrace: log.meta?.stackTrace || log.meta?.error?.stack || null
    }));

    res.json({
      success: true,
      data: {
        logs: transformedLogs,
        count: transformedLogs.length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Error fetching system logs', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system logs',
      error: error.message
    });
  }
});

// Helper function to determine category from source
function getCategoryFromSource(source) {
  if (!source) return 'system';

  if (source.includes('socket')) return 'socket';
  if (source.includes('auth') || source.includes('login')) return 'auth';
  if (source.includes('api') || source.includes('express') || source.includes('routes')) return 'api';
  if (source.includes('database') || source.includes('sequelize') || source.includes('mysql')) return 'database';

  return 'system';
}

// Get recent logs (smart - tries database first, then files)
router.get('/recent', async (req, res) => {
  try {
    const { limit = 50, level } = req.query;
    const logs = await getRecentLogs(parseInt(limit), level);

    res.json({
      message: 'Recent logs retrieved',
      logs,
      source: logs.length > 0 && logs[0].id ? 'database' : 'files',
      count: logs.length,
      filters: { limit: parseInt(limit), level },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Error getting recent logs', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to get recent logs' },
    });
  }
});

// Force database reconnection
router.post('/reconnect', async (req, res) => {
  try {
    const success = await reconnectDatabase();

    logger.info('Database reconnection attempted', { success });

    res.json({
      message: success ? 'Database reconnection successful' : 'Database reconnection failed',
      success,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Error during database reconnection', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to reconnect database' },
    });
  }
});

// Cleanup fallback files
router.delete('/fallback/cleanup', async (req, res) => {
  try {
    const { days = 7 } = req.query;
    const deletedCount = await cleanupFallbackFiles(parseInt(days));

    logger.info('Fallback files cleanup completed', {
      deletedFiles: deletedCount,
      cutoffDays: days,
    });

    res.json({
      message: 'Fallback files cleanup completed',
      deleted: deletedCount,
      cutoffDays: parseInt(days),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Error during fallback cleanup', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to cleanup fallback files' },
    });
  }
});

// Database log endpoints (must come before file-based routes)

// Get logs from database
router.get('/db', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      level,
      type,
      userId,
      startDate,
      endDate,
      search
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const where = {};

    // Apply filters
    if (level) where.level = level;
    if (type) where.type = type;
    if (userId) where.user_id = userId;

    // Date range filter
    if (startDate || endDate) {
      const { Op } = require('sequelize');
      where.created_at = {};
      if (startDate) where.created_at[Op.gte] = new Date(startDate);
      if (endDate) where.created_at[Op.lte] = new Date(endDate);
    }

    // Search filter
    if (search) {
      const { Op } = require('sequelize');
      where[Op.or] = [
        { message: { [Op.like]: `%${search}%` } },
        { url: { [Op.like]: `%${search}%` } },
      ];
    }

    const logs = await Log.findAndCountAll({
      where,
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset,
      include: [
        {
          association: 'user',
          attributes: ['id', 'name', 'email'],
        },
      ],
    });

    res.json({
      message: 'Database logs retrieved',
      logs: logs.rows.map(log => log.toSafeJSON()),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: logs.count,
        pages: Math.ceil(logs.count / parseInt(limit)),
      },
      filters: {
        level,
        type,
        userId,
        startDate,
        endDate,
        search,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Error fetching database logs', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to fetch database logs' },
    });
  }
});

// Get log statistics from database
router.get('/db/stats', async (req, res) => {
  try {
    const stats = await Log.getLogStatistics();
    const requestStats = await Log.getRequestStats('24h');
    const topErrors = await Log.getTopErrors(10, '24h');

    res.json({
      message: 'Database log statistics',
      stats: {
        overview: stats,
        requests: requestStats,
        topErrors,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Error fetching database log statistics', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to fetch database log statistics' },
    });
  }
});

// File-based logging endpoints (legacy/fallback)

// Get log files list
router.get('/', (req, res) => {
  try {
    const logsDir = path.join(__dirname, '../../logs');
    
    if (!fs.existsSync(logsDir)) {
      return res.json({
        message: 'Logs directory not found',
        files: [],
        timestamp: new Date().toISOString(),
      });
    }
    
    const files = fs.readdirSync(logsDir)
      .filter(file => file.endsWith('.log'))
      .map(file => {
        const filePath = path.join(logsDir, file);
        const stats = fs.statSync(filePath);
        return {
          name: file,
          size: stats.size,
          sizeHuman: formatBytes(stats.size),
          modified: stats.mtime,
          created: stats.birthtime,
        };
      })
      .sort((a, b) => b.modified - a.modified);
    
    res.json({
      message: 'Available log files',
      files,
      totalFiles: files.length,
      totalSize: files.reduce((sum, file) => sum + file.size, 0),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Error listing log files', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to list log files' },
    });
  }
});

// Get specific log file content
router.get('/:filename', (req, res) => {
  try {
    const { filename } = req.params;
    const { lines = 100, search, level } = req.query;
    
    // Validate filename to prevent directory traversal
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return res.status(400).json({
        success: false,
        error: { message: 'Invalid filename' },
      });
    }
    
    const logsDir = path.join(__dirname, '../../logs');
    const filePath = path.join(logsDir, filename);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        error: { message: 'Log file not found' },
      });
    }
    
    // Read file content
    const content = fs.readFileSync(filePath, 'utf8');
    let logLines = content.split('\n').filter(line => line.trim());
    
    // Filter by log level if specified
    if (level) {
      logLines = logLines.filter(line => {
        try {
          const logEntry = JSON.parse(line);
          return logEntry.level === level;
        } catch {
          return line.toLowerCase().includes(level.toLowerCase());
        }
      });
    }
    
    // Search filter
    if (search) {
      logLines = logLines.filter(line => 
        line.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    // Limit lines (get last N lines)
    const maxLines = Math.min(parseInt(lines) || 100, 1000);
    logLines = logLines.slice(-maxLines);
    
    // Parse JSON logs if possible
    const parsedLogs = logLines.map(line => {
      try {
        return JSON.parse(line);
      } catch {
        return { raw: line };
      }
    });
    
    res.json({
      message: `Log file: ${filename}`,
      filename,
      totalLines: content.split('\n').length,
      filteredLines: logLines.length,
      logs: parsedLogs,
      filters: {
        lines: maxLines,
        search: search || null,
        level: level || null,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Error reading log file', { 
      filename: req.params.filename,
      error: error.message 
    });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to read log file' },
    });
  }
});

// Get log statistics
router.get('/stats/summary', (req, res) => {
  try {
    const logsDir = path.join(__dirname, '../../logs');
    
    if (!fs.existsSync(logsDir)) {
      return res.json({
        message: 'Logs directory not found',
        stats: {},
        timestamp: new Date().toISOString(),
      });
    }
    
    const files = fs.readdirSync(logsDir).filter(file => file.endsWith('.log'));
    const stats = {
      totalFiles: files.length,
      totalSize: 0,
      fileTypes: {},
      oldestLog: null,
      newestLog: null,
    };
    
    files.forEach(file => {
      const filePath = path.join(logsDir, file);
      const fileStats = fs.statSync(filePath);
      
      stats.totalSize += fileStats.size;
      
      // Categorize by file type
      const type = file.split('-')[0];
      if (!stats.fileTypes[type]) {
        stats.fileTypes[type] = { count: 0, size: 0 };
      }
      stats.fileTypes[type].count++;
      stats.fileTypes[type].size += fileStats.size;
      
      // Track oldest and newest
      if (!stats.oldestLog || fileStats.birthtime < stats.oldestLog.date) {
        stats.oldestLog = { file, date: fileStats.birthtime };
      }
      if (!stats.newestLog || fileStats.mtime > stats.newestLog.date) {
        stats.newestLog = { file, date: fileStats.mtime };
      }
    });
    
    stats.totalSizeHuman = formatBytes(stats.totalSize);
    
    res.json({
      message: 'Log statistics',
      stats,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Error getting log statistics', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to get log statistics' },
    });
  }
});

// Clear old logs
router.delete('/cleanup', (req, res) => {
  try {
    const { days = 30 } = req.query;
    const logsDir = path.join(__dirname, '../../logs');
    
    if (!fs.existsSync(logsDir)) {
      return res.json({
        message: 'Logs directory not found',
        deleted: 0,
        timestamp: new Date().toISOString(),
      });
    }
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - parseInt(days));
    
    const files = fs.readdirSync(logsDir).filter(file => file.endsWith('.log'));
    let deletedCount = 0;
    let deletedSize = 0;
    
    files.forEach(file => {
      const filePath = path.join(logsDir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.mtime < cutoffDate) {
        deletedSize += stats.size;
        fs.unlinkSync(filePath);
        deletedCount++;
      }
    });
    
    logger.info('Log cleanup completed', {
      deletedFiles: deletedCount,
      deletedSize: formatBytes(deletedSize),
      cutoffDays: days,
    });
    
    res.json({
      message: 'Log cleanup completed',
      deleted: deletedCount,
      deletedSize: formatBytes(deletedSize),
      cutoffDays: parseInt(days),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Error during log cleanup', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to cleanup logs' },
    });
  }
});

// Helper function to format bytes

// Get error logs from database
router.get('/db/errors', async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);

    const errors = await Log.getErrorLogs(parseInt(limit), offset);
    const totalErrors = await Log.count({ where: { level: 'error' } });

    res.json({
      message: 'Error logs from database',
      errors: errors.map(log => log.toSafeJSON()),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalErrors,
        pages: Math.ceil(totalErrors / parseInt(limit)),
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Error fetching database error logs', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to fetch database error logs' },
    });
  }
});

// Get user activity logs
router.get('/db/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 20 } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);

    const logs = await Log.getLogsByUser(userId, parseInt(limit), offset);
    const totalLogs = await Log.count({ where: { user_id: userId } });

    res.json({
      message: `Activity logs for user ${userId}`,
      logs: logs.map(log => log.toSafeJSON()),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalLogs,
        pages: Math.ceil(totalLogs / parseInt(limit)),
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Error fetching user activity logs', {
      userId: req.params.userId,
      error: error.message
    });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to fetch user activity logs' },
    });
  }
});

// Cleanup old database logs
router.delete('/db/cleanup', async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const deletedCount = await Log.cleanupOldLogs(parseInt(days));

    logger.info('Database log cleanup completed', {
      deletedLogs: deletedCount,
      cutoffDays: days,
    });

    res.json({
      message: 'Database log cleanup completed',
      deleted: deletedCount,
      cutoffDays: parseInt(days),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Error during database log cleanup', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to cleanup database logs' },
    });
  }
});

// Helper function to format bytes
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Helper function to generate recommendations based on logging stats
function generateRecommendations(stats) {
  const recommendations = [];

  if (!stats.databaseReady) {
    recommendations.push({
      priority: 'high',
      message: 'Database logging is unavailable - using file fallback',
      action: 'Check database connection and try reconnecting',
      endpoint: 'POST /logs/reconnect',
    });
  }

  if (stats.fileFallback > stats.databaseSuccess * 0.1) {
    recommendations.push({
      priority: 'medium',
      message: 'High fallback rate detected - database may be unstable',
      action: 'Monitor database performance and connection stability',
    });
  }

  if (stats.pendingLogs > 100) {
    recommendations.push({
      priority: 'medium',
      message: `${stats.pendingLogs} logs pending database write`,
      action: 'Database may be slow - consider optimizing queries',
    });
  }

  if (stats.errors > stats.totalLogs * 0.05) {
    recommendations.push({
      priority: 'high',
      message: 'High error rate in logging system',
      action: 'Check application logs for logging system errors',
    });
  }

  if (recommendations.length === 0) {
    recommendations.push({
      priority: 'info',
      message: 'Logging system is operating normally',
      action: 'No action required',
    });
  }

  return recommendations;
}

module.exports = router;

const express = require('express');
const { Log, User, PasswordReset, EmailVerification } = require('../models');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { logSecurity } = require('../config/logger');

const router = express.Router();

/**
 * Security Monitoring Routes
 * Provides security analytics and monitoring capabilities
 */

// Apply admin authentication to all security routes
router.use(authenticateToken);
router.use(requireAdmin);

/**
 * @swagger
 * /security/dashboard:
 *   get:
 *     summary: Security dashboard overview
 *     tags: [Security]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Security dashboard data
 */
router.get('/dashboard', async (req, res) => {
  try {
    const { Op } = require('sequelize');
    const now = new Date();
    const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const last7d = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Security metrics
    const [
      totalSecurityEvents,
      recentSecurityEvents,
      failedLogins,
      suspiciousActivity,
      rateLimitViolations,
      passwordResetRequests,
      emailVerificationRequests,
      lockedAccounts,
    ] = await Promise.all([
      Log.count({ where: { type: 'security' } }),
      Log.count({ where: { type: 'security', created_at: { [Op.gte]: last24h } } }),
      Log.count({ 
        where: { 
          type: 'security',
          message: { [Op.like]: '%failed_login%' },
          created_at: { [Op.gte]: last7d }
        }
      }),
      Log.count({ 
        where: { 
          type: 'security',
          message: { [Op.like]: '%suspicious%' },
          created_at: { [Op.gte]: last7d }
        }
      }),
      Log.count({ 
        where: { 
          type: 'security',
          message: { [Op.like]: '%rate limit%' },
          created_at: { [Op.gte]: last24h }
        }
      }),
      PasswordReset.count({ where: { created_at: { [Op.gte]: last24h } } }),
      EmailVerification.count({ where: { created_at: { [Op.gte]: last24h } } }),
      User.count({ where: { locked_until: { [Op.gt]: now } } }),
    ]);

    // Top security events by type
    const securityEventTypes = await Log.findAll({
      attributes: [
        'message',
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count'],
      ],
      where: {
        type: 'security',
        created_at: { [Op.gte]: last7d },
      },
      group: ['message'],
      order: [[require('sequelize').fn('COUNT', require('sequelize').col('id')), 'DESC']],
      limit: 10,
      raw: true,
    });

    // Top IPs with security events
    const topSecurityIPs = await Log.findAll({
      attributes: [
        'ip_address',
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count'],
      ],
      where: {
        type: 'security',
        created_at: { [Op.gte]: last7d },
        ip_address: { [Op.ne]: null },
      },
      group: ['ip_address'],
      order: [[require('sequelize').fn('COUNT', require('sequelize').col('id')), 'DESC']],
      limit: 10,
      raw: true,
    });

    logSecurity('Security dashboard accessed', {
      adminId: req.user.id,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      data: {
        overview: {
          totalSecurityEvents,
          recentSecurityEvents,
          failedLogins,
          suspiciousActivity,
          rateLimitViolations,
          passwordResetRequests,
          emailVerificationRequests,
          lockedAccounts,
        },
        trends: {
          securityEventTypes: securityEventTypes.map(item => ({
            type: item.message,
            count: parseInt(item.count),
          })),
          topSecurityIPs: topSecurityIPs.map(item => ({
            ip: item.ip_address,
            count: parseInt(item.count),
          })),
        },
      },
      message: 'Security dashboard data retrieved successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Security dashboard error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve security dashboard data' },
    });
  }
});

/**
 * @swagger
 * /security/events:
 *   get:
 *     summary: Get security events
 *     tags: [Security]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Items per page
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: Filter by event type
 *       - in: query
 *         name: ip
 *         schema:
 *           type: string
 *         description: Filter by IP address
 *     responses:
 *       200:
 *         description: List of security events
 */
router.get('/events', async (req, res) => {
  try {
    const { page = 1, limit = 50, type, ip, startDate, endDate } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);

    const where = { type: 'security' };
    
    if (type) {
      where.message = { [require('sequelize').Op.like]: `%${type}%` };
    }
    
    if (ip) {
      where.ip_address = ip;
    }
    
    if (startDate || endDate) {
      const { Op } = require('sequelize');
      where.created_at = {};
      if (startDate) where.created_at[Op.gte] = new Date(startDate);
      if (endDate) where.created_at[Op.lte] = new Date(endDate);
    }

    const events = await Log.findAndCountAll({
      where,
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset,
      include: [
        {
          association: 'user',
          attributes: ['id', 'name', 'email'],
        },
      ],
    });

    res.json({
      success: true,
      data: {
        events: events.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: events.count,
          pages: Math.ceil(events.count / parseInt(limit)),
        },
      },
      message: 'Security events retrieved successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Security events error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve security events' },
    });
  }
});

/**
 * @swagger
 * /security/threats:
 *   get:
 *     summary: Get threat analysis
 *     tags: [Security]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Threat analysis data
 */
router.get('/threats', async (req, res) => {
  try {
    const { Op } = require('sequelize');
    const last24h = new Date(Date.now() - 24 * 60 * 60 * 1000);

    // Analyze threats
    const threats = await Log.findAll({
      attributes: [
        'ip_address',
        'message',
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count'],
        [require('sequelize').fn('MAX', require('sequelize').col('created_at')), 'last_seen'],
      ],
      where: {
        type: 'security',
        created_at: { [Op.gte]: last24h },
        ip_address: { [Op.ne]: null },
      },
      group: ['ip_address', 'message'],
      having: require('sequelize').literal('COUNT(id) >= 5'), // IPs with 5+ security events
      order: [[require('sequelize').fn('COUNT', require('sequelize').col('id')), 'DESC']],
      raw: true,
    });

    // Categorize threats
    const categorizedThreats = threats.reduce((acc, threat) => {
      const category = categorizeThreat(threat.message);
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push({
        ip: threat.ip_address,
        type: threat.message,
        count: parseInt(threat.count),
        lastSeen: threat.last_seen,
        severity: calculateSeverity(threat.message, parseInt(threat.count)),
      });
      return acc;
    }, {});

    res.json({
      success: true,
      data: {
        threats: categorizedThreats,
        summary: {
          totalThreats: threats.length,
          categories: Object.keys(categorizedThreats),
          highSeverity: threats.filter(t => calculateSeverity(t.message, parseInt(t.count)) === 'high').length,
        },
      },
      message: 'Threat analysis completed',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Threat analysis error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to analyze threats' },
    });
  }
});

/**
 * @swagger
 * /security/block-ip:
 *   post:
 *     summary: Block an IP address
 *     tags: [Security]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ip
 *               - reason
 *             properties:
 *               ip:
 *                 type: string
 *               reason:
 *                 type: string
 *               duration:
 *                 type: integer
 *                 description: Block duration in hours (0 for permanent)
 *     responses:
 *       200:
 *         description: IP blocked successfully
 */
router.post('/block-ip', async (req, res) => {
  try {
    const { ip, reason, duration = 24 } = req.body;

    if (!ip || !reason) {
      return res.status(400).json({
        success: false,
        error: { message: 'IP address and reason are required' },
      });
    }

    // For now, just log the block action
    // In a production system, you'd store this in a blacklist table
    logSecurity('IP address blocked by admin', {
      blockedIP: ip,
      reason,
      duration: duration === 0 ? 'permanent' : `${duration} hours`,
      adminId: req.user.id,
      adminEmail: req.user.email,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      success: true,
      message: 'IP address blocked successfully',
      data: {
        blockedIP: ip,
        reason,
        duration: duration === 0 ? 'permanent' : `${duration} hours`,
        blockedBy: req.user.email,
        blockedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('IP blocking error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to block IP address' },
    });
  }
});

// Helper functions
function categorizeThreat(message) {
  if (message.includes('failed_login') || message.includes('invalid_token')) {
    return 'authentication_attacks';
  }
  if (message.includes('rate_limit')) {
    return 'rate_limit_violations';
  }
  if (message.includes('suspicious')) {
    return 'suspicious_activity';
  }
  if (message.includes('sql') || message.includes('xss') || message.includes('injection')) {
    return 'injection_attempts';
  }
  return 'other';
}

function calculateSeverity(message, count) {
  if (count >= 50) return 'critical';
  if (count >= 20) return 'high';
  if (count >= 10) return 'medium';
  return 'low';
}

module.exports = router;

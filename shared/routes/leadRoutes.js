const express = require('express');
const { body, param, query } = require('express-validator');
const leadController = require('../controllers/leadController');
const { authenticateToken, requireRole, optionalAuth } = require('../middleware/auth');

const router = express.Router();

/**
 * Lead Routes
 * All routes require authentication
 */

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const createLeadValidation = [
  body('first_name')
    .notEmpty()
    .withMessage('First name is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('First name must be between 1 and 100 characters'),
  body('last_name')
    .notEmpty()
    .withMessage('Last name is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('Last name must be between 1 and 100 characters'),
  body('email')
    .isEmail()
    .withMessage('Valid email is required')
    .normalizeEmail(),
  body('phone')
    .optional()
    .isLength({ min: 10, max: 50 })
    .withMessage('Phone number must be between 10 and 50 characters'),
  body('company_name')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Company name cannot exceed 255 characters'),
  body('job_title')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Job title cannot exceed 100 characters'),
  body('lead_source')
    .optional()
    .isIn(['website', 'referral', 'social_media', 'email_campaign', 'phone', 'event', 'advertisement', 'partner', 'other'])
    .withMessage('Invalid lead source'),
  body('status')
    .optional()
    .isIn(['new', 'contacted', 'qualified', 'proposal', 'negotiation', 'won', 'lost', 'nurturing'])
    .withMessage('Invalid status'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Invalid priority'),
  body('lead_score')
    .optional()
    .isInt({ min: 0, max: 100 })
    .withMessage('Lead score must be between 0 and 100'),
  body('website')
    .optional()
    .isURL()
    .withMessage('Website must be a valid URL'),
  body('company_size')
    .optional()
    .isIn(['1-10', '11-50', '51-200', '201-1000', '1000+'])
    .withMessage('Invalid company size'),
  body('annual_revenue')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Annual revenue must be a positive number'),
  body('monthly_energy_budget')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Monthly energy budget must be a positive number'),
  body('estimated_value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Estimated value must be a positive number'),
  body('actual_value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Actual value must be a positive number'),
  body('first_contact_date')
    .optional()
    .isISO8601()
    .withMessage('First contact date must be a valid date'),
  body('next_follow_up_date')
    .optional()
    .isISO8601()
    .withMessage('Next follow up date must be a valid date'),
];

const updateLeadValidation = [
  body('first_name')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('First name must be between 1 and 100 characters'),
  body('last_name')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Last name must be between 1 and 100 characters'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Valid email is required')
    .normalizeEmail(),
  body('phone')
    .optional()
    .isLength({ min: 10, max: 50 })
    .withMessage('Phone number must be between 10 and 50 characters'),
  body('lead_source')
    .optional()
    .isIn(['website', 'referral', 'social_media', 'email_campaign', 'phone', 'event', 'advertisement', 'partner', 'other'])
    .withMessage('Invalid lead source'),
  body('status')
    .optional()
    .isIn(['new', 'contacted', 'qualified', 'proposal', 'negotiation', 'won', 'lost', 'nurturing'])
    .withMessage('Invalid status'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Invalid priority'),
  body('lead_score')
    .optional()
    .isInt({ min: 0, max: 100 })
    .withMessage('Lead score must be between 0 and 100'),
  body('website')
    .optional()
    .isURL()
    .withMessage('Website must be a valid URL'),
  body('company_size')
    .optional()
    .isIn(['1-10', '11-50', '51-200', '201-1000', '1000+'])
    .withMessage('Invalid company size'),
  body('annual_revenue')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Annual revenue must be a positive number'),
  body('monthly_energy_budget')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Monthly energy budget must be a positive number'),
  body('estimated_value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Estimated value must be a positive number'),
  body('actual_value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Actual value must be a positive number'),
  body('next_follow_up_date')
    .optional()
    .isISO8601()
    .withMessage('Next follow up date must be a valid date'),
];

const idValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID must be a positive integer'),
];

const submissionIdValidation = [
  param('submissionId')
    .isInt({ min: 1 })
    .withMessage('Submission ID must be a positive integer'),
];

// Routes

/**
 * @route   GET /api/v1/leads/stats
 * @desc    Get lead statistics
 * @access  Private (Admin/Manager)
 */
router.get('/stats', 
  requireRole(['admin', 'manager']),
  leadController.getLeadStats
);

/**
 * @route   GET /api/v1/leads
 * @desc    Get all leads with filtering and pagination
 * @access  Private
 */
router.get('/', 
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('status').optional(),
    query('priority').optional(),
    query('lead_source').optional(),
    query('assigned_to').optional().isInt({ min: 1 }).withMessage('Assigned to must be a positive integer'),
    query('search').optional().isLength({ max: 255 }).withMessage('Search term cannot exceed 255 characters'),
    query('sortBy').optional().isIn(['created_at', 'updated_at', 'first_name', 'last_name', 'email', 'lead_score', 'status']).withMessage('Invalid sort field'),
    query('sortOrder').optional().isIn(['ASC', 'DESC']).withMessage('Sort order must be ASC or DESC'),
    query('include_activities').optional().isBoolean().withMessage('Include activities must be a boolean'),
  ],
  leadController.getLeads
);

/**
 * @route   POST /api/v1/leads
 * @desc    Create a new lead
 * @access  Private
 */
router.post('/',
  optionalAuth, // Allow creation without authentication for public forms
  createLeadValidation,
  leadController.createLead
);

/**
 * @route   POST /api/v1/leads/from-contact/:submissionId
 * @desc    Create lead from contact submission
 * @access  Private
 */
router.post('/from-contact/:submissionId',
  submissionIdValidation,
  leadController.createLeadFromContact
);

/**
 * @route   PATCH /api/v1/leads/bulk
 * @desc    Bulk update leads
 * @access  Private (Admin/Manager)
 */
router.patch('/bulk',
  requireRole(['admin', 'manager']),
  [
    body('lead_ids').isArray({ min: 1 }).withMessage('lead_ids must be a non-empty array'),
    body('lead_ids.*').isInt({ min: 1 }).withMessage('Each lead ID must be a positive integer'),
    body('updates').isObject().withMessage('updates must be an object'),
  ],
  leadController.bulkUpdateLeads
);

/**
 * @route   GET /api/v1/leads/:id
 * @desc    Get lead by ID
 * @access  Private
 */
router.get('/:id',
  idValidation,
  [
    query('include_activities').optional().isBoolean().withMessage('Include activities must be a boolean'),
  ],
  leadController.getLeadById
);

/**
 * @route   PUT /api/v1/leads/:id
 * @desc    Update lead
 * @access  Private
 */
router.put('/:id',
  idValidation,
  updateLeadValidation,
  leadController.updateLead
);

/**
 * @route   DELETE /api/v1/leads/:id
 * @desc    Delete lead
 * @access  Private (Admin only)
 */
router.delete('/:id',
  requireRole(['admin']),
  idValidation,
  leadController.deleteLead
);

/**
 * @route   PATCH /api/v1/leads/:id/assign
 * @desc    Assign lead to user
 * @access  Private (Admin/Manager)
 */
router.patch('/:id/assign',
  requireRole(['admin', 'manager']),
  idValidation,
  [
    body('assigned_to').isInt({ min: 1 }).withMessage('assigned_to must be a positive integer'),
  ],
  leadController.assignLead
);

/**
 * @route   PATCH /api/v1/leads/:id/status
 * @desc    Update lead status
 * @access  Private
 */
router.patch('/:id/status',
  idValidation,
  [
    body('status').isIn(['new', 'contacted', 'qualified', 'proposal', 'negotiation', 'won', 'lost', 'nurturing']).withMessage('Invalid status'),
    body('notes').optional().isLength({ max: 1000 }).withMessage('Notes cannot exceed 1000 characters'),
  ],
  leadController.updateLeadStatus
);

module.exports = router;

const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const helmet = require('helmet');
const { logSecurity } = require('../config/logger');

/**
 * Security Middleware
 * Provides comprehensive security features including rate limiting, 
 * request throttling, and security headers
 */

// Global rate limiter - applies to all requests
const globalRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Limit each IP to 1000 requests per windowMs
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  handler: (req, res) => {
    logSecurity('Global rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.originalUrl,
      method: req.method,
    });

    res.status(429).json({
      success: false,
      error: {
        message: 'Too many requests from this IP, please try again later',
        type: 'RATE_LIMIT_EXCEEDED',
        retryAfter: '15 minutes',
      },
    });
  },
});

// Authentication rate limiter - stricter for auth endpoints
const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Temporarily increased for testing - Limit each IP to 100 auth requests per windowMs
  skipSuccessfulRequests: true, // Don't count successful requests
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logSecurity('Authentication rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.originalUrl,
      method: req.method,
      body: req.body?.email ? { email: req.body.email } : {},
    });

    res.status(429).json({
      success: false,
      error: {
        message: 'Too many authentication attempts, please try again later',
        type: 'AUTH_RATE_LIMIT_EXCEEDED',
        retryAfter: '15 minutes',
      },
    });
  },
});

// Password reset rate limiter - very strict
const passwordResetRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Limit each IP to 3 password reset requests per hour
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logSecurity('Password reset rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      email: req.body?.email,
    });

    res.status(429).json({
      success: false,
      error: {
        message: 'Too many password reset attempts, please try again later',
        type: 'PASSWORD_RESET_RATE_LIMIT_EXCEEDED',
        retryAfter: '1 hour',
      },
    });
  },
});

// Contact form rate limiter
const contactRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // Limit each IP to 5 contact submissions per hour
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logSecurity('Contact form rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      email: req.body?.email,
    });

    res.status(429).json({
      success: false,
      error: {
        message: 'Too many contact submissions, please try again later',
        type: 'CONTACT_RATE_LIMIT_EXCEEDED',
        retryAfter: '1 hour',
      },
    });
  },
});

// Request speed limiter - slows down requests instead of blocking
const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 100, // Allow 100 requests per windowMs without delay
  delayMs: () => 500, // Add 500ms delay per request after delayAfter (new v2 syntax)
  maxDelayMs: 20000, // Maximum delay of 20 seconds
  validate: {
    delayMs: false, // Disable delayMs validation warning
  },
});

// Security headers middleware
const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
  crossOriginEmbedderPolicy: false, // Disable for API compatibility
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true,
  },
});

// Suspicious activity detector
const suspiciousActivityDetector = (req, res, next) => {
  // Skip security checks for authenticated requests to protected endpoints
  const isProtectedEndpoint = req.originalUrl.includes('/api/v1/auth/') ||
                              req.originalUrl.includes('/api/v1/crm/') ||
                              req.originalUrl.includes('/api/v1/admin/') ||
                              req.originalUrl.includes('/api/v1/profile/') ||
                              req.originalUrl.includes('/api/v1/contact/') ||
                              req.originalUrl.includes('/api/v1/analytics/') ||
                              req.originalUrl.includes('/api/v1/leads/');
  const hasAuthToken = req.headers.authorization && req.headers.authorization.startsWith('Bearer ');

  if (isProtectedEndpoint && hasAuthToken) {
    // For authenticated auth endpoints, use more lenient patterns
    const strictPatterns = [
      // Only check for obvious SQL injection
      /(\b(union|select|insert|update|delete|drop|create|alter)\s+\b)/i,
      // Only check for obvious XSS
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      // Path traversal
      /\.\.\//g,
      /\.\.\\/g,
    ];

    const checkString = JSON.stringify(req.body) + req.originalUrl + JSON.stringify(req.query);

    for (const pattern of strictPatterns) {
      if (pattern.test(checkString)) {
        logSecurity('Suspicious activity detected (authenticated)', {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          url: req.originalUrl,
          method: req.method,
          pattern: pattern.toString(),
          matchedContent: checkString.match(pattern)?.[0],
        });

        return res.status(400).json({
          success: false,
          error: {
            message: 'Invalid request detected',
            type: 'SUSPICIOUS_ACTIVITY',
          },
        });
      }
    }
  } else {
    // For unauthenticated requests, use full security patterns but be more specific
    const suspiciousPatterns = [
      // SQL injection patterns (more specific to avoid false positives)
      /(\bunion\s+select\b)/i,
      /(\bselect\s+\*\s+from\b)/i,
      /(\binsert\s+into\b)/i,
      /(\bupdate\s+\w+\s+set\b)/i,
      /(\bdelete\s+from\b)/i,
      /(\bdrop\s+table\b)/i,
      /(\bcreate\s+table\b)/i,
      /(\balter\s+table\b)/i,
      /(\bexec\s*\()/i,
      /(\bexecute\s*\()/i,
      // XSS patterns
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      // Path traversal
      /\.\.\//g,
      /\.\.\\/g,
      // Command injection (more restrictive)
      /[;&|`$]{2,}/g, // Multiple command separators
    ];

    const checkString = JSON.stringify(req.body) + req.originalUrl + JSON.stringify(req.query);

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(checkString)) {
        logSecurity('Suspicious activity detected', {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          url: req.originalUrl,
          method: req.method,
          pattern: pattern.toString(),
          matchedContent: checkString.match(pattern)?.[0],
          body: req.body,
          query: req.query,
        });

        return res.status(400).json({
          success: false,
          error: {
            message: 'Invalid request detected',
            type: 'SUSPICIOUS_ACTIVITY',
          },
        });
      }
    }
  }

  next();
};

// Request size limiter
const requestSizeLimiter = (maxSize = '10mb') => {
  return (req, res, next) => {
    const contentLength = parseInt(req.get('Content-Length') || '0');
    const maxBytes = parseSize(maxSize);
    
    if (contentLength > maxBytes) {
      logSecurity('Request size limit exceeded', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        url: req.originalUrl,
        contentLength,
        maxSize,
      });
      
      return res.status(413).json({
        success: false,
        error: {
          message: 'Request entity too large',
          type: 'REQUEST_TOO_LARGE',
          maxSize,
        },
      });
    }
    
    next();
  };
};

// IP whitelist/blacklist middleware
const ipFilter = (options = {}) => {
  const { whitelist = [], blacklist = [] } = options;
  
  return (req, res, next) => {
    const clientIP = req.ip;
    
    // Check blacklist first
    if (blacklist.length > 0 && blacklist.includes(clientIP)) {
      logSecurity('Blacklisted IP access attempt', {
        ip: clientIP,
        userAgent: req.get('User-Agent'),
        url: req.originalUrl,
      });
      
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied',
          type: 'IP_BLACKLISTED',
        },
      });
    }
    
    // Check whitelist if configured
    if (whitelist.length > 0 && !whitelist.includes(clientIP)) {
      logSecurity('Non-whitelisted IP access attempt', {
        ip: clientIP,
        userAgent: req.get('User-Agent'),
        url: req.originalUrl,
      });
      
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied',
          type: 'IP_NOT_WHITELISTED',
        },
      });
    }
    
    next();
  };
};

// Helper function to parse size strings
function parseSize(size) {
  const units = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024,
  };
  
  const match = size.toString().toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/);
  if (!match) return 0;
  
  const value = parseFloat(match[1]);
  const unit = match[2] || 'b';
  
  return Math.floor(value * units[unit]);
}

module.exports = {
  globalRateLimit,
  authRateLimit,
  passwordResetRateLimit,
  contactRateLimit,
  speedLimiter,
  securityHeaders,
  suspiciousActivityDetector,
  requestSizeLimiter,
  ipFilter,
};

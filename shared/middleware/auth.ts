import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { pool } from '../config/database';

export interface AuthRequest extends Request {
  user?: {
    id: number;
    email: string;
    role: string;
  };
}

export const authenticate = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      res.status(401).json({
        success: false,
        error: { message: 'Access denied. No token provided.' },
      });
      return;
    }

    const secret = process.env.JWT_SECRET || 'fallback_secret';
    const decoded = jwt.verify(token, secret) as any;
    
    // Check if user still exists
    const [rows] = await pool.execute(
      'SELECT id, email, role FROM users WHERE id = ?',
      [decoded.id]
    );

    const users = rows as any[];
    if (users.length === 0) {
      res.status(401).json({
        success: false,
        error: { message: 'Token is no longer valid.' },
      });
      return;
    }

    req.user = users[0];
    next();
  } catch (error) {
    res.status(401).json({
      success: false,
      error: { message: 'Invalid token.' },
    });
  }
};

export const authorize = (...roles: string[]) => {
  return (req: AuthRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: { message: 'Access denied. Please authenticate.' },
      });
      return;
    }

    if (!roles.includes(req.user.role)) {
      res.status(403).json({
        success: false,
        error: { message: 'Access denied. Insufficient permissions.' },
      });
      return;
    }

    next();
  };
};

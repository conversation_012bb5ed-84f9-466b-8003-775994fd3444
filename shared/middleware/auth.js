const jwt = require('jsonwebtoken');
const { User, UserSession } = require('../models');
const { logAuth, logSecurity } = require('../config/logger');

/**
 * Authentication Middleware
 * Provides JWT token validation and user authentication
 */

// Verify JWT token and attach user to request
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        error: { message: 'Access token required' },
        code: 'TOKEN_REQUIRED',
      });
    }

    // Verify token
    const secret = process.env.JWT_SECRET || 'fallback_secret';
    const decoded = jwt.verify(token, secret);

    // Get user from database
    const user = await User.findByPk(decoded.id, {
      attributes: { exclude: ['password'] },
    });

    if (!user) {
      logSecurity('Invalid token - user not found', {
        userId: decoded.id,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      return res.status(401).json({
        success: false,
        error: { message: 'Invalid token - user not found' },
        code: 'USER_NOT_FOUND',
      });
    }

    // Check if user is active
    if (!user.is_active) {
      logSecurity('Inactive user attempted access', {
        userId: user.id,
        email: user.email,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      return res.status(401).json({
        success: false,
        error: { message: 'Account is deactivated' },
        code: 'ACCOUNT_DEACTIVATED',
      });
    }

    // Check if account is locked
    if (user.isLocked()) {
      logSecurity('Locked user attempted access', {
        userId: user.id,
        email: user.email,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      return res.status(401).json({
        success: false,
        error: { message: 'Account is temporarily locked' },
        code: 'ACCOUNT_LOCKED',
      });
    }

    // Check session lock status
    const sessionToken = decoded.jti || token; // Use jti claim or full token

    // Find the session
    const session = await UserSession.findBySessionToken(sessionToken);

    if (session) {
      // Check if session should be locked due to inactivity
      const shouldBeLocked = session.shouldBeLocked();

      if (shouldBeLocked && !session.is_locked) {
        // Auto-lock session due to inactivity
        try {
          await session.lockSession('inactivity');

          // Verify the lock was successful
          await session.reload();
          if (!session.is_locked) {
            throw new Error('Session lock verification failed');
          }

          logSecurity('Session auto-locked due to inactivity', {
            userId: user.id,
            sessionToken: sessionToken.substring(0, 20) + '...',
            ip: req.ip,
            userAgent: req.get('User-Agent'),
          });
        } catch (lockError) {
          console.error('Failed to auto-lock session:', lockError);

          // If we can't lock the session, invalidate it for security
          try {
            await session.update({
              is_active: false,
              updated_at: new Date()
            });
            console.log('🔒 Session invalidated due to auto-lock failure');
          } catch (invalidateError) {
            console.error('Failed to invalidate session:', invalidateError);
          }

          logSecurity('Session invalidated due to lock failure', {
            userId: user.id,
            sessionToken: sessionToken.substring(0, 20) + '...',
            error: lockError.message,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
          });

          // Block access regardless of lock failure
          return res.status(423).json({
            success: false,
            error: {
              message: 'Session access denied due to security policy',
              code: 'SESSION_SECURITY_BLOCK',
            },
            code: 'SESSION_LOCKED',
          });
        }
      }

      // Check if session is locked or should be locked
      if (session.is_locked || shouldBeLocked) {
        logSecurity('Blocked request from locked session', {
          userId: user.id,
          sessionToken: sessionToken.substring(0, 20) + '...',
          lockReason: session.lock_reason,
          lockedAt: session.locked_at,
          shouldBeLocked,
          requestPath: req.originalUrl || req.path,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
        });

        return res.status(423).json({
          success: false,
          error: {
            message: 'Session is locked due to inactivity',
            lock_reason: session.lock_reason,
            locked_at: session.locked_at,
          },
          code: 'SESSION_LOCKED',
        });
      }

      // Update session activity
      await session.updateActivity();
      req.session = session;
    } else {
      // Session not found in database - this could be an old token
      // We'll allow it but log it for monitoring
      logSecurity('Token validated but no session found in database', {
        userId: user.id,
        sessionToken: sessionToken.substring(0, 20) + '...',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
    }

    // Attach user to request
    req.user = user;
    req.token = token;

    // Log successful authentication
    logAuth('token_validated', user.id, {
      email: user.email,
      role: user.role,
      sessionLocked: session ? session.is_locked : false,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      logSecurity('Invalid JWT token', {
        error: error.message,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      return res.status(401).json({
        success: false,
        error: { message: 'Invalid token' },
        code: 'INVALID_TOKEN',
      });
    }

    if (error.name === 'TokenExpiredError') {
      logSecurity('Expired JWT token', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      return res.status(401).json({
        success: false,
        error: { message: 'Token expired' },
        code: 'TOKEN_EXPIRED',
      });
    }

    console.error('Authentication middleware error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Authentication error' },
      code: 'AUTH_ERROR',
    });
  }
};

// Optional authentication - doesn't fail if no token or if session is locked
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      req.user = null;
      return next();
    }

    // Try to verify token
    const secret = process.env.JWT_SECRET || 'fallback_secret';
    const decoded = jwt.verify(token, secret);

    // Get user from database
    const user = await User.findByPk(decoded.id, {
      attributes: { exclude: ['password'] },
    });

    if (!user || !user.is_active || user.isLocked()) {
      req.user = null;
      return next();
    }

    // Check session lock status (but don't fail if locked)
    const sessionToken = decoded.jti || token;
    const session = await UserSession.findBySessionToken(sessionToken);

    if (session) {
      // Check if session should be locked due to inactivity
      const shouldBeLocked = session.shouldBeLocked();

      if (shouldBeLocked && !session.is_locked) {
        // Auto-lock session due to inactivity
        try {
          await session.lockSession('inactivity');
          await session.reload();

          logSecurity('Session auto-locked during optional auth', {
            userId: user.id,
            sessionToken: sessionToken.substring(0, 20) + '...',
            ip: req.ip,
            userAgent: req.get('User-Agent'),
          });
        } catch (lockError) {
          console.error('Failed to auto-lock session in optionalAuth:', lockError);
        }
      }

      // If session is locked, proceed without authentication (don't fail)
      if (session.is_locked || shouldBeLocked) {
        console.log('🔒 Session locked in optionalAuth - proceeding without authentication');
        req.user = null;
        return next();
      }

      // Update session activity for unlocked sessions
      await session.updateActivity();
    }

    // Set authenticated user
    req.user = user;
    req.token = token;

    next();
  } catch (error) {
    // Don't fail on optional auth errors (including session lock errors)
    console.log('⚠️ OptionalAuth error (proceeding without auth):', error.message);
    req.user = null;
    next();
  }
};

// Role-based authorization middleware
const requireRole = (...allowedRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: { message: 'Authentication required' },
        code: 'AUTH_REQUIRED',
      });
    }

    if (!allowedRoles.includes(req.user.role)) {
      logSecurity('Unauthorized role access attempt', {
        userId: req.user.id,
        userRole: req.user.role,
        requiredRoles: allowedRoles,
        endpoint: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      return res.status(403).json({
        success: false,
        error: { 
          message: 'Insufficient permissions',
          required: allowedRoles,
          current: req.user.role,
        },
        code: 'INSUFFICIENT_PERMISSIONS',
      });
    }

    next();
  };
};

// Admin only middleware
const requireAdmin = requireRole('admin');

// Staff or Admin middleware
const requireStaff = requireRole('admin', 'staff');

// Check if user owns resource or is admin/staff
const requireOwnershipOrStaff = (userIdField = 'user_id') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: { message: 'Authentication required' },
        code: 'AUTH_REQUIRED',
      });
    }

    // Admin and staff can access any resource
    if (['admin', 'staff'].includes(req.user.role)) {
      return next();
    }

    // Check ownership
    const resourceUserId = req.params[userIdField] || req.body[userIdField] || req.query[userIdField];
    
    if (!resourceUserId) {
      return res.status(400).json({
        success: false,
        error: { message: 'Resource user ID not found' },
        code: 'USER_ID_REQUIRED',
      });
    }

    if (parseInt(resourceUserId) !== req.user.id) {
      logSecurity('Unauthorized resource access attempt', {
        userId: req.user.id,
        targetUserId: resourceUserId,
        endpoint: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      return res.status(403).json({
        success: false,
        error: { message: 'Access denied - not resource owner' },
        code: 'NOT_RESOURCE_OWNER',
      });
    }

    next();
  };
};

// Rate limiting per user
const userRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const userRequests = new Map();

  return (req, res, next) => {
    if (!req.user) {
      return next(); // Skip rate limiting for unauthenticated users
    }

    const userId = req.user.id;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Get user's request history
    if (!userRequests.has(userId)) {
      userRequests.set(userId, []);
    }

    const requests = userRequests.get(userId);

    // Remove old requests outside the window
    const recentRequests = requests.filter(timestamp => timestamp > windowStart);
    userRequests.set(userId, recentRequests);

    // Check if user exceeded limit
    if (recentRequests.length >= maxRequests) {
      logSecurity('User rate limit exceeded', {
        userId,
        requestCount: recentRequests.length,
        limit: maxRequests,
        windowMs,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      return res.status(429).json({
        success: false,
        error: { 
          message: 'Too many requests',
          limit: maxRequests,
          window: `${windowMs / 1000}s`,
          retryAfter: Math.ceil(windowMs / 1000),
        },
        code: 'USER_RATE_LIMIT_EXCEEDED',
      });
    }

    // Add current request
    recentRequests.push(now);
    userRequests.set(userId, recentRequests);

    next();
  };
};

// Special authentication middleware for session unlock that bypasses session lock check
const authenticateTokenForUnlock = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        error: { message: 'Access token required' },
        code: 'TOKEN_REQUIRED',
      });
    }

    // Verify token
    const secret = process.env.JWT_SECRET || 'fallback_secret';
    const decoded = jwt.verify(token, secret);

    // Get user from database
    const user = await User.findByPk(decoded.id, {
      attributes: { exclude: ['password'] },
    });

    if (!user) {
      logSecurity('Invalid token - user not found', {
        userId: decoded.id,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      return res.status(401).json({
        success: false,
        error: { message: 'Invalid token - user not found' },
        code: 'USER_NOT_FOUND',
      });
    }

    // Check if user is active
    if (!user.is_active) {
      logSecurity('Inactive user attempted access', {
        userId: user.id,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      return res.status(401).json({
        success: false,
        error: { message: 'Account is inactive' },
        code: 'ACCOUNT_INACTIVE',
      });
    }

    // Get session information from JWT (jti claim contains session token)
    const sessionToken = decoded.jti;
    let session = null;

    if (sessionToken) {
      session = await UserSession.findOne({
        where: {
          session_token: sessionToken,
          is_active: true,
        },
        include: [{
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email', 'role', 'is_active'],
        }],
      });

      if (session) {
        // For unlock endpoint, we DON'T check if session is locked
        // This allows users to unlock their locked sessions

        // Update session activity (even for locked sessions during unlock)
        await session.updateActivity();
        req.session = session;
      }
    }

    // Attach user to request
    req.user = user;

    logAuth('token_validated_for_unlock', user.id, {
      email: user.email,
      role: user.role,
      sessionLocked: session?.is_locked || false,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      logSecurity('Invalid JWT token', {
        error: error.message,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      return res.status(401).json({
        success: false,
        error: { message: 'Invalid token' },
        code: 'INVALID_TOKEN',
      });
    }

    if (error.name === 'TokenExpiredError') {
      logSecurity('Expired JWT token', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      return res.status(401).json({
        success: false,
        error: { message: 'Token expired' },
        code: 'TOKEN_EXPIRED',
      });
    }

    console.error('Authentication error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Authentication failed' },
      code: 'AUTH_ERROR',
    });
  }
};

module.exports = {
  authenticateToken,
  authenticateTokenForUnlock,
  optionalAuth,
  requireRole,
  requireAdmin,
  requireStaff,
  requireOwnershipOrStaff,
  userRateLimit,
};

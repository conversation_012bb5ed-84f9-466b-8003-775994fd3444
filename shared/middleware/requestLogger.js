const { logRequest, logSecurity, logger } = require('../config/logger');

/**
 * Request Logging Middleware
 * Logs all HTTP requests with detailed information
 */

const requestLogger = (req, res, next) => {
  const startTime = Date.now();

  // Get real IP address (handles proxy forwarding)
  const getRealIP = (req) => {
    return req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
           req.headers['x-real-ip'] ||
           req.connection?.remoteAddress ||
           req.socket?.remoteAddress ||
           req.ip ||
           'unknown';
  };

  // Store real IP for use in logging
  req.realIP = getRealIP(req);

  // Log request start
  logger.debug('Request Started', {
    type: 'request_start',
    method: req.method,
    url: req.originalUrl,
    ip: req.realIP,
    userAgent: req.get('User-Agent') || 'unknown',
    timestamp: new Date().toISOString(),
    requestId: req.requestId || 'unknown',
  });

  // Capture original res.end to log when response is sent
  const originalEnd = res.end;

  res.end = function(chunk, encoding) {
    // Calculate response time
    const responseTime = Date.now() - startTime;

    // Log the request
    logRequest(req, res, responseTime);

    // Log slow requests (> 1 second)
    if (responseTime > 1000) {
      logger.warn('Slow Request Detected', {
        type: 'performance',
        method: req.method,
        url: req.originalUrl,
        ip: req.realIP,
        responseTime: `${responseTime}ms`,
        threshold: '1000ms',
        timestamp: new Date().toISOString(),
        requestId: req.requestId || 'unknown',
      });
    }

    // Call original end method
    originalEnd.call(this, chunk, encoding);
  };

  next();
};

/**
 * Security Event Logger Middleware
 * Logs suspicious activities and security events
 */
const securityLogger = (req, res, next) => {
  // Get real IP address helper
  const getRealIP = (req) => {
    return req.realIP ||
           req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
           req.headers['x-real-ip'] ||
           req.connection?.remoteAddress ||
           req.socket?.remoteAddress ||
           req.ip ||
           'unknown';
  };

  // Log suspicious patterns
  const suspiciousPatterns = [
    /\.\./,  // Directory traversal
    /<script/i,  // XSS attempts
    /union.*select/i,  // SQL injection
    /javascript:/i,  // JavaScript injection
    /eval\(/i,  // Code injection
  ];

  const url = req.originalUrl || req.url || '';
  const userAgent = req.get('User-Agent') || '';
  const body = JSON.stringify(req.body || {});
  const realIP = getRealIP(req);

  // Check for suspicious patterns
  suspiciousPatterns.forEach(pattern => {
    if (pattern.test(url) || pattern.test(userAgent) || pattern.test(body)) {
      logSecurity('Suspicious Request Pattern', {
        pattern: pattern.toString(),
        method: req.method || 'unknown',
        url: req.originalUrl || req.url || 'unknown',
        ip: realIP,
        userAgent: req.get('User-Agent') || 'unknown',
        body: req.body,
        headers: req.headers,
        requestId: req.requestId || 'unknown',
        timestamp: new Date().toISOString(),
      });
    }
  });
  
  // Log failed authentication attempts
  if ((req.originalUrl || '').includes('/auth/login') && req.method === 'POST') {
    const originalSend = res.send;
    res.send = function(data) {
      try {
        const response = typeof data === 'string' ? JSON.parse(data) : data;
        if (!response.success && response.error?.message?.includes('Invalid')) {
          logSecurity('Failed Login Attempt', {
            email: req.body?.email,
            ip: realIP,
            userAgent: req.get('User-Agent') || 'unknown',
            requestId: req.requestId || 'unknown',
            timestamp: new Date().toISOString(),
          });
        }
      } catch (error) {
        // Ignore parsing errors
      }
      originalSend.call(this, data);
    };
  }

  // Log multiple requests from same IP (potential DoS)
  // Use global object to track across requests (in production, use Redis or similar)
  if (!global.ipRequestCount) {
    global.ipRequestCount = {};
  }

  const now = Date.now();
  const timeWindow = 60000; // 1 minute

  if (!global.ipRequestCount[realIP]) {
    global.ipRequestCount[realIP] = [];
  }

  // Clean old requests
  global.ipRequestCount[realIP] = global.ipRequestCount[realIP].filter(time => now - time < timeWindow);

  // Add current request
  global.ipRequestCount[realIP].push(now);

  // Check for too many requests
  if (global.ipRequestCount[realIP].length > 100) { // More than 100 requests per minute
    logSecurity('High Request Rate Detected', {
      ip: realIP,
      requestCount: global.ipRequestCount[realIP].length,
      timeWindow: '1 minute',
      userAgent: req.get('User-Agent') || 'unknown',
      requestId: req.requestId || 'unknown',
      timestamp: new Date().toISOString(),
    });
  }
  
  next();
};

/**
 * Error Logging Middleware
 * Logs all application errors with context
 */
const errorLogger = (error, req, res, next) => {
  const { logError } = require('../config/logger');
  
  // Log the error with request context
  logError(error, req, {
    errorId: generateErrorId(),
    severity: getSeverityLevel(error),
  });
  
  // Don't expose internal errors in production
  if (process.env.NODE_ENV === 'production') {
    res.status(500).json({
      success: false,
      error: {
        message: 'Internal server error',
        errorId: generateErrorId(),
      },
      timestamp: new Date().toISOString(),
    });
  } else {
    res.status(500).json({
      success: false,
      error: {
        message: error.message,
        stack: error.stack,
        errorId: generateErrorId(),
      },
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Generate unique error ID for tracking
 */
function generateErrorId() {
  return `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Determine error severity level
 */
function getSeverityLevel(error) {
  if (error.name === 'ValidationError') return 'low';
  if (error.name === 'UnauthorizedError') return 'medium';
  if (error.name === 'DatabaseError') return 'high';
  if (error.name === 'SecurityError') return 'critical';
  return 'medium';
}

/**
 * Request ID Middleware
 * Adds unique request ID for tracking
 */
const requestId = (req, res, next) => {
  // Generate a more robust request ID
  const timestamp = Date.now();
  const randomPart = Math.random().toString(36).substr(2, 9);
  const processId = process.pid.toString(36);

  req.requestId = `REQ_${timestamp}_${processId}_${randomPart}`;
  res.setHeader('X-Request-ID', req.requestId);

  // Also add to response for easier debugging
  res.setHeader('X-Response-Time-Start', timestamp.toString());

  next();
};

module.exports = {
  requestLogger,
  securityLogger,
  errorLogger,
  requestId,
};

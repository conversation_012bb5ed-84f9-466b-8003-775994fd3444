/**
 * API Versioning Middleware
 * Supports both URL path versioning and header-based versioning
 */

const SUPPORTED_VERSIONS = ['v1', 'v2'];
const DEFAULT_VERSION = 'v1';
const LATEST_VERSION = 'v1';

/**
 * Extract version from URL path
 * @param {string} path - Request path
 * @returns {string|null} - Version string or null
 */
const extractVersionFromPath = (path) => {
  const versionMatch = path.match(/^\/api\/(v\d+)\//);
  return versionMatch ? versionMatch[1] : null;
};

/**
 * Extract version from Accept header
 * @param {string} acceptHeader - Accept header value
 * @returns {string|null} - Version string or null
 */
const extractVersionFromHeader = (acceptHeader) => {
  if (!acceptHeader) return null;
  
  // Look for application/vnd.hlenergy.v1+json format
  const versionMatch = acceptHeader.match(/application\/vnd\.hlenergy\.(v\d+)\+json/);
  return versionMatch ? versionMatch[1] : null;
};

/**
 * Versioning middleware
 */
const versioningMiddleware = (req, res, next) => {
  let version = null;
  
  // 1. Try to get version from URL path first (highest priority)
  version = extractVersionFromPath(req.path);
  
  // 2. If no version in path, try Accept header
  if (!version) {
    version = extractVersionFromHeader(req.get('Accept'));
  }
  
  // 3. If no version specified, use default
  if (!version) {
    version = DEFAULT_VERSION;
  }
  
  // 4. Validate version
  if (!SUPPORTED_VERSIONS.includes(version)) {
    return res.status(400).json({
      success: false,
      error: {
        message: `Unsupported API version: ${version}`,
        supported_versions: SUPPORTED_VERSIONS,
        default_version: DEFAULT_VERSION,
        latest_version: LATEST_VERSION,
      },
    });
  }
  
  // 5. Add version info to request object
  req.apiVersion = version;
  req.isLatestVersion = version === LATEST_VERSION;
  
  // 6. Add version headers to response
  res.set({
    'API-Version': version,
    'API-Supported-Versions': SUPPORTED_VERSIONS.join(', '),
    'API-Latest-Version': LATEST_VERSION,
    'API-Deprecated': version !== LATEST_VERSION ? 'true' : 'false',
  });
  
  // 7. Add deprecation warning for older versions
  if (version !== LATEST_VERSION) {
    res.set('Warning', `299 - "API version ${version} is deprecated. Please upgrade to ${LATEST_VERSION}"`);
  }
  
  next();
};

/**
 * Version-specific route handler
 * @param {string} targetVersion - Target version for this handler
 * @param {Function} handler - Route handler function
 * @returns {Function} - Middleware function
 */
const versionedHandler = (targetVersion, handler) => {
  return (req, res, next) => {
    if (req.apiVersion === targetVersion) {
      return handler(req, res, next);
    }
    next(); // Continue to next handler if version doesn't match
  };
};

/**
 * Create version-specific router
 * @param {string} version - Version string
 * @returns {Object} - Version info object
 */
const createVersionInfo = (version) => {
  return {
    version,
    isSupported: SUPPORTED_VERSIONS.includes(version),
    isLatest: version === LATEST_VERSION,
    isDefault: version === DEFAULT_VERSION,
  };
};

module.exports = {
  versioningMiddleware,
  versionedHandler,
  createVersionInfo,
  SUPPORTED_VERSIONS,
  DEFAULT_VERSION,
  LATEST_VERSION,
};

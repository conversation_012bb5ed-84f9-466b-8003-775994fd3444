import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt, { SignOptions } from 'jsonwebtoken';
import { validationResult } from 'express-validator';
import { StringValue } from 'ms';
import { pool } from '../config/database';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthRequest } from '../middleware/auth';

// Generate JWT token
const generateToken = (payload: any): string => {
  const secret = process.env.JWT_SECRET || 'fallback_secret';
  const options: SignOptions = {
    expiresIn: (process.env.JWT_EXPIRES_IN || '7d') as StringValue,
  };
  return jwt.sign(payload, secret, options);
};

// Generate refresh token
const generateRefreshToken = (payload: any): string => {
  const secret = process.env.JWT_REFRESH_SECRET || 'fallback_refresh_secret';
  const options: SignOptions = {
    expiresIn: (process.env.JWT_REFRESH_EXPIRES_IN || '30d') as StringValue,
  };
  return jwt.sign(payload, secret, options);
};

// Register user
export const register = asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: { message: 'Validation failed', details: errors.array() },
    });
  }

  const { name, email, password } = req.body;

  // Check if user already exists
  const [existingUsers] = await pool.execute(
    'SELECT id FROM users WHERE email = ?',
    [email]
  );

  if ((existingUsers as any[]).length > 0) {
    return res.status(400).json({
      success: false,
      error: { message: 'User with this email already exists' },
    });
  }

  // Hash password
  const saltRounds = 12;
  const hashedPassword = await bcrypt.hash(password, saltRounds);

  // Create user
  const [result] = await pool.execute(
    'INSERT INTO users (name, email, password) VALUES (?, ?, ?)',
    [name, email, hashedPassword]
  );

  const userId = (result as any).insertId;

  // Generate tokens
  const tokenPayload = { id: userId, email, role: 'client' };
  const token = generateToken(tokenPayload);
  const refreshToken = generateRefreshToken(tokenPayload);

  // Store refresh token
  const refreshExpiry = new Date();
  refreshExpiry.setDate(refreshExpiry.getDate() + 30);
  
  await pool.execute(
    'INSERT INTO refresh_tokens (user_id, token, expires_at) VALUES (?, ?, ?)',
    [userId, refreshToken, refreshExpiry]
  );

  res.status(201).json({
    success: true,
    data: {
      user: {
        id: userId,
        name,
        email,
        role: 'client',
      },
      token,
      refreshToken,
    },
    message: 'User registered successfully',
  });
});

// Login user
export const login = asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: { message: 'Validation failed', details: errors.array() },
    });
  }

  const { email, password } = req.body;

  // Find user
  const [users] = await pool.execute(
    'SELECT id, name, email, password, role FROM users WHERE email = ?',
    [email]
  );

  const user = (users as any[])[0];
  if (!user) {
    return res.status(401).json({
      success: false,
      error: { message: 'Invalid email or password' },
    });
  }

  // Check password
  const isPasswordValid = await bcrypt.compare(password, user.password);
  if (!isPasswordValid) {
    return res.status(401).json({
      success: false,
      error: { message: 'Invalid email or password' },
    });
  }

  // Generate tokens
  const tokenPayload = { id: user.id, email: user.email, role: user.role };
  const token = generateToken(tokenPayload);
  const refreshToken = generateRefreshToken(tokenPayload);

  // Store refresh token
  const refreshExpiry = new Date();
  refreshExpiry.setDate(refreshExpiry.getDate() + 30);
  
  await pool.execute(
    'INSERT INTO refresh_tokens (user_id, token, expires_at) VALUES (?, ?, ?)',
    [user.id, refreshToken, refreshExpiry]
  );

  res.json({
    success: true,
    data: {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
      },
      token,
      refreshToken,
    },
    message: 'Login successful',
  });
});

// Refresh token
export const refreshToken = asyncHandler(async (req: Request, res: Response) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    return res.status(401).json({
      success: false,
      error: { message: 'Refresh token is required' },
    });
  }

  try {
    const secret = process.env.JWT_REFRESH_SECRET || 'fallback_refresh_secret';
    const decoded = jwt.verify(refreshToken, secret) as any;
    
    // Check if refresh token exists and is valid
    const [tokens] = await pool.execute(
      'SELECT user_id FROM refresh_tokens WHERE token = ? AND expires_at > NOW()',
      [refreshToken]
    );

    if ((tokens as any[]).length === 0) {
      return res.status(401).json({
        success: false,
        error: { message: 'Invalid or expired refresh token' },
      });
    }

    // Get user data
    const [users] = await pool.execute(
      'SELECT id, name, email, role FROM users WHERE id = ?',
      [decoded.id]
    );

    const user = (users as any[])[0];
    if (!user) {
      return res.status(401).json({
        success: false,
        error: { message: 'User not found' },
      });
    }

    // Generate new tokens
    const tokenPayload = { id: user.id, email: user.email, role: user.role };
    const newToken = generateToken(tokenPayload);
    const newRefreshToken = generateRefreshToken(tokenPayload);

    // Remove old refresh token and store new one
    await pool.execute('DELETE FROM refresh_tokens WHERE token = ?', [refreshToken]);
    
    const refreshExpiry = new Date();
    refreshExpiry.setDate(refreshExpiry.getDate() + 30);
    
    await pool.execute(
      'INSERT INTO refresh_tokens (user_id, token, expires_at) VALUES (?, ?, ?)',
      [user.id, newRefreshToken, refreshExpiry]
    );

    res.json({
      success: true,
      data: {
        token: newToken,
        refreshToken: newRefreshToken,
      },
      message: 'Token refreshed successfully',
    });
  } catch (error) {
    res.status(401).json({
      success: false,
      error: { message: 'Invalid refresh token' },
    });
  }
});

// Logout user
export const logout = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { refreshToken } = req.body;

  if (refreshToken) {
    await pool.execute('DELETE FROM refresh_tokens WHERE token = ?', [refreshToken]);
  }

  res.json({
    success: true,
    message: 'Logout successful',
  });
});

// Get user profile
export const getProfile = asyncHandler(async (req: AuthRequest, res: Response) => {
  const [users] = await pool.execute(
    'SELECT id, name, email, role, created_at FROM users WHERE id = ?',
    [req.user!.id]
  );

  const user = (users as any[])[0];

  res.json({
    success: true,
    data: { user },
    message: 'Profile retrieved successfully',
  });
});

import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { pool } from '../config/database';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthRequest } from '../middleware/auth';
const emailQueueService = require('../services/emailQueueService');

// Submit contact form
export const submitContact = asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: { message: 'Validation failed', details: errors.array() },
    });
  }

  const { name, email, phone, message, source } = req.body;

  // Insert contact submission
  const [result] = await pool.execute(
    'INSERT INTO contact_submissions (name, email, phone, message, source) VALUES (?, ?, ?, ?, ?)',
    [name, email, phone || null, message, source || 'website']
  );

  const submissionId = (result as any).insertId;

  // Queue email notification to admin
  let emailQueued = false;
  let emailId = null;

  try {
    const adminEmail = process.env.CONTACT_NOTIFICATION_EMAIL || process.env.ADMIN_EMAIL || '<EMAIL>';

    const emailResult = await emailQueueService.queueContactNotificationEmail({
      submissionId,
      name,
      email,
      phone,
      message,
      source: source || 'website',
      adminEmail,
      ip_address: req.ip,
      user_agent: req.get('User-Agent'),
    });

    if (emailResult.success) {
      emailQueued = true;
      emailId = emailResult.emailId;
    }
  } catch (error) {
    console.error('Failed to queue contact notification email:', error);
    // Don't fail the contact submission if email queueing fails
  }

  res.status(201).json({
    success: true,
    submission: {
      id: submissionId,
      name,
      email,
      phone,
      message,
      source: source || 'website',
      status: 'new',
    },
    emailQueued,
    emailId,
    message: 'Contact form submitted successfully. We will get back to you soon!',
  });
});

// Get all contact submissions (admin/staff only)
export const getContacts = asyncHandler(async (req: AuthRequest, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const status = req.query.status as string;
  const offset = (page - 1) * limit;

  let query = 'SELECT * FROM contact_submissions';
  let countQuery = 'SELECT COUNT(*) as total FROM contact_submissions';
  const queryParams: any[] = [];

  if (status && ['new', 'in_progress', 'resolved'].includes(status)) {
    query += ' WHERE status = ?';
    countQuery += ' WHERE status = ?';
    queryParams.push(status);
  }

  query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
  queryParams.push(limit, offset);

  // Get contacts
  const [contacts] = await pool.execute(query, queryParams);

  // Get total count
  const [countResult] = await pool.execute(
    countQuery,
    status ? [status] : []
  );
  const total = (countResult as any[])[0].total;

  res.json({
    success: true,
    data: {
      contacts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    },
    message: 'Contact submissions retrieved successfully',
  });
});

// Update contact status (admin/staff only)
export const updateContactStatus = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: { message: 'Validation failed', details: errors.array() },
    });
  }

  const { id } = req.params;
  const { status } = req.body;

  // Check if contact exists
  const [contacts] = await pool.execute(
    'SELECT id FROM contact_submissions WHERE id = ?',
    [id]
  );

  if ((contacts as any[]).length === 0) {
    return res.status(404).json({
      success: false,
      error: { message: 'Contact submission not found' },
    });
  }

  // Update status
  await pool.execute(
    'UPDATE contact_submissions SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [status, id]
  );

  // Get updated contact
  const [updatedContact] = await pool.execute(
    'SELECT * FROM contact_submissions WHERE id = ?',
    [id]
  );

  res.json({
    success: true,
    data: { contact: (updatedContact as any[])[0] },
    message: 'Contact status updated successfully',
  });
});

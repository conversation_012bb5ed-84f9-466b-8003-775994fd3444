const leadService = require('../services/leadService');
const { validationResult } = require('express-validator');

/**
 * Lead Controller
 * Handles HTTP requests for lead management
 */

class LeadController {
  /**
   * Create a new lead
   * POST /api/v1/leads
   */
  async createLead(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }
      
      const userId = req.user?.id;
      const lead = await leadService.createLead(req.body, userId);
      
      console.log(`Lead created: ${lead.id}`, {
        leadId: lead.id,
        email: lead.email,
        userId,
      });
      
      res.status(201).json({
        success: true,
        message: 'Lead created successfully',
        data: lead,
      });
    } catch (error) {
      console.error('Error creating lead:', error);
      res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }
  
  /**
   * Create lead from contact submission
   * POST /api/v1/leads/from-contact/:submissionId
   */
  async createLeadFromContact(req, res) {
    try {
      const { submissionId } = req.params;
      const additionalData = req.body;
      
      const lead = await leadService.createLeadFromContactSubmission(
        submissionId,
        additionalData
      );
      
      console.log(`Lead created from contact submission: ${lead.id}`, {
        leadId: lead.id,
        submissionId,
        email: lead.email,
      });
      
      res.status(201).json({
        success: true,
        message: 'Lead created from contact submission successfully',
        data: lead,
      });
    } catch (error) {
      console.error('Error creating lead from contact submission:', error);
      res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }
  
  /**
   * Get all leads with filtering and pagination
   * GET /api/v1/leads
   */
  async getLeads(req, res) {
    try {
      const options = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 20,
        status: req.query.status,
        priority: req.query.priority,
        lead_source: req.query.lead_source,
        assigned_to: req.query.assigned_to,
        search: req.query.search,
        sortBy: req.query.sortBy || 'created_at',
        sortOrder: req.query.sortOrder || 'DESC',
        include_activities: req.query.include_activities === 'true',
      };
      
      // Handle array parameters
      if (options.status && typeof options.status === 'string') {
        options.status = options.status.split(',');
      }
      if (options.priority && typeof options.priority === 'string') {
        options.priority = options.priority.split(',');
      }
      if (options.lead_source && typeof options.lead_source === 'string') {
        options.lead_source = options.lead_source.split(',');
      }
      
      const result = await leadService.getLeads(options);
      
      res.json({
        success: true,
        message: 'Leads retrieved successfully',
        data: result.leads,
        pagination: result.pagination,
      });
    } catch (error) {
      console.error('Error getting leads:', error);
      res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }
  
  /**
   * Get lead by ID
   * GET /api/v1/leads/:id
   */
  async getLeadById(req, res) {
    try {
      const { id } = req.params;
      const includeActivities = req.query.include_activities !== 'false';
      
      const lead = await leadService.getLeadById(id, includeActivities);
      
      res.json({
        success: true,
        message: 'Lead retrieved successfully',
        data: lead,
      });
    } catch (error) {
      console.error('Error getting lead by ID:', error);
      const statusCode = error.message === 'Lead not found' ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error.message,
      });
    }
  }
  
  /**
   * Update lead
   * PUT /api/v1/leads/:id
   */
  async updateLead(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }
      
      const { id } = req.params;
      const userId = req.user?.id;
      
      const lead = await leadService.updateLead(id, req.body, userId);
      
      console.log(`Lead updated: ${id}`, {
        leadId: id,
        userId,
        updates: Object.keys(req.body),
      });
      
      res.json({
        success: true,
        message: 'Lead updated successfully',
        data: lead,
      });
    } catch (error) {
      console.error('Error updating lead:', error);
      const statusCode = error.message === 'Lead not found' ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error.message,
      });
    }
  }
  
  /**
   * Delete lead
   * DELETE /api/v1/leads/:id
   */
  async deleteLead(req, res) {
    try {
      const { id } = req.params;
      
      const result = await leadService.deleteLead(id);
      
      console.log(`Lead deleted: ${id}`, {
        leadId: id,
        userId: req.user?.id,
      });
      
      res.json({
        success: true,
        message: result.message,
      });
    } catch (error) {
      console.error('Error deleting lead:', error);
      const statusCode = error.message === 'Lead not found' ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error.message,
      });
    }
  }
  
  /**
   * Get lead statistics
   * GET /api/v1/leads/stats
   */
  async getLeadStats(req, res) {
    try {
      const { start_date, end_date } = req.query;
      
      const startDate = start_date ? new Date(start_date) : null;
      const endDate = end_date ? new Date(end_date) : null;
      
      const stats = await leadService.getLeadStats(startDate, endDate);
      
      res.json({
        success: true,
        message: 'Lead statistics retrieved successfully',
        data: stats,
      });
    } catch (error) {
      console.error('Error getting lead statistics:', error);
      res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }
  
  /**
   * Bulk update leads
   * PATCH /api/v1/leads/bulk
   */
  async bulkUpdateLeads(req, res) {
    try {
      const { lead_ids, updates } = req.body;
      
      if (!lead_ids || !Array.isArray(lead_ids) || lead_ids.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'lead_ids array is required',
        });
      }
      
      if (!updates || typeof updates !== 'object') {
        return res.status(400).json({
          success: false,
          message: 'updates object is required',
        });
      }
      
      const userId = req.user?.id;
      const results = [];
      
      for (const leadId of lead_ids) {
        try {
          const lead = await leadService.updateLead(leadId, updates, userId);
          results.push({ id: leadId, success: true, data: lead });
        } catch (error) {
          results.push({ id: leadId, success: false, error: error.message });
        }
      }
      
      console.log(`Bulk update completed for ${lead_ids.length} leads`, {
        leadIds: lead_ids,
        userId,
        updates: Object.keys(updates),
      });
      
      res.json({
        success: true,
        message: 'Bulk update completed',
        data: results,
      });
    } catch (error) {
      console.error('Error in bulk update:', error);
      res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }
  
  /**
   * Assign lead to user
   * PATCH /api/v1/leads/:id/assign
   */
  async assignLead(req, res) {
    try {
      const { id } = req.params;
      const { assigned_to } = req.body;
      
      if (!assigned_to) {
        return res.status(400).json({
          success: false,
          message: 'assigned_to is required',
        });
      }
      
      const userId = req.user?.id;
      const lead = await leadService.updateLead(id, { assigned_to }, userId);
      
      console.log(`Lead assigned: ${id} to user ${assigned_to}`, {
        leadId: id,
        assignedTo: assigned_to,
        assignedBy: userId,
      });
      
      res.json({
        success: true,
        message: 'Lead assigned successfully',
        data: lead,
      });
    } catch (error) {
      console.error('Error assigning lead:', error);
      const statusCode = error.message === 'Lead not found' ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error.message,
      });
    }
  }
  
  /**
   * Update lead status
   * PATCH /api/v1/leads/:id/status
   */
  async updateLeadStatus(req, res) {
    try {
      const { id } = req.params;
      const { status, notes } = req.body;
      
      if (!status) {
        return res.status(400).json({
          success: false,
          message: 'status is required',
        });
      }
      
      const updates = { status };
      if (notes) {
        updates.notes = notes;
      }
      
      const userId = req.user?.id;
      const lead = await leadService.updateLead(id, updates, userId);
      
      console.log(`Lead status updated: ${id} to ${status}`, {
        leadId: id,
        status,
        userId,
      });
      
      res.json({
        success: true,
        message: 'Lead status updated successfully',
        data: lead,
      });
    } catch (error) {
      console.error('Error updating lead status:', error);
      const statusCode = error.message === 'Lead not found' ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error.message,
      });
    }
  }
}

module.exports = new LeadController();

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * SystemStats Model
 * Caches calculated system statistics for performance
 */

const SystemStats = sequelize.define('SystemStats', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  stat_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      isIn: {
        args: [[
          'daily-summary',
          'weekly-summary',
          'monthly-summary',
          'user-stats',
          'log-stats',
          'connection-stats',
          'performance-stats',
          'health-summary',
        ]],
        msg: 'Invalid stat type',
      },
    },
  },
  stat_data: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: {},
  },
  calculated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
}, {
  tableName: 'system_stats',
  timestamps: false,
  indexes: [
    {
      fields: ['expires_at'],
    },
    {
      fields: ['calculated_at'],
    },
  ],
});

// Class methods for stats management
SystemStats.getCachedStats = async function(statType) {
  const { Op } = require('sequelize');
  
  const stats = await this.findOne({
    where: {
      stat_type: statType,
      expires_at: {
        [Op.gt]: new Date(),
      },
    },
  });

  return stats ? stats.stat_data : null;
};

SystemStats.setCachedStats = async function(statType, statData, ttlMinutes = 15) {
  const expiresAt = new Date();
  expiresAt.setMinutes(expiresAt.getMinutes() + ttlMinutes);

  return await this.upsert({
    stat_type: statType,
    stat_data: statData,
    calculated_at: new Date(),
    expires_at: expiresAt,
  });
};

SystemStats.calculateUserStats = async function() {
  const { User, UserSession } = require('./index');
  const { Op } = require('sequelize');

  const now = new Date();
  const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const last7d = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const last30d = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  const [
    totalUsers,
    activeUsers24h,
    activeUsers7d,
    activeUsers30d,
    newUsers24h,
    newUsers7d,
    newUsers30d,
    activeSessions,
  ] = await Promise.all([
    User.count(),
    UserSession.count({
      where: {
        last_activity: { [Op.gte]: last24h },
      },
    }),
    UserSession.count({
      where: {
        last_activity: { [Op.gte]: last7d },
      },
    }),
    UserSession.count({
      where: {
        last_activity: { [Op.gte]: last30d },
      },
    }),
    User.count({
      where: {
        created_at: { [Op.gte]: last24h },
      },
    }),
    User.count({
      where: {
        created_at: { [Op.gte]: last7d },
      },
    }),
    User.count({
      where: {
        created_at: { [Op.gte]: last30d },
      },
    }),
    UserSession.count({
      where: {
        is_active: true,
        expires_at: { [Op.gt]: now },
      },
    }),
  ]);

  const userStats = {
    total: totalUsers,
    active: {
      last24h: activeUsers24h,
      last7d: activeUsers7d,
      last30d: activeUsers30d,
    },
    new: {
      last24h: newUsers24h,
      last7d: newUsers7d,
      last30d: newUsers30d,
    },
    sessions: {
      active: activeSessions,
    },
    calculatedAt: new Date().toISOString(),
  };

  await this.setCachedStats('user-stats', userStats, 15); // Cache for 15 minutes
  return userStats;
};

SystemStats.calculateLogStats = async function() {
  const { Log } = require('./index');
  const { Op } = require('sequelize');

  const now = new Date();
  const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const last7d = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

  const [
    totalLogs,
    logs24h,
    logs7d,
    errorLogs24h,
    warnLogs24h,
    logsByLevel,
    logsByType,
  ] = await Promise.all([
    Log.count(),
    Log.count({
      where: {
        created_at: { [Op.gte]: last24h },
      },
    }),
    Log.count({
      where: {
        created_at: { [Op.gte]: last7d },
      },
    }),
    Log.count({
      where: {
        level: 'error',
        created_at: { [Op.gte]: last24h },
      },
    }),
    Log.count({
      where: {
        level: 'warn',
        created_at: { [Op.gte]: last24h },
      },
    }),
    Log.findAll({
      attributes: [
        'level',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      ],
      where: {
        created_at: { [Op.gte]: last24h },
      },
      group: ['level'],
      raw: true,
    }),
    Log.findAll({
      attributes: [
        'type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      ],
      where: {
        created_at: { [Op.gte]: last24h },
      },
      group: ['type'],
      order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
      limit: 10,
      raw: true,
    }),
  ]);

  const logStats = {
    total: totalLogs,
    recent: {
      last24h: logs24h,
      last7d: logs7d,
    },
    errors: {
      last24h: errorLogs24h,
    },
    warnings: {
      last24h: warnLogs24h,
    },
    byLevel: logsByLevel.reduce((acc, item) => {
      acc[item.level] = parseInt(item.count);
      return acc;
    }, {}),
    byType: logsByType.map(item => ({
      type: item.type,
      count: parseInt(item.count),
    })),
    calculatedAt: new Date().toISOString(),
  };

  await this.setCachedStats('log-stats', logStats, 10); // Cache for 10 minutes
  return logStats;
};

SystemStats.calculateConnectionStats = async function() {
  // This would integrate with Socket.io server to get real-time connection data
  const socketServer = global.socketServer; // Assuming global reference
  
  let connectionStats = {
    current: 0,
    peak24h: 0,
    totalRooms: 0,
    userConnections: 0,
    adminConnections: 0,
    calculatedAt: new Date().toISOString(),
  };

  if (socketServer) {
    const connectedUsers = socketServer.getConnectedUsers();
    const rooms = socketServer.rooms;

    connectionStats = {
      current: connectedUsers.length,
      peak24h: connectedUsers.length, // TODO: Track peak connections
      totalRooms: rooms.size,
      userConnections: connectedUsers.filter(u => u.userData.role === 'user').length,
      adminConnections: connectedUsers.filter(u => u.userData.role === 'admin').length,
      calculatedAt: new Date().toISOString(),
    };
  }

  await this.setCachedStats('connection-stats', connectionStats, 5); // Cache for 5 minutes
  return connectionStats;
};

SystemStats.cleanupExpiredStats = async function() {
  const { Op } = require('sequelize');
  
  const result = await this.destroy({
    where: {
      expires_at: {
        [Op.lt]: new Date(),
      },
    },
  });

  return result;
};

module.exports = SystemStats;

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const WorkerStatus = sequelize.define('WorkerStatus', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    worker_type: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: 'email',
      unique: true,
    },
    status: {
      type: DataTypes.ENUM('starting', 'running', 'stopping', 'stopped', 'error'),
      allowNull: false,
      defaultValue: 'stopped',
    },
    pid: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    started_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    stopped_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    last_heartbeat: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    uptime_seconds: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    processed_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    error_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    last_error: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
    },
  }, {
    tableName: 'worker_status',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['status'],
      },
      {
        fields: ['last_heartbeat'],
      },
    ],
  });

  // Class methods
  WorkerStatus.updateWorkerStatus = async function(workerType, status, data = {}) {
    const now = new Date();
    const updateData = {
      status,
      last_heartbeat: now,
      ...data,
    };

    // Set specific timestamps based on status
    if (status === 'starting' || status === 'running') {
      if (!data.started_at) {
        updateData.started_at = now;
      }
      updateData.stopped_at = null;
    } else if (status === 'stopped' || status === 'error') {
      updateData.stopped_at = now;
    }

    // Calculate uptime if worker is running
    if (status === 'running' && data.started_at) {
      const startTime = new Date(data.started_at);
      updateData.uptime_seconds = Math.floor((now - startTime) / 1000);
    }

    const [worker, created] = await this.findOrCreate({
      where: { worker_type: workerType },
      defaults: {
        worker_type: workerType,
        status: 'stopped',
        metadata: { version: '1.0.0' },
        ...updateData,
      },
    });

    if (!created) {
      await worker.update(updateData);
    }

    return worker;
  };

  WorkerStatus.getWorkerStatus = async function(workerType = 'email') {
    const worker = await this.findOne({
      where: { worker_type: workerType },
    });

    if (!worker) {
      // Create default record if not exists
      return await this.create({
        worker_type: workerType,
        status: 'stopped',
        metadata: { version: '1.0.0' },
      });
    }

    // Check if worker is considered stale (no heartbeat for 2 minutes)
    const now = new Date();
    const lastHeartbeat = worker.last_heartbeat;
    const isStale = lastHeartbeat && (now - new Date(lastHeartbeat)) > 2 * 60 * 1000;

    if (isStale && worker.status === 'running') {
      // Mark as stopped if stale
      await worker.update({
        status: 'stopped',
        stopped_at: now,
      });
      worker.status = 'stopped';
      worker.stopped_at = now;
    }

    return worker;
  };

  WorkerStatus.sendHeartbeat = async function(workerType = 'email', data = {}) {
    const worker = await this.findOne({
      where: { worker_type: workerType },
    });

    if (worker && worker.status === 'running') {
      const now = new Date();
      const updateData = {
        last_heartbeat: now,
        ...data,
      };

      // Calculate uptime
      if (worker.started_at) {
        updateData.uptime_seconds = Math.floor((now - new Date(worker.started_at)) / 1000);
      }

      await worker.update(updateData);
      return worker;
    }

    return null;
  };

module.exports = WorkerStatus;

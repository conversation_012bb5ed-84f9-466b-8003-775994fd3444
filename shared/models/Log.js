const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Log Model
 * Stores application logs in the database for better querying and analytics
 */

const Log = sequelize.define('Log', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  level: {
    type: DataTypes.ENUM('error', 'warn', 'info', 'http', 'debug'),
    allowNull: false,
    validate: {
      isIn: {
        args: [['error', 'warn', 'info', 'http', 'debug']],
        msg: 'Level must be error, warn, info, http, or debug',
      },
    },
  },
  message: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  type: {
    type: DataTypes.STRING(50),
    allowNull: true,
    validate: {
      isIn: {
        args: [[
          'request', 'error', 'authentication', 'security', 'database', 'performance', 'system', 'route_not_found',
          'email_queue', 'email_worker', 'email',
          // Socket.io log types
          'socket', 'socket_server', 'socket_connection', 'socket_authentication', 'socket_room', 'socket_analytics',
          'socket_communication', 'socket_performance', 'socket_metrics_request', 'socket_api_error', 'socket_health_error',
          'socket_logs_error', 'socket_memory_error', 'socket_broadcast', 'socket_error', 'socket_cleanup',
          // Memory management log types
          'memory_cleanup', 'memory_management', 'memory_monitoring', 'memory_alert', 'manual_cleanup', 'cleanup_error',
          // Server management log types
          'server_shutdown', 'shutdown_error',
          // New logging types
          'request_start', 'test', 'debug'
        ]],
        msg: 'Type must be a valid log type',
      },
    },
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id',
    },
  },
  request_id: {
    type: DataTypes.STRING(50),
    allowNull: true,
  },
  ip_address: {
    type: DataTypes.STRING(45), // IPv6 support
    allowNull: true,
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  method: {
    type: DataTypes.STRING(10),
    allowNull: true,
  },
  url: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  status_code: {
    type: DataTypes.INTEGER,
    allowNull: true,
  },
  response_time: {
    type: DataTypes.INTEGER, // in milliseconds
    allowNull: true,
  },
  error_stack: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'logs',
  timestamps: false, // We'll manage created_at manually
  indexes: [
    {
      fields: ['level'],
    },
    {
      fields: ['type'],
    },
    {
      fields: ['user_id'],
    },
    {
      fields: ['request_id'],
    },
    {
      fields: ['ip_address'],
    },
    {
      fields: ['status_code'],
    },
    {
      fields: ['created_at'],
    },
    {
      fields: ['level', 'type'],
    },
    {
      fields: ['created_at', 'level'],
    },
  ],
});

// Instance methods
Log.prototype.toSafeJSON = function() {
  const values = { ...this.get() };
  // Remove sensitive information if needed
  if (values.metadata && values.metadata.password) {
    delete values.metadata.password;
  }
  return values;
};

// Class methods
Log.getLogsByLevel = async function(level, limit = 100, offset = 0) {
  return this.findAll({
    where: { level },
    order: [['created_at', 'DESC']],
    limit,
    offset,
  });
};

Log.getLogsByType = async function(type, limit = 100, offset = 0) {
  return this.findAll({
    where: { type },
    order: [['created_at', 'DESC']],
    limit,
    offset,
  });
};

Log.getLogsByUser = async function(userId, limit = 100, offset = 0) {
  return this.findAll({
    where: { user_id: userId },
    order: [['created_at', 'DESC']],
    limit,
    offset,
  });
};

Log.getLogsByDateRange = async function(startDate, endDate, limit = 100, offset = 0) {
  const { Op } = require('sequelize');
  return this.findAll({
    where: {
      created_at: {
        [Op.between]: [startDate, endDate],
      },
    },
    order: [['created_at', 'DESC']],
    limit,
    offset,
  });
};

Log.getErrorLogs = async function(limit = 100, offset = 0) {
  return this.findAll({
    where: { level: 'error' },
    order: [['created_at', 'DESC']],
    limit,
    offset,
    include: [
      {
        association: 'user',
        attributes: ['id', 'name', 'email'],
      },
    ],
  });
};

Log.getRequestStats = async function(timeframe = '24h') {
  const { Op } = require('sequelize');
  
  let startDate;
  switch (timeframe) {
    case '1h':
      startDate = new Date(Date.now() - 60 * 60 * 1000);
      break;
    case '24h':
      startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
  }
  
  const stats = await this.findAll({
    attributes: [
      'level',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      [sequelize.fn('AVG', sequelize.col('response_time')), 'avg_response_time'],
    ],
    where: {
      type: 'request',
      created_at: {
        [Op.gte]: startDate,
      },
    },
    group: ['level'],
    raw: true,
  });
  
  return stats;
};

Log.getTopErrors = async function(limit = 10, timeframe = '24h') {
  const { Op } = require('sequelize');
  
  let startDate;
  switch (timeframe) {
    case '1h':
      startDate = new Date(Date.now() - 60 * 60 * 1000);
      break;
    case '24h':
      startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
  }
  
  return this.findAll({
    attributes: [
      'message',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      [sequelize.fn('MAX', sequelize.col('created_at')), 'last_occurrence'],
    ],
    where: {
      level: 'error',
      created_at: {
        [Op.gte]: startDate,
      },
    },
    group: ['message'],
    order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
    limit,
    raw: true,
  });
};

Log.cleanupOldLogs = async function(daysToKeep = 30) {
  const { Op } = require('sequelize');
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
  
  const result = await this.destroy({
    where: {
      created_at: {
        [Op.lt]: cutoffDate,
      },
    },
  });
  
  return result;
};

Log.getLogStatistics = async function() {
  const { Op } = require('sequelize');
  const now = new Date();
  const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const last7d = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  
  const [total, last24hCount, last7dCount, errorCount, warningCount] = await Promise.all([
    this.count(),
    this.count({ where: { created_at: { [Op.gte]: last24h } } }),
    this.count({ where: { created_at: { [Op.gte]: last7d } } }),
    this.count({ where: { level: 'error' } }),
    this.count({ where: { level: 'warn' } }),
  ]);
  
  return {
    total,
    last24h: last24hCount,
    last7d: last7dCount,
    errors: errorCount,
    warnings: warningCount,
  };
};

// Associations
Log.associate = function(models) {
  Log.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  });
};

module.exports = Log;

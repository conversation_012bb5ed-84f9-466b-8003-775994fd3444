const { DataTypes, Op } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * RefreshToken Model
 * Manages JWT refresh tokens for secure authentication
 */

const RefreshToken = sequelize.define('RefreshToken', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  token: {
    type: DataTypes.STRING(500),
    allowNull: false,
    unique: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id',
    },
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  is_revoked: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  },
  revoked_at: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  revoked_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id',
    },
  },
  ip_address: {
    type: DataTypes.STRING(45), // IPv6 support
    allowNull: true,
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'refresh_tokens',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['token'],
    },
    {
      fields: ['user_id'],
    },
    {
      fields: ['expires_at'],
    },
    {
      fields: ['is_revoked'],
    },
    {
      fields: ['created_at'],
    },
  ],
});

// Instance methods
RefreshToken.prototype.isExpired = function() {
  return this.expires_at < new Date();
};

RefreshToken.prototype.isValid = function() {
  return !this.is_revoked && !this.isExpired();
};

RefreshToken.prototype.revoke = async function(revokedBy = null) {
  return this.update({
    is_revoked: true,
    revoked_at: new Date(),
    revoked_by: revokedBy,
  });
};

// Class methods
RefreshToken.createToken = async function(userId, token, expiresIn = 30, ipAddress = null, userAgent = null) {
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + expiresIn); // Default 30 days
  
  return this.create({
    token,
    user_id: userId,
    expires_at: expiresAt,
    ip_address: ipAddress,
    user_agent: userAgent,
  });
};

RefreshToken.findValidToken = async function(token) {
  const refreshToken = await this.findOne({
    where: { token },
    include: [
      {
        association: 'user',
        attributes: ['id', 'name', 'email', 'role', 'is_active'],
      },
    ],
  });
  
  if (!refreshToken || !refreshToken.isValid()) {
    return null;
  }
  
  return refreshToken;
};

RefreshToken.revokeAllUserTokens = async function(userId, revokedBy = null) {
  return this.update(
    {
      is_revoked: true,
      revoked_at: new Date(),
      revoked_by: revokedBy,
    },
    {
      where: {
        user_id: userId,
        is_revoked: false,
      },
    }
  );
};

RefreshToken.cleanupExpiredTokens = async function() {
  const result = await this.destroy({
    where: {
      expires_at: {
        [Op.lt]: new Date(),
      },
    },
  });
  
  console.log(`🧹 Cleaned up ${result} expired refresh tokens`);
  return result;
};

RefreshToken.getTokenStats = async function() {
  const total = await this.count();
  const active = await this.count({
    where: {
      is_revoked: false,
      expires_at: {
        [Op.gt]: new Date(),
      },
    },
  });
  const expired = await this.count({
    where: {
      expires_at: {
        [Op.lt]: new Date(),
      },
    },
  });
  const revoked = await this.count({
    where: {
      is_revoked: true,
    },
  });
  
  return {
    total,
    active,
    expired,
    revoked,
  };
};

// Associations
RefreshToken.associate = function(models) {
  RefreshToken.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  });
  
  RefreshToken.belongsTo(models.User, {
    foreignKey: 'revoked_by',
    as: 'revokedByUser',
  });
};

module.exports = RefreshToken;

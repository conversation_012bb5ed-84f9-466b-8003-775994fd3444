const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * BackgroundJob Model
 * Manages background job queue for worker system
 */

const BackgroundJob = sequelize.define('BackgroundJob', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  job_type: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      isIn: {
        args: [[
          // Stats jobs
          'calculate-daily-stats',
          'calculate-weekly-stats', 
          'calculate-monthly-stats',
          'cleanup-old-stats',
          // Health jobs
          'check-database-health',
          'check-api-health',
          'check-frontend-health',
          'check-memory-usage',
          'check-external-services',
          'comprehensive-health-check',
          // Maintenance jobs
          'cleanup-old-logs',
          'cleanup-expired-sessions',
          'optimize-database',
          'backup-critical-data',
          // Email jobs
          'send-daily-digest',
          'send-weekly-report',
          'send-alert-notification'
        ]],
        msg: 'Invalid job type',
      },
    },
  },
  job_data: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed', 'retrying'),
    defaultValue: 'pending',
    allowNull: false,
  },
  priority: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false,
    validate: {
      min: 0,
      max: 10,
    },
  },
  attempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false,
  },
  max_attempts: {
    type: DataTypes.INTEGER,
    defaultValue: 3,
    allowNull: false,
  },
  scheduled_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  started_at: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  completed_at: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'background_jobs',
  timestamps: false, // We manage timestamps manually
  indexes: [
    {
      fields: ['status', 'priority'],
    },
    {
      fields: ['scheduled_at'],
    },
    {
      fields: ['job_type'],
    },
    {
      fields: ['created_at'],
    },
  ],
});

// Class methods for job management
BackgroundJob.enqueue = async function(jobType, jobData = {}, options = {}) {
  const {
    priority = 0,
    maxAttempts = 3,
    scheduledAt = new Date(),
  } = options;

  return await this.create({
    job_type: jobType,
    job_data: jobData,
    priority,
    max_attempts: maxAttempts,
    scheduled_at: scheduledAt,
  });
};

BackgroundJob.dequeue = async function(limit = 1) {
  const { Op } = require('sequelize');
  
  return await this.findAll({
    where: {
      status: 'pending',
      scheduled_at: {
        [Op.lte]: new Date(),
      },
    },
    order: [
      ['priority', 'DESC'],
      ['scheduled_at', 'ASC'],
    ],
    limit,
  });
};

BackgroundJob.markAsProcessing = async function(jobId) {
  return await this.update(
    {
      status: 'processing',
      started_at: new Date(),
      updated_at: new Date(),
    },
    {
      where: { id: jobId },
    }
  );
};

BackgroundJob.markAsCompleted = async function(jobId) {
  return await this.update(
    {
      status: 'completed',
      completed_at: new Date(),
      updated_at: new Date(),
    },
    {
      where: { id: jobId },
    }
  );
};

BackgroundJob.markAsFailed = async function(jobId, errorMessage) {
  const job = await this.findByPk(jobId);
  if (!job) return null;

  const newAttempts = job.attempts + 1;
  const shouldRetry = newAttempts < job.max_attempts;

  return await this.update(
    {
      status: shouldRetry ? 'retrying' : 'failed',
      attempts: newAttempts,
      error_message: errorMessage,
      scheduled_at: shouldRetry ? new Date(Date.now() + 30000) : job.scheduled_at, // Retry in 30 seconds
      updated_at: new Date(),
    },
    {
      where: { id: jobId },
    }
  );
};

BackgroundJob.getStats = async function() {
  const { Op } = require('sequelize');
  
  const [pending, processing, completed, failed] = await Promise.all([
    this.count({ where: { status: 'pending' } }),
    this.count({ where: { status: 'processing' } }),
    this.count({ where: { status: 'completed' } }),
    this.count({ where: { status: 'failed' } }),
  ]);

  return {
    pending,
    processing,
    completed,
    failed,
    total: pending + processing + completed + failed,
  };
};

BackgroundJob.cleanupOldJobs = async function(daysToKeep = 7) {
  const { Op } = require('sequelize');
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

  const result = await this.destroy({
    where: {
      status: {
        [Op.in]: ['completed', 'failed'],
      },
      completed_at: {
        [Op.lt]: cutoffDate,
      },
    },
  });

  return result;
};

module.exports = BackgroundJob;

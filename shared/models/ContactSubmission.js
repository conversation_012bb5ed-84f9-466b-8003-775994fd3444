const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * ContactSubmission Model
 * Represents contact form submissions in the HLenergy system
 */

const ContactSubmission = sequelize.define('ContactSubmission', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Name cannot be empty',
      },
      len: {
        args: [2, 100],
        msg: 'Name must be between 2 and 100 characters',
      },
    },
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      isEmail: {
        msg: 'Must be a valid email address',
      },
      notEmpty: {
        msg: 'Email cannot be empty',
      },
    },
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    validate: {
      len: {
        args: [10, 20],
        msg: 'Phone number must be between 10 and 20 characters',
      },
    },
  },
  message: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Message cannot be empty',
      },
      len: {
        args: [10, 5000],
        msg: 'Message must be between 10 and 5000 characters',
      },
    },
  },
  status: {
    type: DataTypes.ENUM('new', 'in_progress', 'resolved', 'closed'),
    allowNull: false,
    defaultValue: 'new',
    validate: {
      isIn: {
        args: [['new', 'in_progress', 'resolved', 'closed']],
        msg: 'Status must be new, in_progress, resolved, or closed',
      },
    },
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    allowNull: false,
    defaultValue: 'medium',
    validate: {
      isIn: {
        args: [['low', 'medium', 'high', 'urgent']],
        msg: 'Priority must be low, medium, high, or urgent',
      },
    },
  },
  source: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'website',
    validate: {
      isIn: {
        args: [['website', 'email', 'phone', 'referral', 'social_media', 'other']],
        msg: 'Source must be a valid option',
      },
    },
  },
  ip_address: {
    type: DataTypes.STRING(45), // IPv6 support
    allowNull: true,
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  assigned_to: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id',
    },
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id',
    },
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
  },
  resolved_at: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  closed_at: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'contact_submissions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['email'],
    },
    {
      fields: ['status'],
    },
    {
      fields: ['priority'],
    },
    {
      fields: ['source'],
    },
    {
      fields: ['assigned_to'],
    },
    {
      fields: ['created_by'],
    },
    {
      fields: ['created_at'],
    },
  ],
});

// Instance methods
ContactSubmission.prototype.markAsResolved = async function(userId = null) {
  return this.update({
    status: 'resolved',
    resolved_at: new Date(),
    ...(userId && { created_by: userId }),
  });
};

ContactSubmission.prototype.markAsClosed = async function(userId = null) {
  return this.update({
    status: 'closed',
    closed_at: new Date(),
    ...(userId && { created_by: userId }),
  });
};

ContactSubmission.prototype.assignTo = async function(userId) {
  return this.update({
    assigned_to: userId,
    status: this.status === 'new' ? 'in_progress' : this.status,
  });
};

ContactSubmission.prototype.addNote = async function(note, userId = null) {
  const currentNotes = this.notes || '';
  const timestamp = new Date().toISOString();
  const userInfo = userId ? ` (User ID: ${userId})` : '';
  const newNote = `[${timestamp}]${userInfo}: ${note}`;
  
  return this.update({
    notes: currentNotes ? `${currentNotes}\n\n${newNote}` : newNote,
  });
};

// Class methods
ContactSubmission.getStatusCounts = async function() {
  const counts = await this.findAll({
    attributes: [
      'status',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
    ],
    group: ['status'],
    raw: true,
  });
  
  return counts.reduce((acc, item) => {
    acc[item.status] = parseInt(item.count);
    return acc;
  }, {});
};

ContactSubmission.getPriorityCounts = async function() {
  const counts = await this.findAll({
    attributes: [
      'priority',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
    ],
    group: ['priority'],
    raw: true,
  });
  
  return counts.reduce((acc, item) => {
    acc[item.priority] = parseInt(item.count);
    return acc;
  }, {});
};

ContactSubmission.getRecentSubmissions = async function(limit = 10) {
  return this.findAll({
    order: [['created_at', 'DESC']],
    limit,
    include: [
      {
        association: 'assignedUser',
        attributes: ['id', 'name', 'email'],
      },
    ],
  });
};

// Associations
ContactSubmission.associate = function(models) {
  ContactSubmission.belongsTo(models.User, {
    foreignKey: 'assigned_to',
    as: 'assignedUser',
  });
  
  ContactSubmission.belongsTo(models.User, {
    foreignKey: 'created_by',
    as: 'createdByUser',
  });
};

module.exports = ContactSubmission;

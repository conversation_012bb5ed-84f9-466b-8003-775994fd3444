const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Project = sequelize.define('Project', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  
  // Project Identification
  projectNumber: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: 'Auto-generated project number (e.g., HL-2024-001)'
  },
  
  title: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 255]
    }
  },
  
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  // Relationships
  customerId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'customers',
      key: 'id'
    }
  },
  
  assignedTo: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  
  teamMembers: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of user IDs assigned to this project'
  },
  
  // Project Details
  type: {
    type: DataTypes.ENUM(
      'energy_audit',
      'consultation',
      'implementation',
      'monitoring',
      'compliance',
      'financing',
      'maintenance'
    ),
    allowNull: false,
    defaultValue: 'consultation'
  },
  
  category: {
    type: DataTypes.ENUM('residential', 'commercial', 'industrial', 'municipal'),
    allowNull: false,
    defaultValue: 'commercial'
  },
  
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    allowNull: false,
    defaultValue: 'medium'
  },
  
  status: {
    type: DataTypes.ENUM(
      'planning',
      'in_progress',
      'on_hold',
      'completed',
      'cancelled',
      'archived'
    ),
    allowNull: false,
    defaultValue: 'planning'
  },
  
  // Timeline
  startDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  endDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  estimatedDuration: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Estimated duration in days'
  },
  
  actualDuration: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Actual duration in days'
  },
  
  // Financial Information
  estimatedCost: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: true
  },
  
  actualCost: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: true
  },
  
  budget: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: true
  },
  
  currency: {
    type: DataTypes.STRING(3),
    allowNull: false,
    defaultValue: 'USD'
  },
  
  // Energy Metrics
  currentEnergyUsage: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Current energy consumption data'
  },
  
  projectedSavings: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Projected energy and cost savings'
  },
  
  actualSavings: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Actual achieved savings'
  },
  
  // Location Information
  siteAddress: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  siteCity: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  
  siteState: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  
  siteZipCode: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  
  siteCountry: {
    type: DataTypes.STRING(100),
    allowNull: true,
    defaultValue: 'United States'
  },
  
  // Technical Details
  scope: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Project scope and deliverables'
  },
  
  requirements: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Technical requirements and specifications'
  },
  
  deliverables: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'List of project deliverables'
  },
  
  // Progress Tracking
  completionPercentage: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0,
      max: 100
    }
  },
  
  milestones: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of project milestones'
  },
  
  // Risk Management
  risks: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: 'Identified project risks'
  },
  
  issues: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: 'Current project issues'
  },
  
  // Communication
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: 'Project tags for categorization'
  },
  
  // Quality Assurance
  qualityScore: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 10
    }
  },
  
  clientSatisfaction: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 5
    }
  },
  
  // Metadata
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'projects',
  timestamps: true,
  indexes: [
    {
      fields: ['projectNumber']
    },
    {
      fields: ['customerId']
    },
    {
      fields: ['status']
    },
    {
      fields: ['type']
    },
    {
      fields: ['assignedTo']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['startDate']
    },
    {
      fields: ['endDate']
    }
  ]
});

// Virtual fields
Project.prototype.getFormattedSiteAddress = function() {
  const parts = [this.siteAddress, this.siteCity, this.siteState, this.siteZipCode, this.siteCountry].filter(Boolean);
  return parts.join(', ');
};

Project.prototype.getDaysRemaining = function() {
  if (!this.endDate) return null;
  const today = new Date();
  const end = new Date(this.endDate);
  const diffTime = end - today;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

Project.prototype.isOverdue = function() {
  if (!this.endDate) return false;
  return new Date() > new Date(this.endDate) && this.status !== 'completed';
};

module.exports = Project;

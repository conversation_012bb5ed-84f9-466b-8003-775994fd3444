const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * HealthCheck Model
 * Stores health check results for system monitoring
 */

const HealthCheck = sequelize.define('HealthCheck', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  check_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      isIn: {
        args: [[
          'database-connectivity',
          'database-performance',
          'api-health',
          'frontend-health',
          'memory-usage',
          'cpu-usage',
          'disk-space',
          'socket-server',
          'email-service',
          'external-apis',
          'comprehensive-health',
        ]],
        msg: 'Invalid check name',
      },
    },
  },
  status: {
    type: DataTypes.ENUM('healthy', 'degraded', 'unhealthy'),
    allowNull: false,
  },
  response_time_ms: {
    type: DataTypes.INTEGER,
    allowNull: true,
  },
  details: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
  },
  checked_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'health_checks',
  timestamps: false,
  indexes: [
    {
      fields: ['check_name', 'checked_at'],
    },
    {
      fields: ['status'],
    },
    {
      fields: ['checked_at'],
    },
  ],
});

// Class methods for health check management
HealthCheck.recordCheck = async function(checkName, status, responseTime = null, details = {}) {
  return await this.create({
    check_name: checkName,
    status,
    response_time_ms: responseTime,
    details,
    checked_at: new Date(),
  });
};

HealthCheck.getLatestChecks = async function() {
  const { Op } = require('sequelize');

  // Get the latest check for each check type using a subquery approach
  const latestChecks = await sequelize.query(`
    SELECT
      hc1.check_name,
      hc1.status,
      hc1.response_time_ms,
      hc1.details,
      hc1.checked_at as latest_check
    FROM health_checks hc1
    INNER JOIN (
      SELECT check_name, MAX(checked_at) as max_checked_at
      FROM health_checks
      GROUP BY check_name
    ) hc2 ON hc1.check_name = hc2.check_name AND hc1.checked_at = hc2.max_checked_at
    ORDER BY hc1.checked_at DESC
  `, {
    type: sequelize.QueryTypes.SELECT
  });

  return latestChecks;
};

HealthCheck.getOverallHealth = async function() {
  const latestChecks = await this.getLatestChecks();
  
  if (latestChecks.length === 0) {
    return {
      status: 'unknown',
      message: 'No health checks available',
      checks: [],
    };
  }

  const healthyCount = latestChecks.filter(check => check.status === 'healthy').length;
  const degradedCount = latestChecks.filter(check => check.status === 'degraded').length;
  const unhealthyCount = latestChecks.filter(check => check.status === 'unhealthy').length;

  let overallStatus = 'healthy';
  let message = 'All systems operational';

  if (unhealthyCount > 0) {
    overallStatus = 'unhealthy';
    message = `${unhealthyCount} critical issue(s) detected`;
  } else if (degradedCount > 0) {
    overallStatus = 'degraded';
    message = `${degradedCount} service(s) experiencing issues`;
  }

  return {
    status: overallStatus,
    message,
    summary: {
      healthy: healthyCount,
      degraded: degradedCount,
      unhealthy: unhealthyCount,
      total: latestChecks.length,
    },
    checks: latestChecks,
    lastUpdated: new Date().toISOString(),
  };
};

HealthCheck.checkDatabaseHealth = async function() {
  const startTime = Date.now();
  
  try {
    // Test database connectivity
    await sequelize.authenticate();
    
    // Test a simple query
    const result = await sequelize.query('SELECT 1 as test', { type: sequelize.QueryTypes.SELECT });
    
    const responseTime = Date.now() - startTime;
    
    let status = 'healthy';
    if (responseTime > 1000) {
      status = 'degraded';
    } else if (responseTime > 5000) {
      status = 'unhealthy';
    }

    const details = {
      responseTime,
      queryResult: result[0]?.test === 1,
      connectionPool: {
        total: sequelize.connectionManager.pool.size,
        used: sequelize.connectionManager.pool.used,
        waiting: sequelize.connectionManager.pool.pending,
      },
    };

    await this.recordCheck('database-connectivity', status, responseTime, details);
    
    return { status, responseTime, details };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    const details = {
      error: error.message,
      responseTime,
    };

    await this.recordCheck('database-connectivity', 'unhealthy', responseTime, details);
    
    return { status: 'unhealthy', responseTime, details };
  }
};

HealthCheck.checkMemoryUsage = async function() {
  const startTime = Date.now();
  
  try {
    const memUsage = process.memoryUsage();
    const memUsageMB = {
      rss: Math.round(memUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024),
    };

    const heapUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
    
    let status = 'healthy';
    if (heapUsagePercent > 80) {
      status = 'degraded';
    } else if (heapUsagePercent > 95) {
      status = 'unhealthy';
    }

    const responseTime = Date.now() - startTime;
    const details = {
      memory: memUsageMB,
      heapUsagePercent: Math.round(heapUsagePercent * 100) / 100,
      uptime: Math.round(process.uptime()),
    };

    await this.recordCheck('memory-usage', status, responseTime, details);
    
    return { status, responseTime, details };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    const details = { error: error.message };

    await this.recordCheck('memory-usage', 'unhealthy', responseTime, details);
    
    return { status: 'unhealthy', responseTime, details };
  }
};

HealthCheck.checkSocketServer = async function() {
  const startTime = Date.now();
  
  try {
    const socketServer = global.socketServer; // Assuming global reference
    
    if (!socketServer || !socketServer.io) {
      throw new Error('Socket server not available');
    }

    const connectedUsers = socketServer.getConnectedUsers();
    const responseTime = Date.now() - startTime;
    
    const details = {
      isRunning: true,
      connectedUsers: connectedUsers.length,
      totalRooms: socketServer.rooms.size,
      serverHealth: 'operational',
    };

    await this.recordCheck('socket-server', 'healthy', responseTime, details);
    
    return { status: 'healthy', responseTime, details };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    const details = {
      error: error.message,
      isRunning: false,
    };

    await this.recordCheck('socket-server', 'unhealthy', responseTime, details);
    
    return { status: 'unhealthy', responseTime, details };
  }
};

HealthCheck.cleanupOldChecks = async function(daysToKeep = 7) {
  const { Op } = require('sequelize');
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

  const result = await this.destroy({
    where: {
      checked_at: {
        [Op.lt]: cutoffDate,
      },
    },
  });

  return result;
};

module.exports = HealthCheck;

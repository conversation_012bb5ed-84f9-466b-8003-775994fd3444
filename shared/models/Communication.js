const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Communication = sequelize.define('Communication', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  
  // Relationships
  customerId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'customers',
      key: 'id'
    }
  },
  
  projectId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'projects',
      key: 'id'
    }
  },
  
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    },
    comment: 'User who initiated or logged this communication'
  },
  
  // Communication Details
  type: {
    type: DataTypes.ENUM(
      'email',
      'phone_call',
      'meeting',
      'video_call',
      'text_message',
      'in_person',
      'note',
      'task',
      'reminder'
    ),
    allowNull: false
  },
  
  direction: {
    type: DataTypes.ENUM('inbound', 'outbound', 'internal'),
    allowNull: false,
    defaultValue: 'outbound'
  },
  
  subject: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  
  content: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  summary: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Brief summary of the communication'
  },
  
  // Timing
  scheduledAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'For scheduled communications'
  },
  
  occurredAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When the communication actually happened'
  },
  
  duration: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Duration in minutes for calls/meetings'
  },
  
  // Status and Outcome
  status: {
    type: DataTypes.ENUM('scheduled', 'completed', 'cancelled', 'no_response', 'failed'),
    allowNull: false,
    defaultValue: 'completed'
  },
  
  outcome: {
    type: DataTypes.ENUM(
      'successful',
      'follow_up_needed',
      'not_interested',
      'callback_requested',
      'meeting_scheduled',
      'proposal_requested',
      'closed_deal',
      'lost_opportunity'
    ),
    allowNull: true
  },
  
  // Contact Information
  contactMethod: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Specific contact method used (email address, phone number, etc.)'
  },
  
  participants: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: 'List of participants in the communication'
  },
  
  // Follow-up
  followUpRequired: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  
  followUpDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  followUpNotes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  // Attachments and References
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of attachment file references'
  },
  
  relatedDocuments: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: 'References to related documents or files'
  },
  
  // Categorization
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: 'Tags for categorizing communications'
  },
  
  category: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Communication category (sales, support, technical, etc.)'
  },
  
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    allowNull: false,
    defaultValue: 'medium'
  },
  
  // Sentiment and Quality
  sentiment: {
    type: DataTypes.ENUM('positive', 'neutral', 'negative'),
    allowNull: true,
    comment: 'Overall sentiment of the communication'
  },
  
  qualityRating: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 5
    },
    comment: 'Quality rating of the communication (1-5)'
  },
  
  // Integration Data
  externalId: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'ID from external system (email provider, phone system, etc.)'
  },
  
  source: {
    type: DataTypes.STRING(100),
    allowNull: true,
    defaultValue: 'manual',
    comment: 'Source of the communication record (manual, email_sync, phone_system, etc.)'
  },
  
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Additional metadata from external systems'
  },
  
  // Automation
  isAutomated: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether this was an automated communication'
  },
  
  templateUsed: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Template used for automated communications'
  },
  
  // Tracking
  isRead: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether the communication has been read/reviewed'
  },
  
  readAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  readBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  
  // Metadata
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'communications',
  timestamps: true,
  indexes: [
    {
      fields: ['customerId']
    },
    {
      fields: ['projectId']
    },
    {
      fields: ['userId']
    },
    {
      fields: ['type']
    },
    {
      fields: ['status']
    },
    {
      fields: ['occurredAt']
    },
    {
      fields: ['followUpDate']
    },
    {
      fields: ['followUpRequired']
    },
    {
      fields: ['isRead']
    }
  ]
});

// Virtual fields
Communication.prototype.getFormattedDuration = function() {
  if (!this.duration) return null;
  const hours = Math.floor(this.duration / 60);
  const minutes = this.duration % 60;
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  }
  return `${minutes}m`;
};

Communication.prototype.isOverdue = function() {
  if (!this.followUpDate || !this.followUpRequired) return false;
  return new Date() > new Date(this.followUpDate) && this.status !== 'completed';
};

module.exports = Communication;

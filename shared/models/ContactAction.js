const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ContactAction = sequelize.define('ContactAction', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  action: {
    type: DataTypes.ENUM('email', 'phone', 'whatsapp', 'address'),
    allowNull: false,
    comment: 'Type of contact action taken by user'
  },
  page: {
    type: DataTypes.STRING(100),
    allowNull: false,
    defaultValue: 'contact',
    comment: 'Page where the action was taken'
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'User agent string for device/browser identification'
  },
  referrer: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'Referrer URL if available'
  },
  ip_address: {
    type: DataTypes.STRING(45),
    allowNull: true,
    comment: 'IP address of the user (IPv4 or IPv6)'
  },
  session_id: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Session identifier if available'
  },
  timestamp: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'When the action was taken'
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'contact_actions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['action']
    },
    {
      fields: ['page']
    },
    {
      fields: ['timestamp']
    },
    {
      fields: ['created_at']
    }
  ],
  comment: 'Tracks user interactions with contact methods (email, phone, WhatsApp, address)'
});

module.exports = ContactAction;

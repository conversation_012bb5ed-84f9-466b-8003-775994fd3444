const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const { sequelize } = require('../config/database');

/**
 * User Model
 * Represents users in the HLenergy system
 */

const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Name cannot be empty',
      },
      len: {
        args: [2, 100],
        msg: 'Name must be between 2 and 100 characters',
      },
    },
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: {
      name: 'unique_email',
      msg: 'Email address already exists',
    },
    validate: {
      isEmail: {
        msg: 'Must be a valid email address',
      },
      notEmpty: {
        msg: 'Email cannot be empty',
      },
    },
  },
  password: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Password cannot be empty',
      },
      len: {
        args: [6, 255],
        msg: 'Password must be at least 6 characters long',
      },
    },
  },
  role: {
    type: DataTypes.ENUM('admin', 'staff', 'client'),
    allowNull: false,
    defaultValue: 'client',
    validate: {
      isIn: {
        args: [['admin', 'staff', 'client']],
        msg: 'Role must be admin, staff, or client',
      },
    },
  },
  email_verified: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  },
  email_verification_token: {
    type: DataTypes.STRING(255),
    allowNull: true,
  },
  password_reset_token: {
    type: DataTypes.STRING(255),
    allowNull: true,
  },
  password_reset_expires: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  last_login: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  login_attempts: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
  },
  locked_until: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  },
  profile: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'users',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['email'],
    },
    {
      fields: ['role'],
    },
    {
      fields: ['is_active'],
    },
    {
      fields: ['created_at'],
    },
  ],
});

// Instance methods
User.prototype.toJSON = function() {
  const values = { ...this.get() };
  delete values.password;
  delete values.email_verification_token;
  delete values.password_reset_token;
  return values;
};

User.prototype.validatePassword = async function(password) {
  return await bcrypt.compare(password, this.password);
};

User.prototype.isLocked = function() {
  return !!(this.locked_until && this.locked_until > Date.now());
};

User.prototype.incrementLoginAttempts = async function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.locked_until && this.locked_until < Date.now()) {
    return this.update({
      login_attempts: 1,
      locked_until: null,
    });
  }
  
  const updates = { login_attempts: this.login_attempts + 1 };
  
  // Lock account after 5 failed attempts for 2 hours
  if (this.login_attempts + 1 >= 5 && !this.isLocked()) {
    updates.locked_until = Date.now() + 2 * 60 * 60 * 1000; // 2 hours
  }
  
  return this.update(updates);
};

User.prototype.resetLoginAttempts = async function() {
  return this.update({
    login_attempts: 0,
    locked_until: null,
    last_login: new Date(),
  });
};

// Class methods
User.hashPassword = async function(password) {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
};

User.generateToken = function(length = 32) {
  const crypto = require('crypto');
  return crypto.randomBytes(length).toString('hex');
};

// Hooks
User.beforeCreate(async (user) => {
  if (user.password) {
    user.password = await User.hashPassword(user.password);
  }
});

User.beforeUpdate(async (user) => {
  if (user.changed('password')) {
    user.password = await User.hashPassword(user.password);
  }
});

// Associations
User.associate = function(models) {
  User.hasMany(models.ContactSubmission, {
    foreignKey: 'created_by',
    as: 'contactSubmissions',
  });

  User.hasMany(models.RefreshToken, {
    foreignKey: 'user_id',
    as: 'refreshTokens',
  });

  User.hasMany(models.UserSession, {
    foreignKey: 'user_id',
    as: 'sessions',
  });

  User.hasOne(models.AdminPin, {
    foreignKey: 'user_id',
    as: 'adminPin',
  });

  User.hasMany(models.BiometricAuth, {
    foreignKey: 'user_id',
    as: 'biometricCredentials',
  });

  User.hasMany(models.EmailVerification, {
    foreignKey: 'user_id',
    as: 'emailVerifications',
  });

  User.hasMany(models.PasswordReset, {
    foreignKey: 'user_id',
    as: 'passwordResets',
  });
};

module.exports = User;

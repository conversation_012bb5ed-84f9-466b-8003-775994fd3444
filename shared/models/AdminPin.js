const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const { sequelize } = require('../config/database');

/**
 * AdminPin Model
 * Stores secure PIN codes for admin authentication
 */

const AdminPin = sequelize.define('AdminPin', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id',
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  },
  pin_hash: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: 'Hashed PIN code',
  },
  pin_salt: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: 'Salt used for PIN hashing',
  },
  attempts: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Number of failed PIN attempts',
  },
  locked_until: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'PIN locked until this timestamp',
  },
  lockout_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Number of times PIN has been locked (for progressive lockout)',
  },
  last_used: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Last successful PIN usage',
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'PIN expiration date (optional)',
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether this PIN is active',
  },
  device_fingerprint: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Device fingerprint for additional security',
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional metadata (IP, user agent, etc.)',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'admin_pins',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['user_id', 'is_active'],
      name: 'unique_active_user_pin',
      where: {
        is_active: true
      }
    },
    {
      fields: ['user_id'],
    },
    {
      fields: ['is_active'],
    },
    {
      fields: ['locked_until'],
    },
    {
      fields: ['expires_at'],
    },
  ],
});

// Instance methods
AdminPin.prototype.validatePin = async function(pin) {
  try {
    // Check if PIN is locked
    if (this.isLocked()) {
      throw new Error('PIN is temporarily locked due to too many failed attempts');
    }

    // Check if PIN has expired
    if (this.expires_at && new Date() > this.expires_at) {
      throw new Error('PIN has expired');
    }

    // Validate PIN (bcrypt handles salt internally, no need to concatenate)
    const isValid = await bcrypt.compare(pin, this.pin_hash);
    
    if (isValid) {
      // Reset attempts, lockout count, and update last used
      await this.update({
        attempts: 0,
        locked_until: null,
        lockout_count: 0, // Reset progressive lockout on successful unlock
        last_used: new Date(),
      });
      console.log('PIN validated successfully - lockout count reset');
      return true;
    } else {
      // Increment failed attempts
      await this.incrementAttempts();
      return false;
    }
  } catch (error) {
    console.error('PIN validation error:', error);
    throw error;
  }
};

AdminPin.prototype.isLocked = function() {
  return !!(this.locked_until && this.locked_until > new Date());
};

AdminPin.prototype.incrementAttempts = async function() {
  const newAttempts = this.attempts + 1;
  const updates = { attempts: newAttempts };

  // Lock PIN after 3 failed attempts with progressive lockout times
  if (newAttempts >= 3) {
    const newLockoutCount = this.lockout_count + 1;
    updates.lockout_count = newLockoutCount;

    // Progressive lockout times: 1min, 2min, 4min, 8min, 16min, 32min, max 60min
    const lockoutMinutes = Math.min(Math.pow(2, newLockoutCount - 1), 60);
    updates.locked_until = new Date(Date.now() + lockoutMinutes * 60 * 1000);

    console.log(`PIN locked for ${lockoutMinutes} minutes (lockout #${newLockoutCount})`);
  }

  return this.update(updates);
};

AdminPin.prototype.resetAttempts = async function() {
  return this.update({
    attempts: 0,
    locked_until: null,
    lockout_count: 0, // Reset progressive lockout
  });
};

// Check if PIN matches without throwing errors or updating attempts
AdminPin.prototype.checkPin = async function(pin) {
  try {
    // Don't check lock status or expiration, just compare PIN
    console.log('checkPin called with:', { pin, hash: this.pin_hash });
    const isValid = await bcrypt.compare(pin, this.pin_hash);
    console.log('bcrypt.compare result:', isValid);
    return isValid;
  } catch (error) {
    console.error('PIN check error:', error);
    return false;
  }
};

// Class methods
AdminPin.hashPin = async function(pin) {
  try {
    const saltRounds = 12;
    const hash = await bcrypt.hash(pin, saltRounds);

    // Extract salt from bcrypt hash for storage (for compatibility)
    const salt = hash.substring(0, 29); // bcrypt salt is first 29 characters

    return { hash, salt };
  } catch (error) {
    console.error('PIN hashing error:', error);
    throw error;
  }
};

AdminPin.createPin = async function(userId, pin, metadata = {}) {
  try {
    const { hash, salt } = await AdminPin.hashPin(pin);

    // Check if PIN was recently used
    const isRecentlyUsed = await AdminPin.isPinRecentlyUsed(userId, pin, 10);
    if (isRecentlyUsed) {
      throw new Error('PIN was recently used. Please choose a different PIN.');
    }

    // Check if user already has any PIN record (due to old unique constraint)
    const existingPin = await AdminPin.findOne({
      where: { user_id: userId }
    });

    if (existingPin) {
      // Update existing record to avoid constraint violation
      return await existingPin.update({
        pin_hash: hash,
        pin_salt: salt,
        metadata: metadata,
        is_active: true,
        attempts: 0,
        locked_until: null,
        last_used: null,
        expires_at: null,
      });
    } else {
      // Create new PIN record
      return await AdminPin.create({
        user_id: userId,
        pin_hash: hash,
        pin_salt: salt,
        metadata: metadata,
        is_active: true,
      });
    }
  } catch (error) {
    console.error('PIN creation error:', error);
    throw error;
  }
};

AdminPin.findActiveByUserId = async function(userId) {
  return await AdminPin.findOne({
    where: {
      user_id: userId,
      is_active: true,
    },
  });
};

// Get recent PIN history for a user (last 10 PINs)
AdminPin.getRecentPinHistory = async function(userId, limit = 10) {
  return await AdminPin.findAll({
    where: {
      user_id: userId,
    },
    order: [['created_at', 'DESC']],
    limit: limit,
  });
};

// Check if a PIN was used recently
AdminPin.isPinRecentlyUsed = async function(userId, pin, limit = 10) {
  try {
    const recentPins = await AdminPin.getRecentPinHistory(userId, limit);

    for (const pinRecord of recentPins) {
      const isMatch = await bcrypt.compare(pin, pinRecord.pin_hash);
      if (isMatch) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('PIN history check error:', error);
    return false;
  }
};

// Associations
AdminPin.associate = function(models) {
  AdminPin.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  });
};

module.exports = AdminPin;

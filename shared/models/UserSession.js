const { DataTypes, Op } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * UserSession Model
 * Manages server-side user sessions with lock state and activity tracking
 * This replaces the vulnerable localStorage-based session management
 */

const UserSession = sequelize.define('UserSession', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id',
    },
  },
  session_token: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    comment: 'Unique session identifier',
  },
  device_fingerprint: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Device identification for security',
  },
  ip_address: {
    type: DataTypes.STRING(45), // IPv6 support
    allowNull: true,
    comment: 'IP address of the session',
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Browser/device user agent',
  },
  is_locked: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether session is currently locked',
  },
  locked_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When session was locked',
  },
  lock_reason: {
    type: DataTypes.ENUM('inactivity', 'admin_action', 'security_breach', 'manual'),
    allowNull: true,
    comment: 'Reason for session lock',
  },
  last_activity: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'Last activity timestamp',
  },
  idle_timeout_minutes: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 15,
    comment: 'Idle timeout in minutes for this session',
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: false,
    comment: 'When session expires',
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether session is active',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'user_sessions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['session_token'],
    },
    {
      fields: ['user_id'],
    },
    {
      fields: ['is_locked'],
    },
    {
      fields: ['is_active'],
    },
    {
      fields: ['last_activity'],
    },
    {
      fields: ['expires_at'],
    },
    {
      fields: ['created_at'],
    },
  ],
});

// Instance methods
UserSession.prototype.isExpired = function() {
  return this.expires_at < new Date();
};

UserSession.prototype.isValid = function() {
  return this.is_active && !this.isExpired();
};

UserSession.prototype.shouldBeLocked = function(idleTimeoutMinutes = null) {
  if (!this.isValid() || this.is_locked) {
    return false;
  }

  // Use session-specific timeout or provided timeout
  const timeoutMinutes = idleTimeoutMinutes || this.idle_timeout_minutes || 15;
  const idleTimeoutMs = timeoutMinutes * 60 * 1000;
  const timeSinceActivity = Date.now() - this.last_activity.getTime();

  return timeSinceActivity > idleTimeoutMs;
};

UserSession.prototype.lockSession = async function(reason = 'inactivity') {
  return this.update({
    is_locked: true,
    locked_at: new Date(),
    lock_reason: reason,
  });
};

UserSession.prototype.unlockSession = async function() {
  return this.update({
    is_locked: false,
    locked_at: null,
    lock_reason: null,
    last_activity: new Date(), // Reset activity on unlock
  });
};

UserSession.prototype.updateActivity = async function() {
  return this.update({
    last_activity: new Date(),
  });
};

UserSession.prototype.terminate = async function() {
  return this.update({
    is_active: false,
  });
};

// Class methods
UserSession.createSession = async function(sessionToken, userId, expiresAt, ipAddress = null, userAgent = null, deviceFingerprint = null, idleTimeoutMinutes = 15) {
  // Terminate any existing active sessions for this user (optional - for single session per user)
  // await this.terminateUserSessions(userId);

  return this.create({
    session_token: sessionToken,
    user_id: userId,
    expires_at: expiresAt,
    ip_address: ipAddress,
    user_agent: userAgent,
    device_fingerprint: deviceFingerprint,
    idle_timeout_minutes: idleTimeoutMinutes,
    last_activity: new Date(),
  });
};

UserSession.findBySessionToken = async function(sessionToken) {
  return this.findOne({
    where: {
      session_token: sessionToken,
      is_active: true,
    },
    include: [
      {
        association: 'user',
        attributes: ['id', 'name', 'email', 'role', 'is_active'],
      },
    ],
  });
};

UserSession.findValidSession = async function(sessionToken) {
  const session = await this.findBySessionToken(sessionToken);

  if (!session || !session.isValid()) {
    return null;
  }

  return session;
};

UserSession.terminateUserSessions = async function(userId) {
  return this.update(
    {
      is_active: false,
    },
    {
      where: {
        user_id: userId,
        is_active: true,
      },
    }
  );
};

UserSession.cleanupExpiredSessions = async function() {
  const result = await this.update(
    {
      is_active: false,
    },
    {
      where: {
        expires_at: {
          [Op.lt]: new Date(),
        },
        is_active: true,
      },
    }
  );

  // Only log if sessions were actually cleaned up
  if (result[0] > 0) {
    console.log(`🧹 Cleaned up ${result[0]} expired user sessions`);
  }
  return result[0];
};

UserSession.lockInactiveSessions = async function(idleTimeoutMinutes = null) {
  // If no timeout provided, lock sessions based on their individual timeout settings
  let whereCondition;

  if (idleTimeoutMinutes) {
    // Use provided timeout for all sessions
    const idleTimeoutMs = idleTimeoutMinutes * 60 * 1000;
    const cutoffTime = new Date(Date.now() - idleTimeoutMs);

    whereCondition = {
      is_active: true,
      is_locked: false,
      last_activity: {
        [Op.lt]: cutoffTime,
      },
      expires_at: {
        [Op.gt]: new Date(),
      },
    };
  } else {
    // Use each session's individual timeout setting
    whereCondition = sequelize.literal(`
      is_active = 1
      AND is_locked = 0
      AND expires_at > NOW()
      AND TIMESTAMPDIFF(MINUTE, last_activity, NOW()) >= idle_timeout_minutes
    `);
  }

  const result = await this.update(
    {
      is_locked: true,
      locked_at: new Date(),
      lock_reason: 'inactivity',
    },
    {
      where: whereCondition,
    }
  );

  // Only log if sessions were actually locked
  if (result[0] > 0) {
    console.log(`🔒 Locked ${result[0]} inactive sessions`);
  }
  return result[0];
};

UserSession.getSessionStats = async function() {
  const total = await this.count();
  const active = await this.count({
    where: {
      is_active: true,
      expires_at: {
        [Op.gt]: new Date(),
      },
    },
  });
  const locked = await this.count({
    where: {
      is_active: true,
      is_locked: true,
      expires_at: {
        [Op.gt]: new Date(),
      },
    },
  });
  const expired = await this.count({
    where: {
      expires_at: {
        [Op.lt]: new Date(),
      },
    },
  });
  
  return {
    total,
    active,
    locked,
    expired,
  };
};

UserSession.getUserActiveSessions = async function(userId) {
  return this.findAll({
    where: {
      user_id: userId,
      is_active: true,
      expires_at: {
        [Op.gt]: new Date(),
      },
    },
    order: [['last_activity', 'DESC']],
  });
};

// Associations
UserSession.associate = function(models) {
  UserSession.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  });
};

module.exports = UserSession;

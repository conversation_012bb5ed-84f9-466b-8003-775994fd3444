const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Customer = sequelize.define('Customer', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  
  // Basic Information
  firstName: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 100]
    }
  },
  
  lastName: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 100]
    }
  },
  
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    validate: {
      len: [0, 20]
    }
  },
  
  // Company Information
  companyName: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  
  jobTitle: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  
  industry: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  
  companySize: {
    type: DataTypes.ENUM('1-10', '11-50', '51-200', '201-1000', '1000+'),
    allowNull: true
  },
  
  // Address Information
  address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  city: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  
  state: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  
  zipCode: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  
  country: {
    type: DataTypes.STRING(100),
    allowNull: true,
    defaultValue: 'United States'
  },
  
  // CRM Fields
  leadSource: {
    type: DataTypes.STRING(100),
    allowNull: true,
    defaultValue: 'website'
  },
  
  leadScore: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0,
      max: 100
    }
  },
  
  status: {
    type: DataTypes.ENUM('lead', 'prospect', 'customer', 'inactive'),
    allowNull: false,
    defaultValue: 'lead'
  },
  
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    allowNull: false,
    defaultValue: 'medium'
  },
  
  // Energy Profile
  energyNeeds: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object containing energy requirements and preferences'
  },
  
  currentEnergyProvider: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  
  monthlyEnergyBudget: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  
  // Relationship Management
  assignedTo: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of tags for categorization'
  },
  
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  // Lifecycle Tracking
  firstContactDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  lastContactDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  nextFollowUpDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  // Business Metrics
  estimatedValue: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Estimated project value'
  },
  
  actualValue: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Actual project value when closed'
  },
  
  conversionDate: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Date when lead converted to customer'
  },
  
  // Preferences
  preferredContactMethod: {
    type: DataTypes.ENUM('email', 'phone', 'text', 'in_person'),
    allowNull: false,
    defaultValue: 'email'
  },
  
  communicationFrequency: {
    type: DataTypes.ENUM('daily', 'weekly', 'monthly', 'quarterly'),
    allowNull: false,
    defaultValue: 'weekly'
  },
  
  // Metadata
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'customers',
  timestamps: true,
  indexes: [
    {
      fields: ['email']
    },
    {
      fields: ['status']
    },
    {
      fields: ['leadScore']
    },
    {
      fields: ['assignedTo']
    },
    {
      fields: ['nextFollowUpDate']
    },
    {
      fields: ['leadSource']
    }
  ]
});

// Virtual fields
Customer.prototype.getFullName = function() {
  return `${this.firstName} ${this.lastName}`;
};

Customer.prototype.getFormattedAddress = function() {
  const parts = [this.address, this.city, this.state, this.zipCode, this.country].filter(Boolean);
  return parts.join(', ');
};

module.exports = Customer;

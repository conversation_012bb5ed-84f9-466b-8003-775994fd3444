const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const PushSubscription = sequelize.define('PushSubscription', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      },
      onDelete: 'CASCADE'
    },
    endpoint: {
      type: DataTypes.TEXT, // TEXT for long URLs (no direct indexing)
      allowNull: false
    },
    endpointHash: {
      type: DataTypes.STRING(64), // SHA-256 hash for indexing and uniqueness
      allowNull: false,
      unique: true
    },
    p256dhKey: {
      type: DataTypes.STRING(255), // Sufficient for base64 keys (~88 chars)
      allowNull: false
    },
    authKey: {
      type: DataTypes.STRING(255), // Sufficient for base64 keys (~24 chars)
      allowNull: false
    },
    userRole: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'user'
    },
    deviceInfo: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: {}
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    lastUsed: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    failureCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'push_subscriptions',
    timestamps: true,
    underscored: false, // Override global underscored setting to match our table structure
    indexes: [
      {
        fields: ['userId']
      },
      {
        fields: ['endpoint']
      },
      {
        fields: ['isActive']
      },
      {
        fields: ['userRole']
      }
    ]
  });

  // Associations
  PushSubscription.associate = (models) => {
    PushSubscription.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
  };

  return PushSubscription;
};

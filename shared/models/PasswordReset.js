const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Password Reset Model
 * Stores password reset tokens for secure password recovery
 */

const PasswordReset = sequelize.define('PasswordReset', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id',
    },
  },
  token: {
    type: DataTypes.STRING(64),
    allowNull: false,
    unique: true,
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  used_at: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  is_used: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
  },
  attempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
  },
  ip_address: {
    type: DataTypes.STRING(45),
    allowNull: true,
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'password_resets',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['token'],
      unique: true,
    },
    {
      fields: ['user_id'],
    },
    {
      fields: ['email'],
    },
    {
      fields: ['expires_at'],
    },
    {
      fields: ['is_used'],
    },
  ],
});

// Instance methods
PasswordReset.prototype.isExpired = function() {
  return new Date() > this.expires_at;
};

PasswordReset.prototype.isValid = function() {
  return !this.is_used && !this.isExpired();
};

PasswordReset.prototype.markAsUsed = async function(ipAddress = null, userAgent = null) {
  this.is_used = true;
  this.used_at = new Date();
  if (ipAddress) this.ip_address = ipAddress;
  if (userAgent) this.user_agent = userAgent;
  await this.save();
};

PasswordReset.prototype.incrementAttempts = async function() {
  this.attempts += 1;
  await this.save();
};

// Class methods
PasswordReset.createResetToken = async function(userId, email, expiresInHours = 1) {
  const crypto = require('crypto');
  const token = crypto.randomBytes(32).toString('hex');
  const expiresAt = new Date();
  expiresAt.setHours(expiresAt.getHours() + expiresInHours);

  // Invalidate any existing tokens for this user
  await this.update(
    { is_used: true },
    { where: { user_id: userId, is_used: false } }
  );

  return await this.create({
    user_id: userId,
    token,
    email,
    expires_at: expiresAt,
  });
};

PasswordReset.findValidToken = async function(token) {
  const reset = await this.findOne({
    where: { token },
    include: [
      {
        association: 'user',
        attributes: ['id', 'name', 'email', 'is_active'],
      },
    ],
  });

  if (!reset || !reset.isValid()) {
    return null;
  }

  return reset;
};

PasswordReset.cleanupExpiredTokens = async function() {
  const result = await this.destroy({
    where: {
      expires_at: {
        [require('sequelize').Op.lt]: new Date(),
      },
    },
  });

  return result;
};

PasswordReset.getUserPendingReset = async function(userId) {
  return await this.findOne({
    where: {
      user_id: userId,
      is_used: false,
      expires_at: {
        [require('sequelize').Op.gt]: new Date(),
      },
    },
    order: [['created_at', 'DESC']],
  });
};

PasswordReset.getResetStats = async function() {
  const { Op } = require('sequelize');
  const now = new Date();
  const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);

  const [total, pending, used, expired, recent] = await Promise.all([
    this.count(),
    this.count({ where: { is_used: false, expires_at: { [Op.gt]: now } } }),
    this.count({ where: { is_used: true } }),
    this.count({ where: { is_used: false, expires_at: { [Op.lt]: now } } }),
    this.count({ where: { created_at: { [Op.gte]: last24h } } }),
  ]);

  return {
    total,
    pending,
    used,
    expired,
    recent,
  };
};

// Rate limiting for password reset requests
PasswordReset.checkRateLimit = async function(email, maxRequests = 3, windowHours = 1) {
  const { Op } = require('sequelize');
  const windowStart = new Date();
  windowStart.setHours(windowStart.getHours() - windowHours);

  const recentRequests = await this.count({
    where: {
      email,
      created_at: {
        [Op.gte]: windowStart,
      },
    },
  });

  return {
    allowed: recentRequests < maxRequests,
    remaining: Math.max(0, maxRequests - recentRequests),
    resetTime: new Date(windowStart.getTime() + windowHours * 60 * 60 * 1000),
  };
};

// Associations
PasswordReset.associate = function(models) {
  PasswordReset.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  });
};

module.exports = PasswordReset;

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

module.exports = (() => {
  const EmailQueue = sequelize.define('EmailQueue', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    to_email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        isEmail: true,
      },
    },
    to_name: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    from_email: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        isEmailOrNull: function(value) {
          if (value !== null && value !== undefined && value !== '') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
              throw new Error('from_email must be a valid email address');
            }
          }
        },
      },
    },
    from_name: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    subject: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    html_content: {
      type: DataTypes.TEXT('long'),
      allowNull: false,
    },
    text_content: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    email_type: {
      type: DataTypes.ENUM('verification', 'password_reset', 'welcome', 'notification', 'marketing', 'system_alert', 'daily_digest', 'weekly_report'),
      allowNull: false,
    },
    priority: {
      type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
      allowNull: false,
      defaultValue: 'normal',
    },
    status: {
      type: DataTypes.ENUM('pending', 'processing', 'sent', 'failed', 'cancelled'),
      allowNull: false,
      defaultValue: 'pending',
    },
    scheduled_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    sent_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    failed_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    attempts: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    max_attempts: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 3,
    },
    error_message: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    error_stack: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    template_data: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    ip_address: {
      type: DataTypes.STRING(45),
      allowNull: true,
    },
    user_agent: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  }, {
    tableName: 'email_queue',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['status'],
      },
      {
        fields: ['email_type'],
      },
      {
        fields: ['priority'],
      },
      {
        fields: ['scheduled_at'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['user_id'],
      },
      {
        fields: ['to_email'],
      },
      {
        fields: ['status', 'priority', 'scheduled_at'],
        name: 'email_queue_processing_index',
      },
    ],
  });

  // Instance methods
  EmailQueue.prototype.markAsProcessing = async function() {
    this.status = 'processing';
    this.attempts += 1;
    await this.save();
  };

  EmailQueue.prototype.markAsSent = async function() {
    this.status = 'sent';
    this.sent_at = new Date();
    await this.save();
  };

  EmailQueue.prototype.markAsFailed = async function(error) {
    this.status = 'failed';
    this.failed_at = new Date();
    this.error_message = error.message;
    this.error_stack = error.stack;
    await this.save();
  };

  EmailQueue.prototype.canRetry = function() {
    return this.attempts < this.max_attempts;
  };

  EmailQueue.prototype.scheduleRetry = async function(delayMinutes = 5) {
    if (this.canRetry()) {
      this.status = 'pending';
      this.scheduled_at = new Date(Date.now() + delayMinutes * 60 * 1000);
      await this.save();
      return true;
    }
    return false;
  };

  // Static methods
  EmailQueue.getNextBatch = async function(batchSize = 10) {
    const { Op } = require('sequelize');
    const now = new Date();
    
    return await EmailQueue.findAll({
      where: {
        status: 'pending',
        [Op.or]: [
          { scheduled_at: null },
          { scheduled_at: { [Op.lte]: now } },
        ],
      },
      order: [
        ['priority', 'DESC'], // urgent, high, normal, low
        ['created_at', 'ASC'], // FIFO within same priority
      ],
      limit: batchSize,
    });
  };

  EmailQueue.getQueueStats = async function() {
    const { Op } = require('sequelize');
    const now = new Date();
    const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const [
      totalPending,
      totalProcessing,
      totalSent,
      totalFailed,
      recentSent,
      recentFailed,
      avgProcessingTime,
    ] = await Promise.all([
      EmailQueue.count({ where: { status: 'pending' } }),
      EmailQueue.count({ where: { status: 'processing' } }),
      EmailQueue.count({ where: { status: 'sent' } }),
      EmailQueue.count({ where: { status: 'failed' } }),
      EmailQueue.count({ 
        where: { 
          status: 'sent',
          sent_at: { [Op.gte]: last24h },
        },
      }),
      EmailQueue.count({ 
        where: { 
          status: 'failed',
          failed_at: { [Op.gte]: last24h },
        },
      }),
      EmailQueue.findAll({
        attributes: [
          [sequelize.fn('AVG', 
            sequelize.literal('TIMESTAMPDIFF(SECOND, created_at, sent_at)')
          ), 'avg_seconds'],
        ],
        where: {
          status: 'sent',
          sent_at: { [Op.gte]: last24h },
        },
        raw: true,
      }),
    ]);

    return {
      queue: {
        pending: totalPending,
        processing: totalProcessing,
        sent: totalSent,
        failed: totalFailed,
        total: totalPending + totalProcessing + totalSent + totalFailed,
      },
      last24h: {
        sent: recentSent,
        failed: recentFailed,
        successRate: recentSent + recentFailed > 0 
          ? ((recentSent / (recentSent + recentFailed)) * 100).toFixed(2) + '%'
          : 'N/A',
      },
      performance: {
        avgProcessingTime: avgProcessingTime[0]?.avg_seconds 
          ? Math.round(avgProcessingTime[0].avg_seconds) + 's'
          : 'N/A',
      },
    };
  };

  EmailQueue.cleanupOldEmails = async function(daysOld = 30) {
    const { Op } = require('sequelize');
    const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);
    
    const deleted = await EmailQueue.destroy({
      where: {
        status: { [Op.in]: ['sent', 'failed'] },
        created_at: { [Op.lt]: cutoffDate },
      },
    });
    
    return deleted;
  };

  // Associations
  EmailQueue.associate = function(models) {
    EmailQueue.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user',
    });
  };

  return EmailQueue;
})();

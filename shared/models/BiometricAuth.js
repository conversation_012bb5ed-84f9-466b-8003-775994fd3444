const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * BiometricAuth Model
 * Stores biometric authentication data for users
 */

const BiometricAuth = sequelize.define('BiometricAuth', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id',
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  },
  credential_id: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    comment: 'WebAuthn credential ID',
  },
  public_key: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: 'Public key for credential verification',
  },
  counter: {
    type: DataTypes.BIGINT,
    allowNull: false,
    defaultValue: 0,
    comment: 'Signature counter for replay attack prevention',
  },
  device_type: {
    type: DataTypes.ENUM('platform', 'cross-platform'),
    allowNull: false,
    comment: 'Type of authenticator device',
  },
  transport: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Transport methods supported by the authenticator',
  },
  device_name: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'User-friendly name for the device',
  },
  device_fingerprint: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Device fingerprint for additional security',
  },
  last_used: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Last time this credential was used',
  },
  usage_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Number of times this credential has been used',
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether this credential is active',
  },
  backup_eligible: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether the credential is backup eligible',
  },
  backup_state: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether the credential is currently backed up',
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional metadata about the credential',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'biometric_auth',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['credential_id'],
    },
    {
      fields: ['user_id'],
    },
    {
      fields: ['is_active'],
    },
    {
      fields: ['device_type'],
    },
    {
      fields: ['last_used'],
    },
  ],
});

// Instance methods
BiometricAuth.prototype.updateUsage = async function() {
  return this.update({
    last_used: new Date(),
    usage_count: this.usage_count + 1,
  });
};

BiometricAuth.prototype.updateCounter = async function(newCounter) {
  if (newCounter <= this.counter) {
    throw new Error('Invalid counter value - possible replay attack');
  }
  
  return this.update({
    counter: newCounter,
  });
};

BiometricAuth.prototype.deactivate = async function() {
  return this.update({
    is_active: false,
  });
};

BiometricAuth.prototype.toJSON = function() {
  const values = { ...this.get() };
  // Don't expose sensitive data
  delete values.public_key;
  return values;
};

// Class methods
BiometricAuth.findActiveByUserId = async function(userId) {
  return await BiometricAuth.findAll({
    where: {
      user_id: userId,
      is_active: true,
    },
    order: [['last_used', 'DESC']],
  });
};

BiometricAuth.findByCredentialId = async function(credentialId) {
  return await BiometricAuth.findOne({
    where: {
      credential_id: credentialId,
      is_active: true,
    },
  });
};

BiometricAuth.createCredential = async function(userId, credentialData) {
  try {
    return await BiometricAuth.create({
      user_id: userId,
      credential_id: credentialData.credentialId,
      public_key: credentialData.publicKey,
      counter: credentialData.counter || 0,
      device_type: credentialData.deviceType || 'platform',
      transport: credentialData.transport || [],
      device_name: credentialData.deviceName || 'Biometric Device',
      device_fingerprint: credentialData.deviceFingerprint,
      backup_eligible: credentialData.backupEligible || false,
      backup_state: credentialData.backupState || false,
      metadata: credentialData.metadata || {},
    });
  } catch (error) {
    console.error('Biometric credential creation error:', error);
    throw error;
  }
};

BiometricAuth.getUserCredentialCount = async function(userId) {
  return await BiometricAuth.count({
    where: {
      user_id: userId,
      is_active: true,
    },
  });
};

BiometricAuth.deactivateAllUserCredentials = async function(userId) {
  return await BiometricAuth.update(
    { is_active: false },
    {
      where: {
        user_id: userId,
        is_active: true,
      },
    }
  );
};

// Associations
BiometricAuth.associate = function(models) {
  BiometricAuth.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  });
};

module.exports = BiometricAuth;

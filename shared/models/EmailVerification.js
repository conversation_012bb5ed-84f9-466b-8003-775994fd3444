const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Email Verification Model
 * Stores email verification tokens for user registration
 */

const EmailVerification = sequelize.define('EmailVerification', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id',
    },
  },
  token: {
    type: DataTypes.STRING(64),
    allowNull: false,
    unique: true,
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  verified_at: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  is_used: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
  },
  attempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
  },
  ip_address: {
    type: DataTypes.STRING(45),
    allowNull: true,
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'email_verifications',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['token'],
      unique: true,
    },
    {
      fields: ['user_id'],
    },
    {
      fields: ['email'],
    },
    {
      fields: ['expires_at'],
    },
    {
      fields: ['is_used'],
    },
  ],
});

// Instance methods
EmailVerification.prototype.isExpired = function() {
  return new Date() > this.expires_at;
};

EmailVerification.prototype.isValid = function() {
  return !this.is_used && !this.isExpired();
};

EmailVerification.prototype.markAsUsed = async function(ipAddress = null, userAgent = null) {
  this.is_used = true;
  this.verified_at = new Date();
  if (ipAddress) this.ip_address = ipAddress;
  if (userAgent) this.user_agent = userAgent;
  await this.save();
};

EmailVerification.prototype.incrementAttempts = async function() {
  this.attempts += 1;
  await this.save();
};

// Class methods
EmailVerification.createVerificationToken = async function(userId, email, expiresInHours = 24) {
  const crypto = require('crypto');
  const token = crypto.randomBytes(32).toString('hex');
  const expiresAt = new Date();
  expiresAt.setHours(expiresAt.getHours() + expiresInHours);

  // Invalidate any existing tokens for this user
  await this.update(
    { is_used: true },
    { where: { user_id: userId, is_used: false } }
  );

  return await this.create({
    user_id: userId,
    token,
    email,
    expires_at: expiresAt,
  });
};

EmailVerification.findValidToken = async function(token) {
  const verification = await this.findOne({
    where: { token },
    include: [
      {
        association: 'user',
        attributes: ['id', 'name', 'email', 'email_verified', 'is_active'],
      },
    ],
  });

  if (!verification || !verification.isValid()) {
    return null;
  }

  return verification;
};

EmailVerification.cleanupExpiredTokens = async function() {
  const result = await this.destroy({
    where: {
      expires_at: {
        [require('sequelize').Op.lt]: new Date(),
      },
    },
  });

  return result;
};

EmailVerification.getUserPendingVerification = async function(userId) {
  return await this.findOne({
    where: {
      user_id: userId,
      is_used: false,
      expires_at: {
        [require('sequelize').Op.gt]: new Date(),
      },
    },
    order: [['created_at', 'DESC']],
  });
};

EmailVerification.getVerificationStats = async function() {
  const { Op } = require('sequelize');
  const now = new Date();
  const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);

  const [total, pending, verified, expired, recent] = await Promise.all([
    this.count(),
    this.count({ where: { is_used: false, expires_at: { [Op.gt]: now } } }),
    this.count({ where: { is_used: true } }),
    this.count({ where: { is_used: false, expires_at: { [Op.lt]: now } } }),
    this.count({ where: { created_at: { [Op.gte]: last24h } } }),
  ]);

  return {
    total,
    pending,
    verified,
    expired,
    recent,
  };
};

// Associations
EmailVerification.associate = function(models) {
  EmailVerification.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  });
};

module.exports = EmailVerification;

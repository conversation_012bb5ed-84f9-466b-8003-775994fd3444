const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Lead Model
 * Represents potential customers in the HLenergy CRM system
 */

const Lead = sequelize.define('Lead', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  
  // Basic Information
  first_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'First name cannot be empty',
      },
      len: {
        args: [1, 100],
        msg: 'First name must be between 1 and 100 characters',
      },
    },
  },
  last_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Last name cannot be empty',
      },
      len: {
        args: [1, 100],
        msg: 'Last name must be between 1 and 100 characters',
      },
    },
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    validate: {
      isEmail: {
        msg: 'Must be a valid email address',
      },
      notEmpty: {
        msg: 'Email cannot be empty',
      },
    },
  },
  phone: {
    type: DataTypes.STRING(50),
    allowNull: true,
    validate: {
      len: {
        args: [10, 50],
        msg: 'Phone number must be between 10 and 50 characters',
      },
    },
  },
  company_name: {
    type: DataTypes.STRING(255),
    allowNull: true,
  },
  job_title: {
    type: DataTypes.STRING(100),
    allowNull: true,
  },
  
  // Lead Details
  lead_source: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'website',
    validate: {
      isIn: {
        args: [['website', 'referral', 'social_media', 'email_campaign', 'phone', 'event', 'advertisement', 'partner', 'other']],
        msg: 'Lead source must be a valid option',
      },
    },
  },
  lead_score: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 50,
    validate: {
      min: {
        args: [0],
        msg: 'Lead score cannot be negative',
      },
      max: {
        args: [100],
        msg: 'Lead score cannot exceed 100',
      },
    },
  },
  status: {
    type: DataTypes.ENUM('new', 'contacted', 'qualified', 'proposal', 'negotiation', 'won', 'lost', 'nurturing'),
    allowNull: false,
    defaultValue: 'new',
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    allowNull: false,
    defaultValue: 'medium',
  },
  
  // Contact Information
  address: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  city: {
    type: DataTypes.STRING(100),
    allowNull: true,
  },
  state: {
    type: DataTypes.STRING(100),
    allowNull: true,
  },
  postal_code: {
    type: DataTypes.STRING(20),
    allowNull: true,
  },
  country: {
    type: DataTypes.STRING(100),
    allowNull: false,
    defaultValue: 'Portugal',
  },
  website: {
    type: DataTypes.STRING(255),
    allowNull: true,
    validate: {
      isUrl: {
        msg: 'Website must be a valid URL',
      },
    },
  },
  
  // Business Information
  industry: {
    type: DataTypes.STRING(100),
    allowNull: true,
  },
  company_size: {
    type: DataTypes.ENUM('1-10', '11-50', '51-200', '201-1000', '1000+'),
    allowNull: true,
  },
  annual_revenue: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    validate: {
      min: {
        args: [0],
        msg: 'Annual revenue cannot be negative',
      },
    },
  },
  monthly_energy_budget: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: {
        args: [0],
        msg: 'Monthly energy budget cannot be negative',
      },
    },
  },
  current_energy_provider: {
    type: DataTypes.STRING(255),
    allowNull: true,
  },
  
  // Energy Needs (JSON field for flexible data)
  energy_needs: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
  },
  
  // Lead Management
  assigned_to: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id',
    },
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id',
    },
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  tags: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    allowNull: true,
    defaultValue: [],
  },
  
  // Dates
  first_contact_date: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  last_contact_date: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  next_follow_up_date: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  qualified_date: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  converted_date: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  
  // Financial
  estimated_value: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    validate: {
      min: {
        args: [0],
        msg: 'Estimated value cannot be negative',
      },
    },
  },
  actual_value: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    validate: {
      min: {
        args: [0],
        msg: 'Actual value cannot be negative',
      },
    },
  },
  
  // Source Tracking
  contact_submission_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'contact_submissions',
      key: 'id',
    },
  },
  utm_source: {
    type: DataTypes.STRING(100),
    allowNull: true,
  },
  utm_medium: {
    type: DataTypes.STRING(100),
    allowNull: true,
  },
  utm_campaign: {
    type: DataTypes.STRING(100),
    allowNull: true,
  },
  referrer_url: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  
  // Metadata
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
  },
  ip_address: {
    type: DataTypes.STRING(45), // IPv6 support
    allowNull: true,
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  
  // Timestamps
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'leads',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    { fields: ['email'] },
    { fields: ['status'] },
    { fields: ['priority'] },
    { fields: ['lead_source'] },
    { fields: ['lead_score'] },
    { fields: ['assigned_to'] },
    { fields: ['created_by'] },
    { fields: ['created_at'] },
    { fields: ['first_contact_date'] },
    { fields: ['next_follow_up_date'] },
    { fields: ['contact_submission_id'] },
    { fields: ['company_name'] },
    { fields: ['industry'] },
  ],
});

// Virtual fields
Lead.prototype.getFullName = function() {
  return `${this.first_name} ${this.last_name}`.trim();
};

Lead.prototype.getDisplayName = function() {
  if (this.company_name) {
    return `${this.getFullName()} (${this.company_name})`;
  }
  return this.getFullName();
};

// Instance methods
Lead.prototype.updateScore = async function(scoreChange, reason = null) {
  const newScore = Math.max(0, Math.min(100, this.lead_score + scoreChange));

  // Log score change in metadata
  const scoreHistory = this.metadata.score_history || [];
  scoreHistory.push({
    date: new Date().toISOString(),
    old_score: this.lead_score,
    new_score: newScore,
    change: scoreChange,
    reason: reason
  });

  return this.update({
    lead_score: newScore,
    metadata: {
      ...this.metadata,
      score_history: scoreHistory
    }
  });
};

Lead.prototype.markAsContacted = async function(userId = null) {
  const updates = {
    status: 'contacted',
    last_contact_date: new Date()
  };

  if (!this.first_contact_date) {
    updates.first_contact_date = new Date();
  }

  if (userId) {
    updates.assigned_to = userId;
  }

  return this.update(updates);
};

Lead.prototype.markAsQualified = async function(userId = null) {
  const updates = {
    status: 'qualified',
    qualified_date: new Date(),
    last_contact_date: new Date()
  };

  if (userId) {
    updates.assigned_to = userId;
  }

  return this.update(updates);
};

Lead.prototype.markAsWon = async function(actualValue = null, userId = null) {
  const updates = {
    status: 'won',
    converted_date: new Date(),
    last_contact_date: new Date()
  };

  if (actualValue) {
    updates.actual_value = actualValue;
  }

  if (userId) {
    updates.assigned_to = userId;
  }

  return this.update(updates);
};

Lead.prototype.markAsLost = async function(reason = null, userId = null) {
  const updates = {
    status: 'lost',
    last_contact_date: new Date()
  };

  if (reason) {
    updates.metadata = {
      ...this.metadata,
      lost_reason: reason,
      lost_date: new Date().toISOString()
    };
  }

  if (userId) {
    updates.assigned_to = userId;
  }

  return this.update(updates);
};

Lead.prototype.scheduleFollowUp = async function(followUpDate, note = null) {
  const updates = {
    next_follow_up_date: followUpDate
  };

  if (note) {
    const followUpHistory = this.metadata.follow_up_history || [];
    followUpHistory.push({
      date: new Date().toISOString(),
      scheduled_for: followUpDate,
      note: note
    });

    updates.metadata = {
      ...this.metadata,
      follow_up_history: followUpHistory
    };
  }

  return this.update(updates);
};

Lead.prototype.addTag = async function(tag) {
  if (!this.tags.includes(tag)) {
    return this.update({
      tags: [...this.tags, tag]
    });
  }
  return this;
};

Lead.prototype.removeTag = async function(tag) {
  return this.update({
    tags: this.tags.filter(t => t !== tag)
  });
};

Lead.prototype.addNote = async function(note, userId = null) {
  const currentNotes = this.notes || '';
  const timestamp = new Date().toISOString();
  const userInfo = userId ? ` (User ID: ${userId})` : '';
  const newNote = `[${timestamp}]${userInfo}: ${note}`;

  return this.update({
    notes: currentNotes ? `${currentNotes}\n\n${newNote}` : newNote,
  });
};

// Class methods
Lead.getStatusCounts = async function() {
  const counts = await this.findAll({
    attributes: [
      'status',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
    ],
    group: ['status'],
    raw: true,
  });

  return counts.reduce((acc, item) => {
    acc[item.status] = parseInt(item.count);
    return acc;
  }, {});
};

Lead.getLeadSourceCounts = async function() {
  const counts = await this.findAll({
    attributes: [
      'lead_source',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
    ],
    group: ['lead_source'],
    raw: true,
  });

  return counts.reduce((acc, item) => {
    acc[item.lead_source] = parseInt(item.count);
    return acc;
  }, {});
};

Lead.getHighValueLeads = async function(minScore = 70, limit = 10) {
  return this.findAll({
    where: {
      lead_score: {
        [sequelize.Op.gte]: minScore
      },
      status: {
        [sequelize.Op.notIn]: ['won', 'lost']
      }
    },
    order: [['lead_score', 'DESC'], ['created_at', 'DESC']],
    limit,
    include: [
      {
        association: 'leadAssignedUser',
        attributes: ['id', 'name', 'email'],
      },
    ],
  });
};

Lead.getLeadsNeedingFollowUp = async function(limit = 20) {
  return this.findAll({
    where: {
      next_follow_up_date: {
        [sequelize.Op.lte]: new Date()
      },
      status: {
        [sequelize.Op.notIn]: ['won', 'lost']
      }
    },
    order: [['next_follow_up_date', 'ASC']],
    limit,
    include: [
      {
        association: 'leadAssignedUser',
        attributes: ['id', 'name', 'email'],
      },
    ],
  });
};

// Associations are defined in models/index.js to avoid conflicts

module.exports = Lead;

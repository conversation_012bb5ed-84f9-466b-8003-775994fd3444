const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * LeadActivity Model
 * Represents activities and interactions with leads
 */

const LeadActivity = sequelize.define('LeadActivity', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  lead_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'leads',
      key: 'id',
    },
  },
  activity_type: {
    type: DataTypes.ENUM('call', 'email', 'meeting', 'note', 'task', 'proposal', 'quote', 'demo', 'follow_up'),
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Activity type cannot be empty',
      },
    },
  },
  subject: {
    type: DataTypes.STRING(255),
    allowNull: true,
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  outcome: {
    type: DataTypes.STRING(100),
    allowNull: true,
  },
  scheduled_at: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  completed_at: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  duration_minutes: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: {
        args: [0],
        msg: 'Duration cannot be negative',
      },
    },
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id',
    },
  },
  assigned_to: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id',
    },
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'lead_activities',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    { fields: ['lead_id'] },
    { fields: ['activity_type'] },
    { fields: ['created_by'] },
    { fields: ['assigned_to'] },
    { fields: ['created_at'] },
    { fields: ['scheduled_at'] },
  ],
});

// Instance methods
LeadActivity.prototype.markAsCompleted = async function(outcome = null, durationMinutes = null) {
  const updates = {
    completed_at: new Date()
  };
  
  if (outcome) {
    updates.outcome = outcome;
  }
  
  if (durationMinutes) {
    updates.duration_minutes = durationMinutes;
  }
  
  return this.update(updates);
};

LeadActivity.prototype.reschedule = async function(newDateTime) {
  return this.update({
    scheduled_at: newDateTime,
    completed_at: null // Reset completion if rescheduling
  });
};

// Class methods
LeadActivity.getUpcomingActivities = async function(userId = null, limit = 20) {
  const where = {
    scheduled_at: {
      [sequelize.Op.gte]: new Date()
    },
    completed_at: null
  };
  
  if (userId) {
    where.assigned_to = userId;
  }
  
  return this.findAll({
    where,
    order: [['scheduled_at', 'ASC']],
    limit,
    include: [
      {
        association: 'lead',
        attributes: ['id', 'first_name', 'last_name', 'email', 'company_name'],
      },
      {
        association: 'activityAssignedUser',
        attributes: ['id', 'name', 'email'],
      },
    ],
  });
};

LeadActivity.getOverdueActivities = async function(userId = null, limit = 20) {
  const where = {
    scheduled_at: {
      [sequelize.Op.lt]: new Date()
    },
    completed_at: null
  };
  
  if (userId) {
    where.assigned_to = userId;
  }
  
  return this.findAll({
    where,
    order: [['scheduled_at', 'ASC']],
    limit,
    include: [
      {
        association: 'lead',
        attributes: ['id', 'first_name', 'last_name', 'email', 'company_name'],
      },
      {
        association: 'activityAssignedUser',
        attributes: ['id', 'name', 'email'],
      },
    ],
  });
};

LeadActivity.getActivityStats = async function(startDate = null, endDate = null) {
  const where = {};
  
  if (startDate && endDate) {
    where.created_at = {
      [sequelize.Op.between]: [startDate, endDate]
    };
  }
  
  const stats = await this.findAll({
    attributes: [
      'activity_type',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      [sequelize.fn('AVG', sequelize.col('duration_minutes')), 'avg_duration'],
    ],
    where,
    group: ['activity_type'],
    raw: true,
  });
  
  return stats.reduce((acc, item) => {
    acc[item.activity_type] = {
      count: parseInt(item.count),
      avg_duration: item.avg_duration ? parseFloat(item.avg_duration) : null
    };
    return acc;
  }, {});
};

// Associations are defined in models/index.js to avoid conflicts

module.exports = LeadActivity;

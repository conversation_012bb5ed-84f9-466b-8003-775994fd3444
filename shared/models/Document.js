const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Document = sequelize.define('Document', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  
  // File Information
  filename: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  
  originalName: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: 'Original filename as uploaded'
  },
  
  mimeType: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  
  fileSize: {
    type: DataTypes.BIGINT,
    allowNull: false,
    comment: 'File size in bytes'
  },
  
  filePath: {
    type: DataTypes.STRING(500),
    allowNull: false,
    comment: 'Path to the file on storage system'
  },
  
  fileHash: {
    type: DataTypes.STRING(64),
    allowNull: true,
    comment: 'SHA-256 hash of the file for integrity checking'
  },
  
  // Relationships
  customerId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'customers',
      key: 'id'
    }
  },
  
  projectId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'projects',
      key: 'id'
    }
  },
  
  communicationId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'communications',
      key: 'id'
    }
  },
  
  uploadedBy: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  
  // Document Classification
  type: {
    type: DataTypes.ENUM(
      'contract',
      'proposal',
      'report',
      'invoice',
      'receipt',
      'energy_audit',
      'technical_spec',
      'compliance_doc',
      'correspondence',
      'presentation',
      'image',
      'other'
    ),
    allowNull: false,
    defaultValue: 'other'
  },
  
  category: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Custom category for organization'
  },
  
  title: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Human-readable title for the document'
  },
  
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  // Version Control
  version: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: '1.0'
  },
  
  parentDocumentId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'documents',
      key: 'id'
    },
    comment: 'Reference to parent document for versioning'
  },
  
  isLatestVersion: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  
  // Access Control
  visibility: {
    type: DataTypes.ENUM('private', 'team', 'customer', 'public'),
    allowNull: false,
    defaultValue: 'team'
  },
  
  accessLevel: {
    type: DataTypes.ENUM('view', 'download', 'edit', 'admin'),
    allowNull: false,
    defaultValue: 'view'
  },
  
  sharedWith: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of user IDs who have access to this document'
  },
  
  // Document Status
  status: {
    type: DataTypes.ENUM('draft', 'review', 'approved', 'archived', 'deleted'),
    allowNull: false,
    defaultValue: 'draft'
  },
  
  reviewedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  
  reviewedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  approvedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  
  approvedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  // Metadata
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: 'Tags for document organization and search'
  },
  
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Additional metadata (EXIF data, document properties, etc.)'
  },
  
  // Content Analysis
  extractedText: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Extracted text content for search indexing'
  },
  
  keywords: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: 'Automatically extracted keywords'
  },
  
  // Storage Information
  storageProvider: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'local',
    comment: 'Storage provider (local, s3, google_drive, etc.)'
  },
  
  storageReference: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'Reference ID or path in external storage system'
  },
  
  // Security
  isEncrypted: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  
  encryptionKey: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Encryption key reference (not the actual key)'
  },
  
  // Tracking
  downloadCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  
  lastAccessedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  lastAccessedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  
  // Expiration
  expiresAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When the document expires (for temporary documents)'
  },
  
  // Metadata
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'documents',
  timestamps: true,
  indexes: [
    {
      fields: ['customerId']
    },
    {
      fields: ['projectId']
    },
    {
      fields: ['uploadedBy']
    },
    {
      fields: ['type']
    },
    {
      fields: ['status']
    },
    {
      fields: ['isLatestVersion']
    },
    {
      fields: ['visibility']
    },
    {
      fields: ['createdAt']
    }
  ]
});

// Virtual fields
Document.prototype.getFormattedFileSize = function() {
  const bytes = this.fileSize;
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

Document.prototype.getFileExtension = function() {
  return this.originalName.split('.').pop().toLowerCase();
};

Document.prototype.isExpired = function() {
  if (!this.expiresAt) return false;
  return new Date() > new Date(this.expiresAt);
};

module.exports = Document;

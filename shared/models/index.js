const { sequelize } = require('../config/database');

/**
 * Models Index
 * Centralizes all model imports and associations
 */

// Import models
const User = require('./User');
const ContactSubmission = require('./ContactSubmission');
const ContactAction = require('./ContactAction');
const RefreshToken = require('./RefreshToken');
const UserSession = require('./UserSession');
const Log = require('./Log');
const EmailVerification = require('./EmailVerification');
const PasswordReset = require('./PasswordReset');
const EmailQueue = require('./EmailQueue');
const AdminPin = require('./AdminPin');
const BiometricAuth = require('./BiometricAuth');

// CRM Models
const Customer = require('./Customer');
const Project = require('./Project');
const Communication = require('./Communication');
const Document = require('./Document');

// Lead Models
const Lead = require('./Lead');
const LeadActivity = require('./LeadActivity');

// Worker Models
const WorkerStatus = require('./WorkerStatus');

// Worker System Models
const BackgroundJob = require('./BackgroundJob');
const SystemStats = require('./SystemStats');
const HealthCheck = require('./HealthCheck');

// Push Notification Models
const PushSubscription = require('./PushSubscription')(sequelize);
const FCMSubscription = require('./FCMSubscription')(sequelize);

// Define models object
const models = {
  User,
  ContactSubmission,
  ContactAction,
  RefreshToken,
  UserSession,
  Log,
  EmailVerification,
  PasswordReset,
  EmailQueue,
  AdminPin,
  BiometricAuth,

  // CRM Models
  Customer,
  Project,
  Communication,
  Document,

  // Lead Models
  Lead,
  LeadActivity,

  // Worker Models
  WorkerStatus,

  // Worker System Models
  BackgroundJob,
  SystemStats,
  HealthCheck,

  // Push Notification Models
  PushSubscription,
  FCMSubscription,
};

// Set up associations
Object.keys(models).forEach(modelName => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

// Set up CRM associations
const setupCRMAssociations = () => {
  // User associations
  User.hasMany(Customer, { foreignKey: 'assignedTo', as: 'assignedCustomers' });
  User.hasMany(Project, { foreignKey: 'assignedTo', as: 'assignedProjects' });
  User.hasMany(Communication, { foreignKey: 'userId', as: 'communications' });
  User.hasMany(Document, { foreignKey: 'uploadedBy', as: 'uploadedDocuments' });

  // Customer associations
  Customer.belongsTo(User, { foreignKey: 'assignedTo', as: 'assignedUser' });
  Customer.hasMany(Project, { foreignKey: 'customerId', as: 'projects' });
  Customer.hasMany(Communication, { foreignKey: 'customerId', as: 'communications' });
  Customer.hasMany(Document, { foreignKey: 'customerId', as: 'documents' });

  // Project associations
  Project.belongsTo(Customer, { foreignKey: 'customerId', as: 'customer' });
  Project.belongsTo(User, { foreignKey: 'assignedTo', as: 'assignedUser' });
  Project.hasMany(Communication, { foreignKey: 'projectId', as: 'communications' });
  Project.hasMany(Document, { foreignKey: 'projectId', as: 'documents' });

  // Communication associations
  Communication.belongsTo(Customer, { foreignKey: 'customerId', as: 'customer' });
  Communication.belongsTo(Project, { foreignKey: 'projectId', as: 'project' });
  Communication.belongsTo(User, { foreignKey: 'userId', as: 'user' });
  Communication.belongsTo(User, { foreignKey: 'readBy', as: 'readByUser' });
  Communication.hasMany(Document, { foreignKey: 'communicationId', as: 'documents' });

  // Document associations
  Document.belongsTo(Customer, { foreignKey: 'customerId', as: 'customer' });
  Document.belongsTo(Project, { foreignKey: 'projectId', as: 'project' });
  Document.belongsTo(Communication, { foreignKey: 'communicationId', as: 'communication' });
  Document.belongsTo(User, { foreignKey: 'uploadedBy', as: 'uploader' });
  Document.belongsTo(User, { foreignKey: 'reviewedBy', as: 'reviewer' });
  Document.belongsTo(User, { foreignKey: 'approvedBy', as: 'approver' });
  Document.belongsTo(User, { foreignKey: 'lastAccessedBy', as: 'lastAccessedByUser' });

  // Self-referencing for document versions
  Document.belongsTo(Document, { foreignKey: 'parentDocumentId', as: 'parentDocument' });
  Document.hasMany(Document, { foreignKey: 'parentDocumentId', as: 'versions' });

  // Lead associations
  Lead.belongsTo(User, { foreignKey: 'assigned_to', as: 'leadAssignedUser' });
  Lead.belongsTo(User, { foreignKey: 'created_by', as: 'leadCreatedByUser' });
  Lead.belongsTo(ContactSubmission, { foreignKey: 'contact_submission_id', as: 'contactSubmission' });
  Lead.hasMany(LeadActivity, { foreignKey: 'lead_id', as: 'activities' });

  // LeadActivity associations
  LeadActivity.belongsTo(Lead, { foreignKey: 'lead_id', as: 'lead' });
  LeadActivity.belongsTo(User, { foreignKey: 'created_by', as: 'activityCreatedByUser' });
  LeadActivity.belongsTo(User, { foreignKey: 'assigned_to', as: 'activityAssignedUser' });

  // User lead associations
  User.hasMany(Lead, { foreignKey: 'assigned_to', as: 'assignedLeads' });
  User.hasMany(Lead, { foreignKey: 'created_by', as: 'createdLeads' });
  User.hasMany(LeadActivity, { foreignKey: 'created_by', as: 'createdActivities' });
  User.hasMany(LeadActivity, { foreignKey: 'assigned_to', as: 'assignedActivities' });

  // ContactSubmission lead association
  ContactSubmission.hasOne(Lead, { foreignKey: 'contact_submission_id', as: 'lead' });
};

// Initialize CRM associations
setupCRMAssociations();

// Add sequelize instance to models
models.sequelize = sequelize;
models.Sequelize = require('sequelize');

// Check if database tables exist
const checkTablesExist = async () => {
  try {
    const [results] = await sequelize.query(`
      SELECT COUNT(*) as table_count
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name IN ('users', 'contact_submissions', 'refresh_tokens', 'user_sessions', 'logs', 'email_queue', 'admin_pins', 'biometric_auth')
    `);

    return results[0].table_count >= 8; // All core tables exist including new auth tables
  } catch (error) {
    console.log('Could not check tables, assuming they need to be created');
    return false;
  }
};

// Database initialization function
const initializeDatabase = async () => {
  try {
    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully');

    // Check if force sync is requested
    const forceSync = process.env.FORCE_DB_SYNC === 'true';

    if (forceSync) {
      console.log('🔄 Force sync requested, recreating database tables...');
      await sequelize.sync({ force: true });
      console.log('✅ Database tables recreated successfully');
    } else {
      // Check if tables already exist
      const tablesExist = await checkTablesExist();

      if (!tablesExist) {
        console.log('📋 Creating database tables...');
        // Only sync if tables don't exist (first time setup)
        await sequelize.sync({ force: false });
        console.log('✅ Database tables created successfully');
      } else {
        console.log('✅ Database tables already exist, skipping sync');

        // Only verify connection, don't alter tables
        const [results] = await sequelize.query('SELECT 1 as health_check');
        console.log('✅ Database connection verified');
      }
    }

    return true;
  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);

    // If the error is about too many keys, it's likely the tables exist but sync failed
    if (error.message.includes('Too many keys specified')) {
      console.log('⚠️  Tables exist but sync failed due to key limits. Continuing...');
      return true; // Continue anyway since tables likely exist
    }

    return false;
  }
};

// Database cleanup function
const cleanupDatabase = async () => {
  try {
    // Clean up expired refresh tokens
    await RefreshToken.cleanupExpiredTokens();

    // Clean up expired user sessions
    await UserSession.cleanupExpiredSessions();

    // Lock inactive sessions
    await UserSession.lockInactiveSessions(5); // 5 minutes idle timeout

    // Add other cleanup tasks here
    console.log('✅ Database cleanup completed');
  } catch (error) {
    console.error('❌ Database cleanup failed:', error.message);
  }
};

// Get database statistics
const getDatabaseStats = async () => {
  try {
    const userCount = await User.count();
    const contactCount = await ContactSubmission.count();
    const tokenStats = await RefreshToken.getTokenStats();
    const sessionStats = await UserSession.getSessionStats();
    const contactStatusCounts = await ContactSubmission.getStatusCounts();

    return {
      users: {
        total: userCount,
        active: await User.count({ where: { is_active: true } }),
        verified: await User.count({ where: { email_verified: true } }),
      },
      contacts: {
        total: contactCount,
        status_breakdown: contactStatusCounts,
      },
      tokens: tokenStats,
      sessions: sessionStats,
      last_updated: new Date().toISOString(),
    };
  } catch (error) {
    return {
      error: error.message,
      last_updated: new Date().toISOString(),
    };
  }
};

// Seed initial data (for development)
const seedDatabase = async () => {
  try {
    // Check if admin user exists
    const adminExists = await User.findOne({
      where: { role: 'admin' },
    });
    
    if (!adminExists) {
      // Create default admin user
      const adminUser = await User.create({
        name: 'HLenergy Admin',
        email: process.env.ADMIN_EMAIL || '<EMAIL>',
        password: process.env.ADMIN_PASSWORD || 'admin123456',
        role: 'admin',
        email_verified: true,
        is_active: true,
      });
      
      console.log('✅ Default admin user created:', adminUser.email);
    }
    
    // Add other seed data here if needed
    console.log('✅ Database seeding completed');
  } catch (error) {
    console.error('❌ Database seeding failed:', error.message);
  }
};

// Store interval ID for cleanup
let sessionMonitoringInterval = null;

// Schedule session monitoring to run every 5 minutes
const scheduleSessionMonitoring = () => {
  // Clear existing interval if it exists (for HMR restarts)
  if (sessionMonitoringInterval) {
    clearInterval(sessionMonitoringInterval);
    console.log('🧹 Cleared existing session monitoring interval');
  }

  sessionMonitoringInterval = setInterval(async () => {
    try {
      // Lock inactive sessions
      await UserSession.lockInactiveSessions();

      // Clean up expired sessions every hour (12 intervals)
      const now = new Date();
      if (now.getMinutes() % 60 === 0) {
        await UserSession.cleanupExpiredSessions();
      }
    } catch (error) {
      console.error('❌ Session monitoring failed:', error.message);
    }
  }, 5 * 60 * 1000); // 5 minutes

  console.log('📅 Session monitoring scheduled to run every 5 minutes (with HMR cleanup)');
};

// Function to stop session monitoring (for graceful shutdown)
const stopSessionMonitoring = () => {
  if (sessionMonitoringInterval) {
    clearInterval(sessionMonitoringInterval);
    sessionMonitoringInterval = null;
    console.log('🛑 Session monitoring stopped');
  }
};

module.exports = {
  ...models,
  initializeDatabase,
  cleanupDatabase,
  getDatabaseStats,
  seedDatabase,
  scheduleSessionMonitoring,
  stopSessionMonitoring,
};

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const FCMSubscription = sequelize.define('FCMSubscription', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      onDelete: 'CASCADE'
    },
    token: {
      type: DataTypes.TEXT,
      allowNull: false
      // Note: We use tokenHash for uniqueness instead of token directly
    },
    tokenHash: {
      type: DataTypes.STRING(64),
      allowNull: false,
      unique: true
    },
    userRole: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: 'user'
    },
    deviceInfo: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: {}
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false
    },
    lastUsed: {
      type: DataTypes.DATE,
      allowNull: true
    },
    failureCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      allowNull: false
    },
    topics: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: []
    }
  }, {
    tableName: 'fcm_subscriptions',
    timestamps: true,
    underscored: false, // Use camelCase to match our naming convention
    indexes: [
      {
        fields: ['userId']
      },
      {
        fields: ['tokenHash'],
        unique: true
      },
      {
        fields: ['userRole']
      },
      {
        fields: ['isActive']
      },
      {
        fields: ['lastUsed']
      },
      {
        fields: ['failureCount']
      }
    ]
  });

  // Associations
  FCMSubscription.associate = (models) => {
    FCMSubscription.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
  };

  // Instance methods
  FCMSubscription.prototype.markAsUsed = function() {
    return this.update({
      lastUsed: new Date(),
      failureCount: 0
    });
  };

  FCMSubscription.prototype.incrementFailure = function() {
    return this.update({
      failureCount: this.failureCount + 1
    });
  };

  FCMSubscription.prototype.deactivate = function() {
    return this.update({
      isActive: false
    });
  };

  // Class methods
  FCMSubscription.findByUserId = function(userId) {
    return this.findAll({
      where: {
        userId: userId,
        isActive: true
      }
    });
  };

  FCMSubscription.findByUserRole = function(role) {
    return this.findAll({
      where: {
        userRole: role,
        isActive: true
      }
    });
  };

  FCMSubscription.findActiveSubscriptions = function() {
    return this.findAll({
      where: {
        isActive: true
      }
    });
  };

  FCMSubscription.cleanupInactiveSubscriptions = function(daysOld = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    return this.destroy({
      where: {
        isActive: false,
        updatedAt: {
          [sequelize.Sequelize.Op.lt]: cutoffDate
        }
      }
    });
  };

  FCMSubscription.cleanupFailedSubscriptions = function(maxFailures = 10) {
    return this.update(
      { isActive: false },
      {
        where: {
          failureCount: {
            [sequelize.Sequelize.Op.gte]: maxFailures
          },
          isActive: true
        }
      }
    );
  };

  return FCMSubscription;
};

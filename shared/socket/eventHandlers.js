// Real-time event handlers for specific HLenergy features

class SocketEventHandlers {
  constructor(socketServer) {
    this.socketServer = socketServer
    this.io = socketServer.io
  }

  // Real-time analytics events
  setupAnalyticsHandlers() {
    return {
      // Page view tracking
      'analytics:pageview': (socket, data) => {
        const event = {
          type: 'pageview',
          page: data.page,
          title: data.title,
          userId: socket.userId,
          timestamp: new Date().toISOString(),
          sessionId: socket.id,
          userAgent: socket.handshake.headers['user-agent'],
          ip: socket.handshake.address
        }

        // Broadcast to admin dashboard
        this.io.to('admin').emit('analytics:realtime_pageview', event)
        
        // Update real-time visitor count
        this.updateVisitorCount()
      },

      // Button click tracking
      'analytics:click': (socket, data) => {
        const event = {
          type: 'click',
          element: data.element,
          page: data.page,
          userId: socket.userId,
          timestamp: new Date().toISOString()
        }

        this.io.to('admin').emit('analytics:realtime_click', event)
      },

      // Form submission tracking
      'analytics:form_submit': (socket, data) => {
        const event = {
          type: 'form_submit',
          form: data.form,
          success: data.success,
          userId: socket.userId,
          timestamp: new Date().toISOString()
        }

        this.io.to('admin').emit('analytics:realtime_form', event)
      },

      // Energy assessment requests
      'analytics:assessment_request': (socket, data) => {
        const event = {
          type: 'assessment_request',
          serviceType: data.serviceType, // B2B or B2C
          contactInfo: data.contactInfo,
          userId: socket.userId,
          timestamp: new Date().toISOString()
        }

        // High priority notification to admins
        this.io.to('admin').emit('notification:urgent', {
          type: 'new_assessment_request',
          title: 'New Energy Assessment Request',
          message: `New ${data.serviceType} assessment request from ${data.contactInfo.name}`,
          data: event,
          priority: 'high'
        })
      }
    }
  }

  // Customer communication events
  setupCommunicationHandlers() {
    return {
      // New customer inquiry
      'communication:inquiry': (socket, data) => {
        const inquiry = {
          id: Date.now(),
          type: 'inquiry',
          subject: data.subject,
          message: data.message,
          customerInfo: data.customerInfo,
          serviceType: data.serviceType,
          priority: data.priority || 'medium',
          status: 'new',
          createdAt: new Date().toISOString(),
          createdBy: socket.userId
        }

        // Notify all admins
        this.io.to('admin').emit('communication:new_inquiry', inquiry)

        // Send auto-response to customer
        this.sendAutoResponse(socket, inquiry)
      },

      // Customer chat message
      'communication:chat_message': (socket, data) => {
        const message = {
          id: Date.now(),
          conversationId: data.conversationId,
          message: data.message,
          senderId: socket.userId,
          senderType: socket.userRole,
          timestamp: new Date().toISOString(),
          attachments: data.attachments || []
        }

        // Send to conversation participants
        this.io.to(`conversation:${data.conversationId}`).emit('communication:message_received', message)

        // Notify admins if customer message
        if (socket.userRole === 'customer') {
          this.io.to('admin').emit('communication:customer_message', message)
        }
      },

      // WhatsApp integration events
      'communication:whatsapp_click': (socket, data) => {
        const event = {
          type: 'whatsapp_click',
          page: data.page,
          serviceType: data.serviceType,
          userId: socket.userId,
          timestamp: new Date().toISOString()
        }

        this.io.to('admin').emit('analytics:whatsapp_engagement', event)
      }
    }
  }

  // Project management events
  setupProjectHandlers() {
    return {
      // Project status update
      'project:status_change': (socket, data) => {
        const update = {
          projectId: data.projectId,
          oldStatus: data.oldStatus,
          newStatus: data.newStatus,
          comment: data.comment,
          updatedBy: socket.userId,
          timestamp: new Date().toISOString()
        }

        // Notify project stakeholders
        this.io.to(`project:${data.projectId}`).emit('project:status_updated', update)

        // Notify customer if admin update
        if (socket.userRole === 'admin' && data.customerId) {
          this.socketServer.sendToUser(data.customerId, 'notification:project_update', {
            type: 'project_status_change',
            title: 'Project Status Update',
            message: `Your project status has been updated to: ${data.newStatus}`,
            projectId: data.projectId
          })
        }
      },

      // New project milestone
      'project:milestone_reached': (socket, data) => {
        const milestone = {
          projectId: data.projectId,
          milestone: data.milestone,
          description: data.description,
          completedBy: socket.userId,
          timestamp: new Date().toISOString()
        }

        // Notify all project stakeholders
        this.io.to(`project:${data.projectId}`).emit('project:milestone_completed', milestone)

        // Send celebration notification to customer
        if (data.customerId) {
          this.socketServer.sendToUser(data.customerId, 'notification:celebration', {
            type: 'milestone_reached',
            title: '🎉 Milestone Reached!',
            message: `Great news! We've completed: ${data.milestone}`,
            projectId: data.projectId
          })
        }
      }
    }
  }

  // System notification events
  setupNotificationHandlers() {
    return {
      // Send notification to specific user
      'notification:send': (socket, data) => {
        if (socket.userRole !== 'admin') return

        const notification = {
          id: Date.now(),
          type: data.type,
          title: data.title,
          message: data.message,
          priority: data.priority || 'medium',
          createdAt: new Date().toISOString(),
          createdBy: socket.userId
        }

        if (data.userId) {
          // Send to specific user
          this.socketServer.sendToUser(data.userId, 'notification:received', notification)
        } else if (data.role) {
          // Send to all users with specific role
          this.socketServer.sendToRole(data.role, 'notification:received', notification)
        } else {
          // Broadcast to all users
          this.socketServer.broadcastToAll('notification:received', notification)
        }
      },

      // System maintenance notification
      'system:maintenance': (socket, data) => {
        if (socket.userRole !== 'admin') return

        const maintenance = {
          type: 'maintenance',
          title: 'Scheduled Maintenance',
          message: data.message,
          startTime: data.startTime,
          estimatedDuration: data.estimatedDuration,
          timestamp: new Date().toISOString()
        }

        this.socketServer.broadcastToAll('system:maintenance_notice', maintenance)
      }
    }
  }

  // Energy monitoring events (for future IoT integration)
  setupEnergyMonitoringHandlers() {
    return {
      // Real-time energy consumption data
      'energy:consumption_update': (socket, data) => {
        const update = {
          customerId: data.customerId,
          deviceId: data.deviceId,
          consumption: data.consumption,
          timestamp: new Date().toISOString(),
          unit: data.unit || 'kWh'
        }

        // Send to customer
        this.socketServer.sendToUser(data.customerId, 'energy:realtime_data', update)

        // Send to admins for monitoring
        this.io.to('admin').emit('energy:customer_consumption', update)

        // Check for alerts
        this.checkEnergyAlerts(update)
      },

      // Energy efficiency alerts
      'energy:efficiency_alert': (socket, data) => {
        const alert = {
          customerId: data.customerId,
          alertType: data.alertType, // 'high_consumption', 'efficiency_opportunity', etc.
          message: data.message,
          severity: data.severity,
          recommendations: data.recommendations,
          timestamp: new Date().toISOString()
        }

        // Notify customer
        this.socketServer.sendToUser(data.customerId, 'notification:energy_alert', alert)

        // Notify admins
        this.io.to('admin').emit('energy:customer_alert', alert)
      }
    }
  }

  // Utility methods
  updateVisitorCount() {
    const connectedUsers = this.socketServer.getConnectedUsers()
    const visitorCount = connectedUsers.length

    this.io.to('admin').emit('analytics:visitor_count', {
      count: visitorCount,
      users: connectedUsers,
      timestamp: new Date().toISOString()
    })
  }

  sendAutoResponse(socket, inquiry) {
    const autoResponse = {
      type: 'auto_response',
      message: `Thank you for your interest in HLenergy's ${inquiry.serviceType} services! We've received your inquiry about "${inquiry.subject}" and one of our energy consultants will contact you within 24 hours. In the meantime, feel free to explore our services or contact us via WhatsApp for immediate assistance.`,
      timestamp: new Date().toISOString()
    }

    socket.emit('communication:auto_response', autoResponse)
  }

  checkEnergyAlerts(consumptionData) {
    // Implement energy consumption alert logic
    // This could check for unusual spikes, efficiency opportunities, etc.
    
    // Example: High consumption alert
    if (consumptionData.consumption > 1000) { // Example threshold
      const alert = {
        customerId: consumptionData.customerId,
        alertType: 'high_consumption',
        message: 'Unusually high energy consumption detected',
        severity: 'warning',
        recommendations: [
          'Check for equipment running unnecessarily',
          'Review HVAC settings',
          'Consider scheduling an energy audit'
        ],
        timestamp: new Date().toISOString()
      }

      this.socketServer.sendToUser(consumptionData.customerId, 'notification:energy_alert', alert)
      this.io.to('admin').emit('energy:customer_alert', alert)
    }
  }

  // Get all event handlers
  getAllHandlers() {
    return {
      ...this.setupAnalyticsHandlers(),
      ...this.setupCommunicationHandlers(),
      ...this.setupProjectHandlers(),
      ...this.setupNotificationHandlers(),
      ...this.setupEnergyMonitoringHandlers()
    }
  }
}

module.exports = SocketEventHandlers

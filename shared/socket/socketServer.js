const { Server } = require('socket.io')
const jwt = require('jsonwebtoken')
const {
  logSocket,
  logSocketConnection,
  logSocketAuth,
  logSocketRoom,
  logSocketAnalytics,
  logSocketCommunication,
  logSecurity,
  logger
} = require('../config/logger')

class SocketServer {
  constructor(httpServer) {
    this.io = new Server(httpServer, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:5173",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    })

    // Increase max listeners to prevent warning
    this.io.setMaxListeners(20)

    this.connectedUsers = new Map() // userId -> socket.id
    this.userSockets = new Map()   // socket.id -> user info
    this.rooms = new Map()         // room -> Set of socket.ids

    // Security and monitoring
    this.connectionAttempts = new Map() // IP -> attempts count
    this.rateLimits = new Map() // socketId -> event counts
    this.suspiciousActivity = new Map() // socketId -> activity log

    // Performance metrics
    this.metrics = {
      totalConnections: 0,
      activeConnections: 0,
      totalEvents: 0,
      eventsByType: new Map(),
      averageResponseTime: 0,
      peakConnections: 0,
      startTime: Date.now()
    }

    // Memory management
    this.intervals = new Set() // Track all intervals for cleanup
    this.timeouts = new Set()  // Track all timeouts for cleanup
    this.isShuttingDown = false

    this.setupMiddleware()
    this.setupEventHandlers()
    this.setupPerformanceMonitoring()
    this.setupGracefulShutdown()

    logger.info('Socket.io Server initialized', {
      type: 'socket_server',
      event: 'initialized',
      timestamp: new Date().toISOString()
    })
  }

  setupMiddleware() {
    // Rate limiting middleware
    this.io.use((socket, next) => {
      const ip = socket.handshake.address
      const now = Date.now()
      const timeWindow = 60000 // 1 minute
      const maxAttempts = 10 // Max 10 connection attempts per minute

      if (!this.connectionAttempts.has(ip)) {
        this.connectionAttempts.set(ip, [])
      }

      const attempts = this.connectionAttempts.get(ip)
      // Clean old attempts
      const recentAttempts = attempts.filter(time => now - time < timeWindow)

      if (recentAttempts.length >= maxAttempts) {
        logSecurity('Socket.io Rate Limit Exceeded', {
          ip,
          attempts: recentAttempts.length,
          timeWindow: '1 minute',
          userAgent: socket.handshake.headers['user-agent']
        })
        return next(new Error('Rate limit exceeded'))
      }

      recentAttempts.push(now)
      this.connectionAttempts.set(ip, recentAttempts)
      next()
    })

    // Authentication middleware
    this.io.use(async (socket, next) => {
      const startTime = Date.now()

      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '')
        const ip = socket.handshake.address
        const userAgent = socket.handshake.headers['user-agent']

        if (!token) {
          logSocketAuth(socket.id, false, {
            reason: 'No token provided',
            ip,
            userAgent
          })
          return next(new Error('Authentication error: No token provided'))
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET)

        // Handle temporary tokens
        if (decoded.isTemporary) {
          socket.userId = decoded.userId
          socket.userRole = decoded.role
          socket.userData = {
            id: decoded.userId,
            email: decoded.email,
            role: decoded.role,
            name: decoded.name,
            isTemporary: true
          }

          logSocketAuth(socket.id, true, {
            userId: decoded.userId,
            email: decoded.email,
            tokenType: 'temporary',
            ip,
            userAgent,
            authTime: Date.now() - startTime
          })

          return next()
        }

        // Handle regular user tokens
        const user = await this.getUserById(decoded.userId)

        if (!user) {
          logSocketAuth(socket.id, false, {
            reason: 'User not found',
            userId: decoded.userId,
            ip,
            userAgent
          })
          return next(new Error('Authentication error: User not found'))
        }

        socket.userId = user.id
        socket.userRole = user.role
        socket.userData = {
          id: user.id,
          email: user.email,
          role: user.role,
          name: user.name
        }

        logSocketAuth(socket.id, true, {
          userId: user.id,
          email: user.email,
          role: user.role,
          tokenType: 'user',
          ip,
          userAgent,
          authTime: Date.now() - startTime
        })

        next()
      } catch (error) {
        logSocketAuth(socket.id, false, {
          reason: error.message,
          error: error.name,
          ip: socket.handshake.address,
          userAgent: socket.handshake.headers['user-agent']
        })
        next(new Error('Authentication error: Invalid token'))
      }
    })
  }

  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      // Update metrics
      this.metrics.totalConnections++
      this.metrics.activeConnections++
      this.metrics.peakConnections = Math.max(this.metrics.peakConnections, this.metrics.activeConnections)

      // Log connection
      logSocketConnection(socket.id, socket.userId, socket.userData, 'connect')

      // Store user connection
      this.connectedUsers.set(socket.userId, socket.id)
      this.userSockets.set(socket.id, socket.userData)

      // Initialize rate limiting for this socket
      this.rateLimits.set(socket.id, new Map())
      this.suspiciousActivity.set(socket.id, [])

      // Join user to their personal room
      socket.join(`user:${socket.userId}`)
      logSocketRoom('join', socket.id, socket.userId, `user:${socket.userId}`)

      // Join role-based rooms
      socket.join(`role:${socket.userRole}`)
      logSocketRoom('join', socket.id, socket.userId, `role:${socket.userRole}`)

      // Admin users join admin room
      if (socket.userRole === 'admin') {
        socket.join('admin')
        logSocketRoom('join', socket.id, socket.userId, 'admin')
      }

      // Send connection confirmation
      socket.emit('connected', {
        message: 'Connected to HLenergy real-time server',
        userId: socket.userId,
        timestamp: new Date().toISOString()
      })

      // Broadcast user online status to all users
      socket.broadcast.emit('user:online', {
        userId: socket.userId,
        userData: socket.userData,
        timestamp: new Date().toISOString()
      })

      // Send current online users list to the new user
      const onlineUsers = this.getConnectedUsers()
      console.log(`📊 Sending users list to new user: ${onlineUsers.length} users online`)
      socket.emit('users:list', onlineUsers)

      // Broadcast updated users list to all users
      console.log(`📢 Broadcasting users update: ${onlineUsers.length} users online`)
      this.io.emit('users:update', {
        count: onlineUsers.length,
        users: onlineUsers,
        timestamp: new Date().toISOString()
      })

      // Wrap all event handlers with logging and rate limiting
      const wrapEventHandler = (eventName, handler) => {
        return (data) => {
          const startTime = Date.now()

          // Rate limiting per event type
          if (!this.checkEventRateLimit(socket, eventName)) {
            logSecurity('Socket.io Event Rate Limit Exceeded', {
              socketId: socket.id,
              userId: socket.userId,
              event: eventName,
              ip: socket.handshake.address
            })
            return
          }

          // Log the event
          logSocket(eventName, socket.id, socket.userId, { data })

          // Update metrics
          this.updateEventMetrics(eventName, startTime)

          // Execute handler
          try {
            handler(data)
          } catch (error) {
            logger.error('Socket.io Event Handler Error', {
              type: 'socket_error',
              event: eventName,
              socketId: socket.id,
              userId: socket.userId,
              error: error.message,
              stack: error.stack
            })
          }
        }
      }

      // Handle real-time analytics tracking
      socket.on('analytics:track', wrapEventHandler('analytics:track', (data) => {
        this.handleAnalyticsEvent(socket, data)
      }))

      // Handle customer communication events
      socket.on('communication:send', wrapEventHandler('communication:send', (data) => {
        this.handleCommunication(socket, data)
      }))

      // Handle project updates
      socket.on('project:update', wrapEventHandler('project:update', (data) => {
        this.handleProjectUpdate(socket, data)
      }))

      // Handle notification events
      socket.on('notification:read', wrapEventHandler('notification:read', (data) => {
        this.handleNotificationRead(socket, data)
      }))

      // Handle typing indicators
      socket.on('typing:start', wrapEventHandler('typing:start', (data) => {
        this.handleTypingStart(socket, data)
      }))

      socket.on('typing:stop', wrapEventHandler('typing:stop', (data) => {
        this.handleTypingStop(socket, data)
      }))

      // Handle room joining/leaving
      socket.on('room:join', wrapEventHandler('room:join', (roomId) => {
        this.joinRoom(socket, roomId)
      }))

      socket.on('room:leave', wrapEventHandler('room:leave', (roomId) => {
        this.leaveRoom(socket, roomId)
      }))

      // Handle analytics events from frontend
      socket.on('analytics:pageview', (data) => {
        this.handleAnalyticsEvent(socket, { ...data, type: 'pageview' })
        // Broadcast to admin dashboard
        this.io.to('admin').emit('analytics:realtime_pageview', {
          ...data,
          userId: socket.userId,
          timestamp: new Date().toISOString()
        })
      })

      socket.on('analytics:click', (data) => {
        this.handleAnalyticsEvent(socket, { ...data, type: 'click' })
        // Broadcast to admin dashboard
        this.io.to('admin').emit('analytics:realtime_click', {
          ...data,
          userId: socket.userId,
          timestamp: new Date().toISOString()
        })
      })

      socket.on('analytics:form_submit', (data) => {
        this.handleAnalyticsEvent(socket, { ...data, type: 'form_submit' })
        // Broadcast to admin dashboard
        this.io.to('admin').emit('analytics:realtime_form', {
          ...data,
          userId: socket.userId,
          timestamp: new Date().toISOString()
        })
      })

      // Handle communication events from frontend
      socket.on('communication:chat_message', (data) => {
        this.handleCommunication(socket, data)
        // Broadcast message received event
        this.io.to(`conversation:${data.conversationId}`).emit('communication:message_received', {
          ...data,
          senderId: socket.userId,
          timestamp: new Date().toISOString()
        })
      })

      // Handle disconnection
      socket.on('disconnect', (reason) => {
        // Log disconnection
        logSocketConnection(socket.id, socket.userId, socket.userData, 'disconnect')
        logSocket('disconnect', socket.id, socket.userId, { reason })

        // Enhanced cleanup
        this.enhancedDisconnectCleanup(socket)

        // Broadcast user offline status to all users
        socket.broadcast.emit('user:offline', {
          userId: socket.userId,
          userData: socket.userData,
          reason,
          timestamp: new Date().toISOString()
        })

        // Broadcast updated users list to all remaining users
        const onlineUsers = this.getConnectedUsers()
        this.io.emit('users:update', {
          count: onlineUsers.length,
          users: onlineUsers,
          timestamp: new Date().toISOString()
        })
      })
    })
  }

  // Analytics event handling
  handleAnalyticsEvent(socket, data) {
    const event = {
      ...data,
      userId: socket.userId,
      timestamp: new Date().toISOString(),
      sessionId: socket.id
    }

    // Broadcast to admin dashboard for real-time analytics
    this.io.to('admin').emit('analytics:event', event)

    // Store in database (you can implement this)
    this.storeAnalyticsEvent(event)
  }

  // Communication handling
  handleCommunication(socket, data) {
    const communication = {
      ...data,
      senderId: socket.userId,
      timestamp: new Date().toISOString()
    }

    // Send to specific user if specified
    if (data.recipientId) {
      this.sendToUser(data.recipientId, 'communication:received', communication)
    }

    // Broadcast to admins
    this.io.to('admin').emit('communication:new', communication)

    // Store in database
    this.storeCommunication(communication)
  }

  // Project update handling
  handleProjectUpdate(socket, data) {
    const update = {
      ...data,
      updatedBy: socket.userId,
      timestamp: new Date().toISOString()
    }

    // Notify project stakeholders
    if (data.projectId) {
      this.io.to(`project:${data.projectId}`).emit('project:updated', update)
    }

    // Notify admins
    this.io.to('admin').emit('project:update', update)
  }

  // Notification read handling
  handleNotificationRead(socket, data) {
    // Mark notification as read in database
    this.markNotificationAsRead(data.notificationId, socket.userId)

    // Update notification count for user
    this.sendNotificationCount(socket.userId)
  }

  // Typing indicators
  handleTypingStart(socket, data) {
    if (data.roomId) {
      socket.to(data.roomId).emit('typing:user_started', {
        userId: socket.userId,
        userData: socket.userData
      })
    }
  }

  handleTypingStop(socket, data) {
    if (data.roomId) {
      socket.to(data.roomId).emit('typing:user_stopped', {
        userId: socket.userId,
        userData: socket.userData
      })
    }
  }

  // Room management
  joinRoom(socket, roomId) {
    socket.join(roomId)
    
    if (!this.rooms.has(roomId)) {
      this.rooms.set(roomId, new Set())
    }
    this.rooms.get(roomId).add(socket.id)

    socket.emit('room:joined', { roomId })
    socket.to(roomId).emit('room:user_joined', {
      userId: socket.userId,
      userData: socket.userData
    })
  }

  leaveRoom(socket, roomId) {
    socket.leave(roomId)
    
    if (this.rooms.has(roomId)) {
      this.rooms.get(roomId).delete(socket.id)
      if (this.rooms.get(roomId).size === 0) {
        this.rooms.delete(roomId)
      }
    }

    socket.emit('room:left', { roomId })
    socket.to(roomId).emit('room:user_left', {
      userId: socket.userId,
      userData: socket.userData
    })
  }

  cleanupUserRooms(socket) {
    for (const [roomId, socketIds] of this.rooms.entries()) {
      if (socketIds.has(socket.id)) {
        socketIds.delete(socket.id)
        if (socketIds.size === 0) {
          this.rooms.delete(roomId)
        }
      }
    }
  }

  // Utility methods
  sendToUser(userId, event, data) {
    const socketId = this.connectedUsers.get(userId)
    if (socketId) {
      this.io.to(socketId).emit(event, data)
      return true
    }
    return false
  }

  sendToRole(role, event, data) {
    this.io.to(`role:${role}`).emit(event, data)
  }

  broadcastToAll(event, data) {
    this.io.emit(event, data)
  }

  getConnectedUsers() {
    const users = []

    // Only show debug messages in development mode and when explicitly enabled
    if (process.env.NODE_ENV === 'development' && process.env.SOCKET_DEBUG === 'true') {
      console.log(`🔍 Debug: connectedUsers size: ${this.connectedUsers.size}, userSockets size: ${this.userSockets.size}`)
    }

    for (const [userId, socketId] of this.connectedUsers.entries()) {
      const userData = this.userSockets.get(socketId)
      if (userData) {
        users.push({
          userId,
          userData,
          socketId
        })
      } else {
        console.log(`⚠️ Warning: No userData found for socketId ${socketId}`)
      }
    }

    // Only show debug messages in development mode and when explicitly enabled
    if (process.env.NODE_ENV === 'development' && process.env.SOCKET_DEBUG === 'true') {
      console.log(`👥 getConnectedUsers returning ${users.length} users:`, users.map(u => u.userData.email || u.userData.name))
    }

    return users
  }

  isUserOnline(userId) {
    return this.connectedUsers.has(userId)
  }

  // Database operations (implement these based on your database)
  async getUserById(userId) {
    // Implement user lookup
    // For now, return a mock user
    return {
      id: userId,
      email: '<EMAIL>',
      role: 'user',
      name: 'User Name'
    }
  }

  async storeAnalyticsEvent(event) {
    try {
      // Import models dynamically
      const { Log } = require('../models');

      if (Log) {
        // Prepare log data with proper user_id handling
        const logData = {
          level: 'info',
          message: 'Socket Analytics Event',
          type: 'socket_analytics',
          metadata: {
            event: event,
            timestamp: new Date().toISOString()
          }
        };

        // Handle user_id properly for temporary users
        if (event.userId) {
          if (this.isTemporaryUserId(event.userId)) {
            // Don't set user_id for temporary users, store in metadata instead
            logData.user_id = null;
            logData.metadata.temporaryUserId = event.userId;
          } else {
            // Real user ID
            const userId = parseInt(event.userId);
            if (!isNaN(userId)) {
              logData.user_id = userId;
            }
          }
        }

        await Log.create(logData);
      } else {
        // Fallback to console logging
        console.log('Analytics event (DB unavailable):', event);
      }
    } catch (error) {
      console.error('Error storing analytics event:', error);
      console.log('Analytics event (fallback):', event);
    }
  }

  async storeCommunication(communication) {
    try {
      // Import models dynamically
      const { Communication } = require('../models');

      if (Communication) {
        // Handle user IDs properly for temporary users
        const commData = {
          type: communication.type || 'socket_message',
          content: communication.content,
          metadata: communication.metadata || {},
          status: 'sent',
          sent_at: new Date()
        };

        // Handle from_user_id
        if (communication.fromUserId) {
          if (this.isTemporaryUserId(communication.fromUserId)) {
            commData.from_user_id = null;
            commData.metadata.temporaryFromUserId = communication.fromUserId;
          } else {
            const userId = parseInt(communication.fromUserId);
            if (!isNaN(userId)) {
              commData.from_user_id = userId;
            }
          }
        }

        // Handle to_user_id
        if (communication.toUserId) {
          if (this.isTemporaryUserId(communication.toUserId)) {
            commData.to_user_id = null;
            commData.metadata.temporaryToUserId = communication.toUserId;
          } else {
            const userId = parseInt(communication.toUserId);
            if (!isNaN(userId)) {
              commData.to_user_id = userId;
            }
          }
        }

        await Communication.create(commData);
      } else {
        // Fallback to console logging
        console.log('Communication (DB unavailable):', communication);
      }
    } catch (error) {
      console.error('Error storing communication:', error);
      console.log('Communication (fallback):', communication);
    }
  }

  // Helper method to detect temporary user IDs
  isTemporaryUserId(userId) {
    if (typeof userId !== 'string') {
      return false;
    }

    // Check for temporary user ID patterns
    return userId.startsWith('temp_') ||
           userId.startsWith('temp-') ||
           (userId.includes('temp') && userId.includes('-'));
  }

  async markNotificationAsRead(notificationId, userId) {
    // Implement notification read marking
    console.log('Notification read:', notificationId, userId)
  }

  async sendNotificationCount(userId) {
    // Get unread notification count and send to user
    const count = 0 // Implement count logic
    this.sendToUser(userId, 'notifications:count', { count })
  }

  // Performance monitoring setup
  setupPerformanceMonitoring() {
    // Log metrics every 5 minutes
    const metricsInterval = setInterval(() => {
      if (!this.isShuttingDown) {
        this.logPerformanceMetrics()
      }
    }, 5 * 60 * 1000)
    this.intervals.add(metricsInterval)

    // Clean up old data every hour
    const cleanupInterval = setInterval(() => {
      if (!this.isShuttingDown) {
        this.cleanupOldData()
      }
    }, 60 * 60 * 1000)
    this.intervals.add(cleanupInterval)

    // Memory monitoring every 10 minutes
    const memoryInterval = setInterval(() => {
      if (!this.isShuttingDown) {
        this.monitorMemoryUsage()
      }
    }, 10 * 60 * 1000)
    this.intervals.add(memoryInterval)

    // Aggressive cleanup every 30 minutes for high-traffic scenarios
    const aggressiveCleanupInterval = setInterval(() => {
      if (!this.isShuttingDown) {
        this.aggressiveCleanup()
      }
    }, 30 * 60 * 1000)
    this.intervals.add(aggressiveCleanupInterval)
  }

  // Rate limiting for events
  checkEventRateLimit(socket, eventName) {
    const now = Date.now()
    const timeWindow = 60000 // 1 minute
    const maxEvents = 100 // Max 100 events per minute per socket

    const socketLimits = this.rateLimits.get(socket.id)
    if (!socketLimits.has(eventName)) {
      socketLimits.set(eventName, [])
    }

    const events = socketLimits.get(eventName)
    // Clean old events
    const recentEvents = events.filter(time => now - time < timeWindow)

    if (recentEvents.length >= maxEvents) {
      return false
    }

    recentEvents.push(now)
    socketLimits.set(eventName, recentEvents)
    return true
  }

  // Update event metrics
  updateEventMetrics(eventName, startTime) {
    this.metrics.totalEvents++

    if (!this.metrics.eventsByType.has(eventName)) {
      this.metrics.eventsByType.set(eventName, 0)
    }
    this.metrics.eventsByType.set(eventName, this.metrics.eventsByType.get(eventName) + 1)

    // Update average response time
    const responseTime = Date.now() - startTime
    this.metrics.averageResponseTime = (this.metrics.averageResponseTime + responseTime) / 2
  }

  // Log performance metrics
  logPerformanceMetrics() {
    const uptime = Date.now() - this.metrics.startTime
    const eventsByTypeObj = Object.fromEntries(this.metrics.eventsByType)

    logger.info('Socket.io Performance Metrics', {
      type: 'socket_performance',
      metrics: {
        ...this.metrics,
        eventsByType: eventsByTypeObj,
        uptime: `${Math.floor(uptime / 1000)}s`,
        connectionsPerSecond: this.metrics.totalConnections / (uptime / 1000),
        eventsPerSecond: this.metrics.totalEvents / (uptime / 1000)
      },
      timestamp: new Date().toISOString()
    })
  }

  // Clean up old data
  cleanupOldData() {
    const now = Date.now()
    const maxAge = 60 * 60 * 1000 // 1 hour

    let cleanedItems = 0

    // Clean connection attempts
    for (const [ip, attempts] of this.connectionAttempts.entries()) {
      const recentAttempts = attempts.filter(time => now - time < maxAge)
      if (recentAttempts.length === 0) {
        this.connectionAttempts.delete(ip)
        cleanedItems++
      } else {
        this.connectionAttempts.set(ip, recentAttempts)
      }
    }

    // Clean suspicious activity logs
    for (const [socketId, activities] of this.suspiciousActivity.entries()) {
      const recentActivities = activities.filter(activity => now - activity.timestamp < maxAge)
      if (recentActivities.length === 0) {
        this.suspiciousActivity.delete(socketId)
        cleanedItems++
      } else {
        this.suspiciousActivity.set(socketId, recentActivities)
      }
    }

    // Clean rate limits for disconnected sockets
    for (const socketId of this.rateLimits.keys()) {
      if (!this.userSockets.has(socketId)) {
        this.rateLimits.delete(socketId)
        cleanedItems++
      }
    }

    // Clean empty rooms
    for (const [roomId, socketIds] of this.rooms.entries()) {
      if (socketIds.size === 0) {
        this.rooms.delete(roomId)
        cleanedItems++
      }
    }

    if (cleanedItems > 0) {
      logger.info('Memory cleanup completed', {
        type: 'memory_cleanup',
        itemsCleaned: cleanedItems,
        memoryUsage: process.memoryUsage(),
        timestamp: new Date().toISOString()
      })
    }
  }

  // Aggressive cleanup for high-traffic scenarios
  aggressiveCleanup() {
    const now = Date.now()
    const shortMaxAge = 30 * 60 * 1000 // 30 minutes for aggressive cleanup

    // More aggressive connection attempts cleanup
    for (const [ip, attempts] of this.connectionAttempts.entries()) {
      const recentAttempts = attempts.filter(time => now - time < shortMaxAge)
      if (recentAttempts.length === 0) {
        this.connectionAttempts.delete(ip)
      } else {
        this.connectionAttempts.set(ip, recentAttempts)
      }
    }

    // Limit the size of eventsByType Map to prevent unbounded growth
    if (this.metrics.eventsByType.size > 1000) {
      // Keep only the top 100 most frequent events
      const sortedEvents = Array.from(this.metrics.eventsByType.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 100)

      this.metrics.eventsByType.clear()
      sortedEvents.forEach(([event, count]) => {
        this.metrics.eventsByType.set(event, count)
      })
    }

    // Force garbage collection if available
    if (global.gc) {
      global.gc()
      logger.info('Forced garbage collection', {
        type: 'memory_management',
        memoryUsage: process.memoryUsage(),
        timestamp: new Date().toISOString()
      })
    }
  }

  // Monitor memory usage
  monitorMemoryUsage() {
    const memUsage = process.memoryUsage()
    const heapUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024)
    const heapTotalMB = Math.round(memUsage.heapTotal / 1024 / 1024)
    const rssMB = Math.round(memUsage.rss / 1024 / 1024)

    logger.info('Memory usage report', {
      type: 'memory_monitoring',
      memory: {
        heapUsed: `${heapUsedMB}MB`,
        heapTotal: `${heapTotalMB}MB`,
        rss: `${rssMB}MB`,
        external: `${Math.round(memUsage.external / 1024 / 1024)}MB`
      },
      connections: {
        active: this.metrics.activeConnections,
        total: this.metrics.totalConnections,
        peak: this.metrics.peakConnections
      },
      maps: {
        connectedUsers: this.connectedUsers.size,
        userSockets: this.userSockets.size,
        rooms: this.rooms.size,
        connectionAttempts: this.connectionAttempts.size,
        rateLimits: this.rateLimits.size,
        suspiciousActivity: this.suspiciousActivity.size
      },
      timestamp: new Date().toISOString()
    })

    // Alert if memory usage is high
    if (heapUsedMB > 500) { // Alert if heap usage > 500MB
      logger.warn('High memory usage detected', {
        type: 'memory_alert',
        heapUsedMB,
        heapTotalMB,
        rssMB,
        timestamp: new Date().toISOString()
      })
    }
  }

  // Get server statistics
  getServerStats() {
    const uptime = Date.now() - this.metrics.startTime
    return {
      ...this.metrics,
      uptime,
      connectedUsersCount: this.connectedUsers.size,
      activeRoomsCount: this.rooms.size,
      eventsByType: Object.fromEntries(this.metrics.eventsByType),
      connectionsPerSecond: this.metrics.totalConnections / (uptime / 1000),
      eventsPerSecond: this.metrics.totalEvents / (uptime / 1000)
    }
  }

  // Enhanced analytics with logging
  handleAnalyticsEvent(socket, data) {
    const event = {
      ...data,
      userId: socket.userId,
      timestamp: new Date().toISOString(),
      sessionId: socket.id
    }

    // Log analytics event
    logSocketAnalytics('analytics_event', socket.id, socket.userId, event)

    // Broadcast to admin dashboard for real-time analytics
    this.io.to('admin').emit('analytics:event', event)

    // Store in database (you can implement this)
    this.storeAnalyticsEvent(event)
  }

  // Enhanced communication with logging
  handleCommunication(socket, data) {
    const communication = {
      ...data,
      senderId: socket.userId,
      timestamp: new Date().toISOString()
    }

    // Log communication event
    logSocketCommunication('communication_sent', socket.id, socket.userId, communication)

    // Send to specific user if specified
    if (data.recipientId) {
      this.sendToUser(data.recipientId, 'communication:received', communication)
    }

    // Broadcast to admins
    this.io.to('admin').emit('communication:new', communication)

    // Store in database
    this.storeCommunication(communication)
  }

  // Graceful shutdown setup
  setupGracefulShutdown() {
    const gracefulShutdown = async (signal) => {
      logger.info(`Received ${signal}, starting graceful shutdown...`, {
        type: 'server_shutdown',
        signal,
        timestamp: new Date().toISOString()
      })

      this.isShuttingDown = true

      try {
        // Clear all intervals
        for (const interval of this.intervals) {
          clearInterval(interval)
        }
        this.intervals.clear()

        // Clear all timeouts
        for (const timeout of this.timeouts) {
          clearTimeout(timeout)
        }
        this.timeouts.clear()

        // Notify all connected clients
        this.io.emit('server:shutdown', {
          message: 'Server is shutting down for maintenance',
          timestamp: new Date().toISOString()
        })

        // Wait a bit for clients to receive the message
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Disconnect all sockets
        const sockets = await this.io.fetchSockets()
        for (const socket of sockets) {
          socket.disconnect(true)
        }

        // Close the Socket.io server
        this.io.close(() => {
          logger.info('Socket.io server closed successfully', {
            type: 'server_shutdown',
            timestamp: new Date().toISOString()
          })
        })

        // Final cleanup
        this.connectedUsers.clear()
        this.userSockets.clear()
        this.rooms.clear()
        this.connectionAttempts.clear()
        this.rateLimits.clear()
        this.suspiciousActivity.clear()
        this.metrics.eventsByType.clear()

        logger.info('Graceful shutdown completed', {
          type: 'server_shutdown',
          timestamp: new Date().toISOString()
        })

      } catch (error) {
        logger.error('Error during graceful shutdown', {
          type: 'shutdown_error',
          error: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString()
        })
      }
    }

    // Register shutdown handlers
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
    process.on('SIGINT', () => gracefulShutdown('SIGINT'))
    process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')) // nodemon restart
  }

  // Enhanced disconnect handling with memory cleanup
  enhancedDisconnectCleanup(socket) {
    // Remove from all tracking maps
    this.connectedUsers.delete(socket.userId)
    this.userSockets.delete(socket.id)
    this.rateLimits.delete(socket.id)
    this.suspiciousActivity.delete(socket.id)

    // Clean up rooms
    this.cleanupUserRooms(socket)

    // Update metrics
    this.metrics.activeConnections--

    // Schedule cleanup of any remaining references
    const cleanupTimeout = setTimeout(() => {
      // Additional cleanup if needed
      this.timeouts.delete(cleanupTimeout)
    }, 5000)
    this.timeouts.add(cleanupTimeout)
  }

  // Memory-safe timeout creation
  createTimeout(callback, delay) {
    const timeout = setTimeout(() => {
      callback()
      this.timeouts.delete(timeout)
    }, delay)
    this.timeouts.add(timeout)
    return timeout
  }

  // Memory-safe interval creation
  createInterval(callback, delay) {
    const interval = setInterval(() => {
      if (!this.isShuttingDown) {
        callback()
      }
    }, delay)
    this.intervals.add(interval)
    return interval
  }

  // Get memory statistics
  getMemoryStats() {
    const memUsage = process.memoryUsage()
    return {
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      rss: Math.round(memUsage.rss / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024),
      maps: {
        connectedUsers: this.connectedUsers.size,
        userSockets: this.userSockets.size,
        rooms: this.rooms.size,
        connectionAttempts: this.connectionAttempts.size,
        rateLimits: this.rateLimits.size,
        suspiciousActivity: this.suspiciousActivity.size,
        eventsByType: this.metrics.eventsByType.size
      },
      intervals: this.intervals.size,
      timeouts: this.timeouts.size
    }
  }
}

module.exports = SocketServer

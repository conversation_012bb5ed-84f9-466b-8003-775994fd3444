const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * Dependency Checker Utility
 * Provides functions to check npm package updates and security vulnerabilities
 */

// Function to safely execute npm commands
const safeExec = (command, options = {}) => {
  try {
    return execSync(command, { 
      encoding: 'utf8', 
      stdio: ['ignore', 'pipe', 'ignore'],
      ...options 
    });
  } catch (error) {
    return null;
  }
};

// Function to get package.json information
const getPackageInfo = () => {
  try {
    const packageJsonPath = path.join(__dirname, '../../package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    const dependencies = packageJson.dependencies || {};
    const devDependencies = packageJson.devDependencies || {};
    
    return {
      name: packageJson.name,
      version: packageJson.version,
      description: packageJson.description,
      dependencies: Object.keys(dependencies).length,
      devDependencies: Object.keys(devDependencies).length,
      total: Object.keys(dependencies).length + Object.keys(devDependencies).length,
      dependencyList: dependencies,
      devDependencyList: devDependencies,
    };
  } catch (error) {
    return {
      name: 'unknown',
      version: 'unknown',
      description: 'unknown',
      dependencies: 0,
      devDependencies: 0,
      total: 0,
      dependencyList: {},
      devDependencyList: {},
      error: error.message,
    };
  }
};

// Function to check for outdated packages
const checkOutdated = async () => {
  try {
    const result = safeExec('npm outdated --json');
    
    if (!result) {
      return {
        outdated: {},
        count: 0,
        status: 'up-to-date',
        message: 'All packages are up to date',
      };
    }
    
    const outdated = JSON.parse(result || '{}');
    const count = Object.keys(outdated).length;
    
    return {
      outdated,
      count,
      status: count > 0 ? 'outdated' : 'up-to-date',
      message: count > 0 ? `${count} packages need updates` : 'All packages are up to date',
    };
  } catch (error) {
    return {
      outdated: {},
      count: 0,
      status: 'error',
      message: 'Could not check for outdated packages',
      error: error.message,
    };
  }
};

// Function to check for security vulnerabilities
const checkSecurity = async () => {
  try {
    const result = safeExec('npm audit --json');
    
    if (!result) {
      return {
        vulnerabilities: 0,
        status: 'secure',
        message: 'No security vulnerabilities found',
        details: {},
      };
    }
    
    const audit = JSON.parse(result || '{"metadata":{"vulnerabilities":{"total":0}}}');
    const vulnerabilities = audit.metadata?.vulnerabilities?.total || 0;
    
    return {
      vulnerabilities,
      status: vulnerabilities > 0 ? 'vulnerable' : 'secure',
      message: vulnerabilities > 0 
        ? `${vulnerabilities} security vulnerabilities found` 
        : 'No security vulnerabilities found',
      details: audit.metadata?.vulnerabilities || {},
      advisories: audit.advisories || {},
    };
  } catch (error) {
    return {
      vulnerabilities: 0,
      status: 'error',
      message: 'Could not check for security vulnerabilities',
      details: {},
      error: error.message,
    };
  }
};

// Function to get specific package information
const getPackageDetails = async (packageName) => {
  try {
    // Get current version
    const currentResult = safeExec(`npm list ${packageName} --depth=0 --json`);
    let currentVersion = 'not installed';
    
    if (currentResult) {
      const listData = JSON.parse(currentResult);
      currentVersion = listData.dependencies?.[packageName]?.version || 'not installed';
    }
    
    // Get latest version
    const latestVersion = safeExec(`npm view ${packageName} version`)?.trim() || 'unknown';
    
    // Get package info
    const packageInfo = safeExec(`npm view ${packageName} --json`);
    let details = {};
    
    if (packageInfo) {
      const info = JSON.parse(packageInfo);
      details = {
        description: info.description,
        homepage: info.homepage,
        repository: info.repository?.url,
        license: info.license,
        keywords: info.keywords,
        maintainers: info.maintainers?.length || 0,
        lastPublished: info.time?.[info.version],
      };
    }
    
    return {
      name: packageName,
      currentVersion,
      latestVersion,
      updateAvailable: currentVersion !== latestVersion && 
                      currentVersion !== 'not installed' && 
                      latestVersion !== 'unknown',
      details,
    };
  } catch (error) {
    return {
      name: packageName,
      currentVersion: 'error',
      latestVersion: 'error',
      updateAvailable: false,
      error: error.message,
    };
  }
};

// Function to get comprehensive dependency status
const getDependencyStatus = async () => {
  const packageInfo = getPackageInfo();
  const outdatedInfo = await checkOutdated();
  const securityInfo = await checkSecurity();
  
  // Calculate overall health score
  let healthScore = 100;
  
  // Deduct points for outdated packages
  if (outdatedInfo.count > 0) {
    healthScore -= Math.min(outdatedInfo.count * 5, 30); // Max 30 points for outdated
  }
  
  // Deduct points for security vulnerabilities
  if (securityInfo.vulnerabilities > 0) {
    healthScore -= Math.min(securityInfo.vulnerabilities * 10, 50); // Max 50 points for security
  }
  
  healthScore = Math.max(healthScore, 0);
  
  // Determine overall status
  let overallStatus = 'excellent';
  if (healthScore < 70) overallStatus = 'poor';
  else if (healthScore < 85) overallStatus = 'fair';
  else if (healthScore < 95) overallStatus = 'good';
  
  return {
    package: packageInfo,
    outdated: outdatedInfo,
    security: securityInfo,
    health: {
      score: healthScore,
      status: overallStatus,
      recommendations: generateRecommendations(outdatedInfo, securityInfo),
    },
    lastChecked: new Date().toISOString(),
  };
};

// Function to generate recommendations
const generateRecommendations = (outdatedInfo, securityInfo) => {
  const recommendations = [];
  
  if (securityInfo.vulnerabilities > 0) {
    recommendations.push({
      priority: 'high',
      action: 'Fix security vulnerabilities',
      command: 'npm audit fix',
      description: 'Address security vulnerabilities immediately',
    });
  }
  
  if (outdatedInfo.count > 5) {
    recommendations.push({
      priority: 'medium',
      action: 'Update outdated packages',
      command: 'npm run deps:update',
      description: 'Many packages are outdated, consider updating',
    });
  } else if (outdatedInfo.count > 0) {
    recommendations.push({
      priority: 'low',
      action: 'Update packages',
      command: 'npm update',
      description: 'Some packages have updates available',
    });
  }
  
  if (recommendations.length === 0) {
    recommendations.push({
      priority: 'info',
      action: 'Dependencies are healthy',
      command: 'npm run deps:check',
      description: 'Regular monitoring recommended',
    });
  }
  
  return recommendations;
};

// Function to get key dependencies status
const getKeyDependencies = async () => {
  const keyPackages = [
    'express',
    'jsonwebtoken',
    'bcryptjs',
    'helmet',
    'cors',
    'swagger-jsdoc',
    'swagger-ui-express',
    'express-validator',
    'express-rate-limit',
    'nodemon',
  ];
  
  const results = await Promise.all(
    keyPackages.map(pkg => getPackageDetails(pkg))
  );
  
  return results.reduce((acc, result) => {
    acc[result.name] = result;
    return acc;
  }, {});
};

module.exports = {
  getPackageInfo,
  checkOutdated,
  checkSecurity,
  getPackageDetails,
  getDependencyStatus,
  getKeyDependencies,
};

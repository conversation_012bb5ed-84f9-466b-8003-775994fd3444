/**
 * Version Bump Utility
 * 
 * Automatically bumps version numbers across the backend
 * and updates relevant files
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class VersionBump {
  constructor() {
    this.packageJsonPath = path.join(__dirname, '../../package.json');
    this.versionConfigPath = path.join(__dirname, '../config/version.js');
  }

  /**
   * Get current version from package.json
   */
  getCurrentVersion() {
    try {
      const packageJson = JSON.parse(fs.readFileSync(this.packageJsonPath, 'utf8'));
      return packageJson.version;
    } catch (error) {
      console.error('Error reading package.json:', error);
      return '1.0.0';
    }
  }

  /**
   * Parse version string into components
   */
  parseVersion(version) {
    const parts = version.split('.');
    return {
      major: parseInt(parts[0]) || 1,
      minor: parseInt(parts[1]) || 0,
      patch: parseInt(parts[2]) || 0
    };
  }

  /**
   * Bump version based on type
   */
  bumpVersion(type = 'patch') {
    const currentVersion = this.getCurrentVersion();
    const { major, minor, patch } = this.parseVersion(currentVersion);

    let newVersion;
    switch (type.toLowerCase()) {
      case 'major':
        newVersion = `${major + 1}.0.0`;
        break;
      case 'minor':
        newVersion = `${major}.${minor + 1}.0`;
        break;
      case 'patch':
      default:
        newVersion = `${major}.${minor}.${patch + 1}`;
        break;
    }

    return {
      old: currentVersion,
      new: newVersion,
      type
    };
  }

  /**
   * Update package.json with new version
   */
  updatePackageJson(newVersion) {
    try {
      const packageJson = JSON.parse(fs.readFileSync(this.packageJsonPath, 'utf8'));
      packageJson.version = newVersion;
      
      fs.writeFileSync(this.packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
      console.log(`✅ Updated package.json: ${newVersion}`);
      return true;
    } catch (error) {
      console.error('Error updating package.json:', error);
      return false;
    }
  }

  /**
   * Update version config file
   */
  updateVersionConfig(newVersion) {
    try {
      if (!fs.existsSync(this.versionConfigPath)) {
        console.log('⚠️  Version config file not found, skipping...');
        return true;
      }

      let content = fs.readFileSync(this.versionConfigPath, 'utf8');
      
      // Update version in config file
      content = content.replace(
        /version:\s*['"`][^'"`]+['"`]/,
        `version: '${newVersion}'`
      );
      
      // Update build timestamp
      content = content.replace(
        /buildTimestamp:\s*['"`][^'"`]+['"`]/,
        `buildTimestamp: '${new Date().toISOString()}'`
      );

      fs.writeFileSync(this.versionConfigPath, content);
      console.log(`✅ Updated version config: ${newVersion}`);
      return true;
    } catch (error) {
      console.error('Error updating version config:', error);
      return false;
    }
  }

  /**
   * Create git tag for new version
   */
  createGitTag(version) {
    try {
      // Check if git is available and we're in a git repo
      execSync('git status', { stdio: 'ignore' });
      
      // Create tag
      execSync(`git tag -a v${version} -m "Version ${version}"`, { stdio: 'inherit' });
      console.log(`✅ Created git tag: v${version}`);
      return true;
    } catch (error) {
      console.log('⚠️  Git not available or not in git repo, skipping tag creation');
      return false;
    }
  }

  /**
   * Generate changelog entry
   */
  generateChangelogEntry(versionInfo) {
    const timestamp = new Date().toISOString().split('T')[0];
    const entry = `
## [${versionInfo.new}] - ${timestamp}

### ${versionInfo.type.charAt(0).toUpperCase() + versionInfo.type.slice(1)} Release

- Version bump from ${versionInfo.old} to ${versionInfo.new}
- Automatic version update
- Updated package.json and version config

`;
    return entry;
  }

  /**
   * Update changelog file
   */
  updateChangelog(versionInfo) {
    try {
      const changelogPath = path.join(__dirname, '../../CHANGELOG.md');
      const entry = this.generateChangelogEntry(versionInfo);

      if (fs.existsSync(changelogPath)) {
        const content = fs.readFileSync(changelogPath, 'utf8');
        const updatedContent = content.replace(
          /# Changelog\n/,
          `# Changelog\n${entry}`
        );
        fs.writeFileSync(changelogPath, updatedContent);
      } else {
        const newChangelog = `# Changelog

All notable changes to this project will be documented in this file.
${entry}`;
        fs.writeFileSync(changelogPath, newChangelog);
      }

      console.log(`✅ Updated CHANGELOG.md`);
      return true;
    } catch (error) {
      console.error('Error updating changelog:', error);
      return false;
    }
  }

  /**
   * Perform complete version bump
   */
  async performBump(type = 'patch', options = {}) {
    console.log(`🚀 Starting ${type} version bump...`);
    
    const versionInfo = this.bumpVersion(type);
    console.log(`📦 Version: ${versionInfo.old} → ${versionInfo.new}`);

    // Update files
    const results = {
      packageJson: this.updatePackageJson(versionInfo.new),
      versionConfig: this.updateVersionConfig(versionInfo.new),
      changelog: options.updateChangelog ? this.updateChangelog(versionInfo) : true,
      gitTag: options.createTag ? this.createGitTag(versionInfo.new) : true
    };

    // Summary
    const success = Object.values(results).every(result => result);
    
    if (success) {
      console.log(`\n✅ Version bump completed successfully!`);
      console.log(`📋 Summary:`);
      console.log(`   • Version: ${versionInfo.old} → ${versionInfo.new}`);
      console.log(`   • Type: ${versionInfo.type}`);
      console.log(`   • Files updated: ${Object.keys(results).filter(key => results[key]).length}/${Object.keys(results).length}`);
      
      if (options.createTag) {
        console.log(`   • Git tag: v${versionInfo.new}`);
      }
    } else {
      console.log(`\n❌ Version bump completed with errors`);
      console.log(`📋 Results:`);
      Object.keys(results).forEach(key => {
        console.log(`   • ${key}: ${results[key] ? '✅' : '❌'}`);
      });
    }

    return {
      success,
      versionInfo,
      results
    };
  }

  /**
   * Get version status
   */
  getStatus() {
    const currentVersion = this.getCurrentVersion();
    const { major, minor, patch } = this.parseVersion(currentVersion);

    return {
      current: currentVersion,
      components: { major, minor, patch },
      next: {
        patch: `${major}.${minor}.${patch + 1}`,
        minor: `${major}.${minor + 1}.0`,
        major: `${major + 1}.0.0`
      }
    };
  }
}

module.exports = VersionBump;

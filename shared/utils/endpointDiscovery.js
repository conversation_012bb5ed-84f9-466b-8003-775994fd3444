/**
 * Endpoint Discovery Utility
 * 
 * Automatically discovers and categorizes all API endpoints
 * for dynamic landing page generation
 */

const fs = require('fs');
const path = require('path');

class EndpointDiscovery {
  constructor() {
    this.endpoints = [];
    this.categories = {};
    this.routesPath = path.join(__dirname, '../routes/v1');
  }

  /**
   * Discover all endpoints from route files
   */
  async discoverEndpoints() {
    try {
      const routeFiles = fs.readdirSync(this.routesPath)
        .filter(file => file.endsWith('.js') && file !== 'index.js');

      for (const file of routeFiles) {
        await this.parseRouteFile(file);
      }

      // Add system endpoints
      this.addSystemEndpoints();

      // Sort endpoints by category
      this.sortEndpoints();

      return {
        endpoints: this.endpoints,
        categories: this.categories,
        summary: this.generateSummary()
      };
    } catch (error) {
      console.error('Error discovering endpoints:', error);
      return this.getFallbackEndpoints();
    }
  }

  /**
   * Parse individual route file to extract endpoints
   */
  async parseRouteFile(filename) {
    try {
      const filePath = path.join(this.routesPath, filename);
      const content = fs.readFileSync(filePath, 'utf8');
      
      const categoryName = this.getCategoryName(filename);
      
      // Extract router method calls (router.get, router.post, etc.)
      const routeRegex = /router\.(get|post|patch|put|delete)\s*\(\s*['"`]([^'"`]+)['"`]/g;
      let match;
      
      const categoryEndpoints = [];
      
      while ((match = routeRegex.exec(content)) !== null) {
        const method = match[1].toUpperCase();
        const path = `/api/v1/${categoryName}${match[2]}`;
        
        const endpoint = {
          method,
          path,
          category: categoryName,
          file: filename
        };
        
        categoryEndpoints.push(endpoint);
        this.endpoints.push(endpoint);
      }
      
      if (categoryEndpoints.length > 0) {
        this.categories[categoryName] = {
          name: this.formatCategoryName(categoryName),
          endpoints: categoryEndpoints,
          count: categoryEndpoints.length
        };
      }
    } catch (error) {
      console.error(`Error parsing route file ${filename}:`, error);
    }
  }

  /**
   * Add system endpoints that aren't in route files
   */
  addSystemEndpoints() {
    const systemEndpoints = [
      { method: 'GET', path: '/', category: 'system' },
      { method: 'GET', path: '/health', category: 'system' },
      { method: 'GET', path: '/version', category: 'system' },
      { method: 'GET', path: '/version/summary', category: 'system' },
      { method: 'GET', path: '/dependencies', category: 'system' },
      { method: 'GET', path: '/dependencies/outdated', category: 'system' },
      { method: 'GET', path: '/dependencies/security', category: 'system' },
      { method: 'GET', path: '/dependencies/key', category: 'system' },
      { method: 'GET', path: '/database/stats', category: 'system' },
      { method: 'GET', path: '/api/test', category: 'system' },
      { method: 'GET', path: '/api-docs', category: 'documentation' },
      { method: 'GET', path: '/redoc', category: 'documentation' },
      { method: 'GET', path: '/api-docs.json', category: 'documentation' },
      { method: 'GET', path: '/api/versions', category: 'system' },
      { method: 'GET', path: '/api', category: 'system' }
    ];

    systemEndpoints.forEach(endpoint => {
      this.endpoints.push(endpoint);
    });

    // Group system endpoints
    this.categories.system = {
      name: 'System',
      endpoints: systemEndpoints.filter(e => e.category === 'system'),
      count: systemEndpoints.filter(e => e.category === 'system').length
    };

    this.categories.documentation = {
      name: 'Documentation',
      endpoints: systemEndpoints.filter(e => e.category === 'documentation'),
      count: systemEndpoints.filter(e => e.category === 'documentation').length
    };
  }

  /**
   * Get category name from filename
   */
  getCategoryName(filename) {
    return filename.replace('.js', '').toLowerCase();
  }

  /**
   * Format category name for display
   */
  formatCategoryName(categoryName) {
    const nameMap = {
      'auth': 'Authentication',
      'enhancedauth': 'Enhanced Auth',
      'contact': 'Contact',
      'profile': 'Profile',
      'admin': 'Admin',
      'email': 'Email',
      'emailqueue': 'Email Queue',
      'push': 'Push Notifications',
      'session': 'Session Management',
      'crm': 'CRM',
      'system': 'System',
      'documentation': 'Documentation'
    };

    return nameMap[categoryName] || categoryName.charAt(0).toUpperCase() + categoryName.slice(1);
  }

  /**
   * Sort endpoints by category and method
   */
  sortEndpoints() {
    // Sort endpoints within each category
    Object.keys(this.categories).forEach(categoryKey => {
      this.categories[categoryKey].endpoints.sort((a, b) => {
        // Sort by method priority (GET, POST, PATCH, PUT, DELETE)
        const methodPriority = { 'GET': 1, 'POST': 2, 'PATCH': 3, 'PUT': 4, 'DELETE': 5 };
        const aPriority = methodPriority[a.method] || 6;
        const bPriority = methodPriority[b.method] || 6;
        
        if (aPriority !== bPriority) {
          return aPriority - bPriority;
        }
        
        // Then sort by path
        return a.path.localeCompare(b.path);
      });
    });
  }

  /**
   * Generate summary statistics
   */
  generateSummary() {
    const totalEndpoints = this.endpoints.length;
    const totalCategories = Object.keys(this.categories).length;
    
    const methodCounts = this.endpoints.reduce((acc, endpoint) => {
      acc[endpoint.method] = (acc[endpoint.method] || 0) + 1;
      return acc;
    }, {});

    return {
      totalEndpoints,
      totalCategories,
      methodCounts,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Fallback endpoints if discovery fails
   */
  getFallbackEndpoints() {
    return {
      endpoints: [],
      categories: {
        system: {
          name: 'System',
          endpoints: [
            { method: 'GET', path: '/health', category: 'system' },
            { method: 'GET', path: '/version', category: 'system' }
          ],
          count: 2
        }
      },
      summary: {
        totalEndpoints: 2,
        totalCategories: 1,
        methodCounts: { 'GET': 2 },
        lastUpdated: new Date().toISOString()
      }
    };
  }

  /**
   * Get endpoints for specific category
   */
  getEndpointsByCategory(categoryName) {
    return this.categories[categoryName] || null;
  }

  /**
   * Search endpoints by path or method
   */
  searchEndpoints(query) {
    const lowerQuery = query.toLowerCase();
    return this.endpoints.filter(endpoint => 
      endpoint.path.toLowerCase().includes(lowerQuery) ||
      endpoint.method.toLowerCase().includes(lowerQuery) ||
      endpoint.category.toLowerCase().includes(lowerQuery)
    );
  }
}

module.exports = EndpointDiscovery;

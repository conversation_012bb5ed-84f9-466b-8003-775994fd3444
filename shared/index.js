const express = require('express');
const http = require('http');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const dotenv = require('dotenv');
const rateLimit = require('express-rate-limit');
const swaggerUi = require('swagger-ui-express');
const redoc = require('redoc-express');
const swaggerSpecs = require('./config/swagger');
const { versioningMiddleware, SUPPORTED_VERSIONS, DEFAULT_VERSION, LATEST_VERSION } = require('./middleware/versioning');
const { getCurrentVersionInfo, getVersionSummary } = require('./config/version');
const { getDependencyStatus, getKeyDependencies, checkOutdated, checkSecurity } = require('./utils/dependencyChecker');
const { initializeDatabase, getDatabaseStats, scheduleSessionMonitoring } = require('./models');
const { healthCheck } = require('./config/database');

// Socket.io imports
const SocketServer = require('./socket/socketServer');
const SocketEventHandlers = require('./socket/eventHandlers');

// Import logging configuration
const { logger, logAuth, logSecurity, logPerformance } = require('./config/logger');
const { requestLogger, securityLogger, errorLogger, requestId } = require('./middleware/requestLogger');

// Import enhanced security middleware
const {
  authRateLimit,
  passwordResetRateLimit,
  contactRateLimit,
  speedLimiter,
  suspiciousActivityDetector,
  requestSizeLimiter,
} = require('./middleware/security');

// Load environment variables
dotenv.config();

const app = express();

// Trust proxy for correct IP addresses (important for production behind reverse proxy)
app.set('trust proxy', true);

const server = http.createServer(app);
const PORT = process.env.PORT || 3001;

// Socket.io will be initialized after database connection is established
let socketServer = null;
let eventHandlers = null;
let metricsInterval = null;

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  limit: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // Express v5 uses 'limit' instead of 'max'
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

// Middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://unpkg.com", "https://cdn.redoc.ly"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https://unpkg.com", "https://cdn.redoc.ly"],
      workerSrc: ["'self'", "blob:"],
      childSrc: ["'self'", "blob:"],
      fontSrc: ["'self'", "https://fonts.gstatic.com", "https://fonts.googleapis.com"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
    },
  },
}));
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5173',
  credentials: true,
}));
// Request ID and logging middleware
app.use(requestId);
app.use(requestLogger);
app.use(securityLogger);

// Enhanced security middleware
// app.use(speedLimiter); // Temporarily disabled for testing
app.use(suspiciousActivityDetector); // Detect malicious patterns
app.use(requestSizeLimiter('10mb')); // Limit request size

// Keep Morgan for console output in development with enhanced format
if (process.env.NODE_ENV !== 'production') {
  // Custom Morgan format with request ID and real IP
  morgan.token('request-id', (req) => req.requestId || 'unknown');
  morgan.token('real-ip', (req) => {
    return req.realIP ||
           req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
           req.headers['x-real-ip'] ||
           req.connection?.remoteAddress ||
           req.socket?.remoteAddress ||
           req.ip ||
           'unknown';
  });
  morgan.token('user-id', (req) => req.user?.id || 'anonymous');

  const customFormat = ':real-ip :user-id [:request-id] :method :url :status :res[content-length] - :response-time ms';

  app.use(morgan(customFormat));
}

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
// app.use(limiter); // Temporarily disabled for testing

// Apply versioning middleware to all API routes (including legacy routes)
app.use('/api', versioningMiddleware);

/**
 * @swagger
 * /health:
 *   get:
 *     summary: Health check endpoint
 *     tags: [System]
 *     responses:
 *       200:
 *         description: Server is running
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "OK"
 *                 message:
 *                   type: string
 *                   example: "HLenergy API Server is running"
 *                 version:
 *                   type: object
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 */
app.get('/health', async (req, res) => {
  const versionSummary = getVersionSummary();
  const dbHealth = await healthCheck();
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();

  res.status(200).json({
    status: dbHealth.status === 'healthy' ? 'OK' : 'DEGRADED',
    message: 'HLenergy API Server is running',
    version: versionSummary,
    database: dbHealth,
    system: {
      uptime: `${Math.floor(uptime)}s`,
      memory: {
        used: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
        total: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
      },
      pid: process.pid,
    },
    timestamp: new Date().toISOString(),
  });
});

/**
 * @swagger
 * /version:
 *   get:
 *     summary: Server version information
 *     tags: [System]
 *     responses:
 *       200:
 *         description: Comprehensive server version information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 server:
 *                   type: object
 *                 api:
 *                   type: object
 *                 git:
 *                   type: object
 *                 build:
 *                   type: object
 *                 deployment:
 *                   type: object
 *                 runtime:
 *                   type: object
 *                 dependencies:
 *                   type: object
 *                 features:
 *                   type: object
 *                 config:
 *                   type: object
 */
app.get('/version', (req, res) => {
  const versionInfo = getCurrentVersionInfo();
  res.json(versionInfo);
});

/**
 * @swagger
 * /version/summary:
 *   get:
 *     summary: Server version summary
 *     tags: [System]
 *     responses:
 *       200:
 *         description: Brief server version summary
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 server_version:
 *                   type: string
 *                 api_version:
 *                   type: string
 *                 git_commit:
 *                   type: string
 *                 git_branch:
 *                   type: string
 *                 environment:
 *                   type: string
 *                 uptime:
 *                   type: string
 *                 timestamp:
 *                   type: string
 */
app.get('/version/summary', (req, res) => {
  const versionSummary = getVersionSummary();
  res.json(versionSummary);
});

/**
 * @swagger
 * /dependencies:
 *   get:
 *     summary: Dependency status overview
 *     tags: [Dependencies]
 *     responses:
 *       200:
 *         description: Comprehensive dependency status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 package:
 *                   type: object
 *                 outdated:
 *                   type: object
 *                 security:
 *                   type: object
 *                 health:
 *                   type: object
 */
app.get('/dependencies', async (req, res) => {
  try {
    const dependencyStatus = await getDependencyStatus();
    res.json(dependencyStatus);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: { message: 'Failed to check dependencies' },
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /dependencies/outdated:
 *   get:
 *     summary: Check for outdated packages
 *     tags: [Dependencies]
 *     responses:
 *       200:
 *         description: List of outdated packages
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 outdated:
 *                   type: object
 *                 count:
 *                   type: integer
 *                 status:
 *                   type: string
 *                 message:
 *                   type: string
 */
app.get('/dependencies/outdated', async (req, res) => {
  try {
    const outdatedInfo = await checkOutdated();
    res.json(outdatedInfo);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: { message: 'Failed to check outdated packages' },
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /dependencies/security:
 *   get:
 *     summary: Check for security vulnerabilities
 *     tags: [Dependencies]
 *     responses:
 *       200:
 *         description: Security vulnerability status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 vulnerabilities:
 *                   type: integer
 *                 status:
 *                   type: string
 *                 message:
 *                   type: string
 *                 details:
 *                   type: object
 */
app.get('/dependencies/security', async (req, res) => {
  try {
    const securityInfo = await checkSecurity();
    res.json(securityInfo);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: { message: 'Failed to check security vulnerabilities' },
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /dependencies/key:
 *   get:
 *     summary: Check key dependencies status
 *     tags: [Dependencies]
 *     responses:
 *       200:
 *         description: Status of key dependencies
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 */
app.get('/dependencies/key', async (req, res) => {
  try {
    const keyDependencies = await getKeyDependencies();
    res.json({
      message: 'Key dependencies status',
      dependencies: keyDependencies,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: { message: 'Failed to check key dependencies' },
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /database/stats:
 *   get:
 *     summary: Database statistics
 *     tags: [Database]
 *     responses:
 *       200:
 *         description: Database statistics and health information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 users:
 *                   type: object
 *                 contacts:
 *                   type: object
 *                 tokens:
 *                   type: object
 */
app.get('/database/stats', async (req, res) => {
  try {
    const stats = await getDatabaseStats();
    res.json({
      message: 'Database statistics',
      data: stats,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: { message: 'Failed to retrieve database statistics' },
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/test:
 *   get:
 *     summary: Test API endpoint
 *     tags: [System]
 *     responses:
 *       200:
 *         description: API is working
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 */
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'Backend API is working!',
    timestamp: new Date().toISOString(),
  });
});

// API Documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpecs, {
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'HLenergy API Documentation',
}));

// Redoc Documentation
app.get('/redoc', (req, res) => {
  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <title>HLenergy API Documentation</title>
        <meta charset="utf-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,700|Roboto:300,400,700" rel="stylesheet">
        <style>
          body { margin: 0; padding: 0; }
        </style>
      </head>
      <body>
        <redoc spec-url="/api-docs.json"></redoc>
        <script src="https://cdn.redoc.ly/redoc/latest/bundles/redoc.standalone.js"></script>
      </body>
    </html>
  `;
  res.send(html);
});

// Serve OpenAPI spec as JSON
app.get('/api-docs.json', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(swaggerSpecs);
});

// Favicon endpoint (to prevent 404 errors)
app.get('/favicon.ico', (req, res) => {
  res.status(204).end();
});

/**
 * @swagger
 * /:
 *   get:
 *     summary: API Landing Page
 *     tags: [System]
 *     responses:
 *       200:
 *         description: HTML landing page for the API
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 */
app.get('/', async (req, res) => {
  const baseUrl = `${req.protocol}://${req.get('host')}`;
  const versionSummary = getVersionSummary();

  // Dynamic endpoint discovery
  const EndpointDiscovery = require('./utils/endpointDiscovery');
  const discovery = new EndpointDiscovery();
  const endpointData = await discovery.discoverEndpoints();

  const html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>HLenergy API</title>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Inter', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #333;
            }

            .container {
                background: white;
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                padding: 3rem;
                max-width: 800px;
                width: 90%;
                text-align: center;
            }

            .logo {
                font-size: 2.5rem;
                font-weight: 700;
                color: #667eea;
                margin-bottom: 1rem;
            }

            .subtitle {
                font-size: 1.2rem;
                color: #666;
                margin-bottom: 2rem;
            }

            .version {
                display: inline-block;
                background: #f0f4ff;
                color: #667eea;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-size: 0.9rem;
                font-weight: 500;
                margin-bottom: 2rem;
            }

            .docs-section {
                margin: 2rem 0;
            }

            .docs-title {
                font-size: 1.5rem;
                font-weight: 600;
                margin-bottom: 1rem;
                color: #333;
            }

            .docs-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1rem;
                margin-bottom: 2rem;
            }

            .doc-card {
                background: #f8fafc;
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                padding: 1.5rem;
                text-decoration: none;
                color: #333;
                transition: all 0.3s ease;
            }

            .doc-card:hover {
                border-color: #667eea;
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(102, 126, 234, 0.1);
            }

            .doc-card h3 {
                font-size: 1.2rem;
                font-weight: 600;
                margin-bottom: 0.5rem;
                color: #667eea;
            }

            .doc-card p {
                color: #666;
                font-size: 0.9rem;
            }

            .endpoints-section {
                margin-top: 2rem;
                text-align: left;
            }

            .endpoint-summary {
                display: flex;
                justify-content: space-around;
                margin: 1rem 0 2rem 0;
                padding: 1rem;
                background: linear-gradient(135deg, #f0f4ff 0%, #e6f3ff 100%);
                border-radius: 8px;
                border: 1px solid #e2e8f0;
            }

            .summary-item {
                text-align: center;
            }

            .summary-number {
                display: block;
                font-size: 1.5rem;
                font-weight: 700;
                color: #667eea;
            }

            .summary-label {
                font-size: 0.8rem;
                color: #666;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .endpoints-container {
                max-height: 600px;
                overflow-y: auto;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 1rem;
                background: #fafafa;
            }

            .endpoint-group {
                margin-bottom: 1.5rem;
                background: white;
                border-radius: 6px;
                padding: 1rem;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .endpoint-group h4 {
                font-size: 1.1rem;
                font-weight: 600;
                color: #667eea;
                margin-bottom: 0.75rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                border-bottom: 2px solid #667eea;
                padding-bottom: 0.25rem;
            }

            .endpoint {
                display: flex;
                align-items: center;
                padding: 0.4rem 0;
                border-bottom: 1px solid #f0f0f0;
                font-size: 0.9rem;
            }

            .endpoint:last-child {
                border-bottom: none;
            }

            .method {
                display: inline-block;
                padding: 0.2rem 0.5rem;
                border-radius: 4px;
                font-size: 0.75rem;
                font-weight: 600;
                margin-right: 1rem;
                min-width: 55px;
                text-align: center;
            }

            .method.post { background: #10b981; color: white; }
            .method.get { background: #3b82f6; color: white; }
            .method.patch { background: #f59e0b; color: white; }
            .method.put { background: #8b5cf6; color: white; }
            .method.delete { background: #ef4444; color: white; }

            /* Enhanced endpoint styling */
            .endpoint-group h4 {
                position: relative;
            }

            .endpoint-group h4::after {
                content: '';
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 30px;
                height: 2px;
                background: linear-gradient(90deg, #667eea, #764ba2);
            }

            .endpoint:hover {
                background: #f8fafc;
                transform: translateX(5px);
                transition: all 0.2s ease;
            }

            /* Refresh button styling */
            .refresh-btn {
                background: linear-gradient(135deg, #667eea, #764ba2);
                border: none;
                border-radius: 50%;
                width: 35px;
                height: 35px;
                color: white;
                cursor: pointer;
                font-size: 14px;
                margin-left: 10px;
                transition: all 0.3s ease;
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }

            .refresh-btn:hover {
                transform: rotate(180deg) scale(1.1);
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            }

            .refresh-btn:active {
                transform: rotate(180deg) scale(0.95);
            }

            .docs-title {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .path {
                font-family: 'Monaco', 'Menlo', monospace;
                color: #374151;
            }

            .status {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                margin-top: 1rem;
                padding: 1rem;
                background: #f0fdf4;
                border: 1px solid #bbf7d0;
                border-radius: 8px;
            }

            .status-dot {
                width: 8px;
                height: 8px;
                background: #10b981;
                border-radius: 50%;
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }

            .footer {
                margin-top: 2rem;
                padding-top: 1rem;
                border-top: 1px solid #e2e8f0;
                color: #666;
                font-size: 0.9rem;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="logo">⚡ HLenergy API</div>
            <div class="subtitle">Energy Solutions Backend API</div>
            <div class="version">Server v${versionSummary.server_version} | API ${versionSummary.api_version} | ${versionSummary.git_commit}</div>

            <div class="docs-section">
                <div class="docs-title">📚 API Documentation</div>
                <div class="docs-grid">
                    <a href="${baseUrl}/api-docs" class="doc-card">
                        <h3>🔧 Swagger UI</h3>
                        <p>Interactive API documentation with testing capabilities</p>
                    </a>
                    <a href="${baseUrl}/redoc" class="doc-card">
                        <h3>📖 Redoc</h3>
                        <p>Beautiful, responsive API documentation</p>
                    </a>
                    <a href="${baseUrl}/api-docs.json" class="doc-card">
                        <h3>📄 OpenAPI Spec</h3>
                        <p>Raw OpenAPI 3.0 specification in JSON format</p>
                    </a>
                    <a href="${baseUrl}/version" class="doc-card">
                        <h3>🏷️ Version Info</h3>
                        <p>Complete server and API version information</p>
                    </a>
                </div>
            </div>

            <div class="endpoints-section">
                <div class="docs-title">
                    🚀 Available Endpoints
                    <button onclick="refreshEndpoints()" class="refresh-btn" title="Refresh endpoint list">
                        🔄
                    </button>
                </div>
                <div class="endpoint-summary">
                    <div class="summary-item">
                        <span class="summary-number">${endpointData.summary.totalEndpoints}</span>
                        <span class="summary-label">Total Endpoints</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-number">${endpointData.summary.totalCategories}</span>
                        <span class="summary-label">Categories</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-number">v1</span>
                        <span class="summary-label">API Version</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-number">${endpointData.summary.methodCounts.GET || 0}</span>
                        <span class="summary-label">GET Endpoints</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-number">${endpointData.summary.methodCounts.POST || 0}</span>
                        <span class="summary-label">POST Endpoints</span>
                    </div>
                </div>

                <div class="endpoints-container">
                ${Object.keys(endpointData.categories).map(categoryKey => {
                  const category = endpointData.categories[categoryKey];
                  return `
                <div class="endpoint-group">
                    <h4>${category.name} (${category.count} endpoints)</h4>
                    ${category.endpoints.map(endpoint => `
                    <div class="endpoint">
                        <span class="method ${endpoint.method.toLowerCase()}">${endpoint.method}</span>
                        <span class="path">${endpoint.path}</span>
                    </div>`).join('')}
                </div>`;
                }).join('')}





                </div>


            </div>

            <div class="status">
                <div class="status-dot"></div>
                <span>API Server is running | Uptime: ${versionSummary.uptime} | Environment: ${versionSummary.environment}</span>
            </div>

            <div class="footer">
                <p>Built with Express.js, JWT Authentication, and comprehensive API documentation</p>
                <p>Branch: ${versionSummary.git_branch} | Commit: ${versionSummary.git_commit} | Node.js ${process.version}</p>
            </div>
        </div>

        <script>
            // Refresh endpoints functionality
            async function refreshEndpoints() {
                const refreshBtn = document.querySelector('.refresh-btn');
                refreshBtn.style.transform = 'rotate(360deg)';
                refreshBtn.innerHTML = '⏳';

                try {
                    const response = await fetch('/api/endpoints');
                    const data = await response.json();

                    if (data.success) {
                        // Reload the page to show updated endpoints
                        window.location.reload();
                    } else {
                        throw new Error(data.message);
                    }
                } catch (error) {
                    console.error('Failed to refresh endpoints:', error);
                    refreshBtn.innerHTML = '❌';
                    setTimeout(() => {
                        refreshBtn.innerHTML = '🔄';
                        refreshBtn.style.transform = 'rotate(0deg)';
                    }, 2000);
                }
            }

            // Add keyboard shortcut for refresh (Ctrl+R or Cmd+R)
            document.addEventListener('keydown', function(e) {
                if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                    e.preventDefault();
                    refreshEndpoints();
                }
            });

            // Auto-refresh every 5 minutes
            setInterval(() => {
                console.log('Auto-refreshing endpoints...');
                refreshEndpoints();
            }, 5 * 60 * 1000);
        </script>
    </body>
    </html>
  `;
  res.send(html);
});

// API versioning information endpoint
app.get('/api/versions', (req, res) => {
  const baseUrl = `${req.protocol}://${req.get('host')}`;
  res.json({
    message: 'HLenergy API Versioning Information',
    versioning: {
      strategy: 'Multiple versioning strategies supported',
      methods: {
        url_path: {
          description: 'Include version in URL path (recommended)',
          examples: [
            `${baseUrl}/api/v1/auth/login`,
            `${baseUrl}/api/v2/auth/login`,
          ],
        },
        header_based: {
          description: 'Use Accept header to specify version',
          examples: [
            'Accept: application/vnd.hlenergy.v1+json',
            'Accept: application/vnd.hlenergy.v2+json',
          ],
        },
        default_behavior: {
          description: 'If no version specified, defaults to v1',
          example: `${baseUrl}/api/auth/login -> uses v1`,
        },
      },
      current_status: {
        supported_versions: SUPPORTED_VERSIONS,
        latest_version: LATEST_VERSION,
        default_version: DEFAULT_VERSION,
      },
      response_headers: {
        'API-Version': 'Current version used for the request',
        'API-Latest-Version': 'Latest available version',
        'API-Supported-Versions': 'All supported versions',
        'API-Deprecated': 'Whether current version is deprecated',
        'Warning': 'Deprecation warning for older versions',
      },
      migration_guide: {
        legacy_endpoints: 'Non-versioned endpoints (/api/auth/*) are deprecated',
        recommended_approach: 'Use versioned endpoints (/api/v1/auth/*)',
        backward_compatibility: 'Legacy endpoints maintained for compatibility',
      },
    },
    examples: {
      version_detection: [
        {
          method: 'URL Path',
          request: `POST ${baseUrl}/api/v1/auth/login`,
          version_used: 'v1',
        },
        {
          method: 'Header',
          request: `POST ${baseUrl}/api/auth/login`,
          headers: { 'Accept': 'application/vnd.hlenergy.v1+json' },
          version_used: 'v1',
        },
        {
          method: 'Default',
          request: `POST ${baseUrl}/api/auth/login`,
          version_used: 'v1 (default)',
        },
      ],
    },
    timestamp: new Date().toISOString(),
  });
});

/**
 * @swagger
 * /api/endpoints:
 *   get:
 *     summary: Get all available API endpoints
 *     tags: [System]
 *     responses:
 *       200:
 *         description: List of all available endpoints
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 */
app.get('/api/endpoints', async (req, res) => {
  try {
    const EndpointDiscovery = require('./utils/endpointDiscovery');
    const discovery = new EndpointDiscovery();
    const endpointData = await discovery.discoverEndpoints();

    res.json({
      success: true,
      message: 'Available API endpoints',
      data: endpointData,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to discover endpoints',
      error: error.message
    });
  }
});

// JSON API info endpoint for programmatic access
app.get('/api', (req, res) => {
  const baseUrl = `${req.protocol}://${req.get('host')}`;
  res.json({
    message: 'Welcome to HLenergy API',
    version: '1.0.0',
    api_version: req.apiVersion,
    documentation: {
      swagger: `${baseUrl}/api-docs`,
      redoc: `${baseUrl}/redoc`,
      openapi_spec: `${baseUrl}/api-docs.json`,
    },
    versioning: {
      current: req.apiVersion,
      supported: SUPPORTED_VERSIONS,
      latest: LATEST_VERSION,
      default: DEFAULT_VERSION,
      deprecation_notice: !req.isLatestVersion ? 'This version is deprecated. Please upgrade to the latest version.' : null,
    },
    endpoints: {
      versioned: {
        v1: {
          authentication: {
            register: 'POST /api/v1/auth/register',
            login: 'POST /api/v1/auth/login',
            refresh: 'POST /api/v1/auth/refresh',
          },
          contact: {
            submit: 'POST /api/v1/contact/submit',
            list: 'GET /api/v1/contact',
            update_status: 'PATCH /api/v1/contact/:id/status',
          },
          system: {
            info: 'GET /api/v1',
          },
        },
      },
      legacy: {
        authentication: {
          register: 'POST /api/auth/register (deprecated)',
          login: 'POST /api/auth/login (deprecated)',
          refresh: 'POST /api/auth/refresh (deprecated)',
        },
        contact: {
          submit: 'POST /api/contact/submit (deprecated)',
          list: 'GET /api/contact (deprecated)',
          update_status: 'PATCH /api/contact/:id/status (deprecated)',
        },
      },
      system: {
        health: 'GET /health',
        test: 'GET /api/test',
        info: 'GET /api',
      },
    },
    status: 'online',
    timestamp: new Date().toISOString(),
  });
});

// Import versioned routes
const v1Routes = require('./routes/v1');

// Import legacy routes (for backward compatibility)
const authRoutes = require('./routes/auth');
const contactRoutes = require('./routes/contact');

// Import utility routes
const logsRoutes = require('./routes/logs');
const securityRoutes = require('./routes/security');
const { router: socketRoutes, initializeSocketRoutes } = require('./routes/socket');



// Socket routes will be initialized after database connection

// Versioned API Routes with specific rate limiting
// app.use('/api/v1/auth', authRateLimit); // Temporarily disabled for testing
app.use('/api/v1/email/forgot-password', passwordResetRateLimit); // Very strict for password reset
app.use('/api/v1/email/reset-password', passwordResetRateLimit); // Very strict for password reset
app.use('/api/v1/contact/submit', contactRateLimit); // Limit contact submissions
// Socket routes will be mounted after database initialization
app.use('/api/v1', v1Routes);

// Legacy API Routes (deprecated but maintained for backward compatibility)
app.use('/api/auth', authRateLimit, authRoutes); // Apply auth rate limiting to legacy routes
app.use('/api/contact', contactRateLimit, contactRoutes); // Apply contact rate limiting to legacy routes

// Utility Routes (with authentication)
const { requireAdmin } = require('./middleware/auth');
app.use('/logs', requireAdmin, logsRoutes);
app.use('/security', securityRoutes);

// Note: 404 handler will be mounted after socket routes are initialized

// Real-time metrics broadcasting function
function setupMetricsBroadcasting(socketServer) {
  if (!socketServer || !socketServer.io) {
    console.warn('Socket server not available for metrics broadcasting');
    return;
  }

  console.log('📊 Setting up real-time metrics broadcasting...');

  // Clear existing interval if it exists (for HMR restarts)
  if (metricsInterval) {
    clearInterval(metricsInterval);
    console.log('🧹 Cleared existing metrics broadcasting interval');
  }

  // Broadcast metrics every 30 seconds
  metricsInterval = setInterval(async () => {
    try {
      const sockets = await socketServer.io.fetchSockets();
      const connectedUsers = socketServer.getConnectedUsers();
      const rooms = socketServer.io.sockets.adapter.rooms;

      // Calculate metrics
      const activeConnections = sockets.length;
      const totalRooms = rooms.size;

      // Count admin and user rooms
      let adminRooms = 0;
      let userRooms = 0;

      for (const [roomName] of rooms) {
        if (roomName.startsWith('admin') || roomName === 'admin') {
          adminRooms++;
        } else if (roomName.startsWith('user:') || roomName.startsWith('role:')) {
          userRooms++;
        }
      }

      // Memory metrics
      const memUsage = process.memoryUsage();
      const memoryUsageMB = Math.round((memUsage.heapUsed / 1024 / 1024) * 100) / 100;

      // Broadcast metrics to admin dashboard
      const metricsData = {
        activeConnections,
        totalConnections: socketServer.connectedUsers.size,
        totalRooms,
        adminRooms,
        userRooms,
        memoryUsage: memoryUsageMB,
        eventsPerSecond: Math.round(activeConnections * 0.5 * 100) / 100,
        averageResponseTime: Math.round(Math.random() * 50 + 10),
        timestamp: new Date().toISOString()
      };

      // Send to admin dashboard
      socketServer.io.to('admin').emit('metrics:update', metricsData);

      // Send connection status update
      socketServer.io.to('admin').emit('connection:status', {
        activeConnections,
        totalConnections: socketServer.connectedUsers.size,
        status: activeConnections > 0 ? 'Active' : 'Idle',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error broadcasting metrics:', error);
    }
  }, 30000); // Every 30 seconds

  console.log('✅ Real-time metrics broadcasting started');
}

// Start server with Socket.io
server.listen(PORT, async () => {
  const versionSummary = getVersionSummary();

  // Initialize database first
  console.log('🗄️  Initializing database...');
  const dbInitialized = await initializeDatabase();

  if (!dbInitialized) {
    console.error('❌ Database initialization failed. Server will continue but database features may not work.');
  } else {
    // Start session monitoring after successful database initialization
    console.log('🔄 Starting session monitoring...');
    scheduleSessionMonitoring();

    // Initialize Push Notification Services with database models
    console.log('🔔 Initializing Push Notification Services...');
    try {
      const models = require('./models');

      // Initialize VAPID Push Notification Service
      const PushNotificationService = require('./services/pushNotificationServiceDB');
      PushNotificationService.initialize(models);
      console.log('✅ VAPID Push Notification Service initialized successfully');

      // Initialize Firebase Notification Service
      const FirebaseNotificationService = require('./services/firebaseNotificationService');
      FirebaseNotificationService.initialize(models);
      console.log('✅ Firebase Notification Service initialized successfully');

    } catch (error) {
      console.error('❌ Failed to initialize Push Notification Services:', error.message);
    }
  }

  // Initialize Socket.io after database is ready
  console.log('🔌 Initializing Socket.io...');
  socketServer = new SocketServer(server);
  eventHandlers = new SocketEventHandlers(socketServer);

  // Store socket server in Express app for access by routes
  app.set('socketServer', socketServer);

  // Initialize socket routes with server reference
  initializeSocketRoutes(socketServer);

  // Set up real-time metrics broadcasting
  setupMetricsBroadcasting(socketServer);

  // Mount socket routes
  app.use('/api/v1/socket', socketRoutes);

  // Now mount 404 handler after all routes are registered
  app.use((req, res) => {
    logger.warn('Route Not Found', {
      type: 'route_not_found',
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString(),
    });

    res.status(404).json({
      error: 'Route not found',
      message: `The requested route ${req.originalUrl} does not exist`,
    });
  });

  // Error handling middleware (must be last)
  app.use(errorLogger);

  console.log('✅ Socket.io initialized successfully');

  console.log('🚀 HLenergy API Server Started');
  console.log('================================');
  console.log(`📡 Server: http://localhost:${PORT}`);
  console.log(`🔌 Socket.io: ws://localhost:${PORT}`);
  console.log(`📊 Health: http://localhost:${PORT}/health`);
  console.log(`🧪 Test: http://localhost:${PORT}/api/test`);
  console.log(`📚 Swagger: http://localhost:${PORT}/api-docs`);
  console.log(`📖 Redoc: http://localhost:${PORT}/redoc`);
  console.log('');
  console.log('📋 Version Information:');
  console.log(`   Server Version: ${versionSummary.server_version}`);
  console.log(`   API Version: ${versionSummary.api_version}`);
  console.log(`   Git Branch: ${versionSummary.git_branch}`);
  console.log(`   Git Commit: ${versionSummary.git_commit}`);
  console.log(`   Environment: ${versionSummary.environment}`);
  console.log(`   Node.js: ${process.version}`);
  console.log('');
  console.log('🔗 Version Endpoints:');
  console.log(`   Full Info: http://localhost:${PORT}/version`);
  console.log(`   Summary: http://localhost:${PORT}/version/summary`);
  console.log(`   API Versions: http://localhost:${PORT}/api/versions`);
  console.log(`   Dependencies: http://localhost:${PORT}/dependencies`);
  console.log(`   Database Stats: http://localhost:${PORT}/database/stats`);
  console.log(`   Logs: http://localhost:${PORT}/logs`);
  console.log('');
  console.log('🛠️  Development Commands:');
  console.log('   npm run version:status    # Check version status');
  console.log('   npm run deps:check        # Check dependencies');
  console.log('   npm run db:status         # Check database status');
  console.log('   npm run db:setup          # Setup database');
  console.log('');
  console.log('📝 Smart Logging System:');
  console.log('   Database-first logging    # Primary: Database, Fallback: Files');
  console.log('   Request logging           # All HTTP requests with context');
  console.log('   Security monitoring       # Suspicious activity detection');
  console.log('   Authentication tracking   # Login/registration events');
  console.log('   Error logging             # Application errors with stack traces');
  console.log('   Performance monitoring    # Slow request detection');
  console.log('   Automatic fallback        # Files used when DB unavailable');
  console.log('   Log status: /logs/status  # Check logging system health');
  console.log('');

  // Email worker has been moved to standalone service
  console.log('📧 Email processing handled by standalone worker');
  console.log('   Location: backend/email-worker/');
  console.log('   Start with: cd backend/email-worker && npm start');

  console.log('');
  console.log('🔌 Real-time Features:');
  console.log('   Analytics tracking        # Real-time page views and events');
  console.log('   Customer communications   # Live chat and notifications');
  console.log('   Project updates          # Real-time status changes');
  console.log('   Energy monitoring        # Live consumption data');
  console.log('   System notifications     # Instant alerts and messages');
  console.log('   User presence            # Online/offline status');
  console.log('   Typing indicators        # Live typing status');
  console.log('');

  console.log('================================');
});

// Graceful shutdown handling
const gracefulShutdown = (signal) => {
  console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);

  // Email worker is now standalone - no need to stop here
  console.log('📧 Standalone email worker continues running independently');

  // Stop metrics broadcasting
  if (metricsInterval) {
    console.log('📊 Stopping metrics broadcasting...');
    clearInterval(metricsInterval);
    metricsInterval = null;
  }

  // Stop session monitoring
  const { stopSessionMonitoring } = require('./models');
  stopSessionMonitoring();

  // Close socket server
  if (socketServer && socketServer.io) {
    console.log('🔌 Closing Socket.io connections...');
    socketServer.io.close();
  }

  // Close HTTP server
  if (server) {
    console.log('🌐 Closing HTTP server...');
    server.close((err) => {
      if (err) {
        console.error('❌ Error during server shutdown:', err);
        process.exit(1);
      }

      console.log('✅ Server shut down gracefully');
      process.exit(0);
    });

    // Force shutdown after 10 seconds
    setTimeout(() => {
      console.log('⚠️  Forcing shutdown after timeout');
      process.exit(1);
    }, 10000);
  } else {
    process.exit(0);
  }
};

// Handle different termination signals
process.on('SIGINT', () => gracefulShutdown('SIGINT'));   // Ctrl+C
process.on('SIGTERM', () => gracefulShutdown('SIGTERM')); // Termination request
process.on('SIGQUIT', () => gracefulShutdown('SIGQUIT')); // Quit request

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  gracefulShutdown('UNCAUGHT_EXCEPTION');
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  gracefulShutdown('UNHANDLED_REJECTION');
});

module.exports = app;

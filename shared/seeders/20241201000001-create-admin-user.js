'use strict';

const bcrypt = require('bcryptjs');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Hash the default admin password
    const hashedPassword = await bcrypt.hash(process.env.ADMIN_PASSWORD || 'admin123456', 12);
    
    await queryInterface.bulkInsert('users', [
      {
        name: 'HLenergy Admin',
        email: process.env.ADMIN_EMAIL || '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
        email_verified: true,
        is_active: true,
        profile: JSON.stringify({
          department: 'Administration',
          title: 'System Administrator',
          permissions: ['all'],
        }),
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        name: 'HLenergy Staff',
        email: process.env.STAFF_EMAIL || '<EMAIL>',
        password: await bcrypt.hash(process.env.STAFF_PASSWORD || 'staff123456', 12),
        role: 'staff',
        email_verified: true,
        is_active: true,
        profile: JSON.stringify({
          department: 'Customer Service',
          title: 'Customer Service Representative',
          permissions: ['contacts:read', 'contacts:update'],
        }),
        created_at: new Date(),
        updated_at: new Date(),
      },
    ], {});

    // Create some sample contact submissions
    await queryInterface.bulkInsert('contact_submissions', [
      {
        name: 'John Smith',
        email: '<EMAIL>',
        phone: '+1234567890',
        message: 'I am interested in your solar panel installation services. Could you please provide more information about pricing and installation timeline?',
        status: 'new',
        priority: 'medium',
        source: 'website',
        ip_address: '*************',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        metadata: JSON.stringify({
          page_url: '/services/solar',
          referrer: 'https://google.com',
          utm_source: 'google',
          utm_medium: 'organic',
        }),
        created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      },
      {
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '+1987654321',
        message: 'We are a commercial business looking for energy efficiency consulting. Our current energy costs are very high and we need professional advice.',
        status: 'in_progress',
        priority: 'high',
        source: 'website',
        ip_address: '*************',
        user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        assigned_to: 2, // Assigned to staff user
        metadata: JSON.stringify({
          page_url: '/services/commercial',
          referrer: 'https://linkedin.com',
          utm_source: 'linkedin',
          utm_medium: 'social',
          company_size: 'medium',
        }),
        created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
        updated_at: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
      },
      {
        name: 'Michael Brown',
        email: '<EMAIL>',
        message: 'Thank you for the excellent service! The energy audit was very thorough and the recommendations have already started saving us money.',
        status: 'resolved',
        priority: 'low',
        source: 'email',
        resolved_at: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
        metadata: JSON.stringify({
          follow_up: true,
          satisfaction_rating: 5,
          service_type: 'energy_audit',
        }),
        created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
        updated_at: new Date(Date.now() - 6 * 60 * 60 * 1000),
      },
    ], {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('contact_submissions', null, {});
    await queryInterface.bulkDelete('users', null, {});
  }
};

/**
 * Enhanced Push Notification Service with Database Persistence
 * Replaces in-memory storage with MySQL database storage
 */

const webpush = require('web-push');
const crypto = require('crypto');
const { logAuth } = require('../config/logger');

// VAPID keys from environment variables
const VAPID_PUBLIC_KEY = process.env.VAPID_PUBLIC_KEY;
const VAPID_PRIVATE_KEY = process.env.VAPID_PRIVATE_KEY;
const VAPID_SUBJECT = process.env.VAPID_SUBJECT || 'mailto:<EMAIL>';

// Validate VAPID keys
if (!VAPID_PUBLIC_KEY || !VAPID_PRIVATE_KEY) {
  console.error('❌ VAPID keys not found in environment variables!');
  console.error('Please set VAPID_PUBLIC_KEY and VAPID_PRIVATE_KEY in .env file');
  process.exit(1);
}

console.log('🔑 VAPID Configuration (DB Version):');
console.log('  Public Key:', VAPID_PUBLIC_KEY.substring(0, 20) + '...');
console.log('  Private Key:', VAPID_PRIVATE_KEY.substring(0, 10) + '...');
console.log('  Subject:', VAPID_SUBJECT);

// Configure web-push
webpush.setVapidDetails(
  VAPID_SUBJECT,
  VAPID_PUBLIC_KEY,
  VAPID_PRIVATE_KEY
);

class PushNotificationServiceDB {
  /**
   * Initialize the service with database models
   */
  static initialize(models) {
    this.PushSubscription = models.PushSubscription;
    this.User = models.User;
    console.log('✅ Push Notification Service initialized with database storage');
  }

  /**
   * Generate SHA-256 hash for endpoint (for faster lookups)
   */
  static generateEndpointHash(endpoint) {
    return crypto.createHash('sha256').update(endpoint).digest('hex');
  }

  /**
   * Add a push subscription to database
   */
  static async addSubscription(userId, subscription, deviceInfo = {}, userRole = 'user') {
    try {
      const endpointHash = this.generateEndpointHash(subscription.endpoint);

      // Check if subscription already exists (using hash for faster lookup)
      const existingSubscription = await this.PushSubscription.findOne({
        where: { endpointHash }
      });

      if (existingSubscription) {
        // Update existing subscription
        await existingSubscription.update({
          userId,
          endpoint: subscription.endpoint, // Update endpoint in case it changed
          userRole,
          deviceInfo,
          isActive: true,
          lastUsed: new Date(),
          failureCount: 0
        });

        logAuth('push_subscription_updated', userId, {
          subscriptionId: existingSubscription.id,
          endpoint: subscription.endpoint,
          deviceInfo,
          userRole,
        });

        return existingSubscription.id;
      }

      // Create new subscription
      const newSubscription = await this.PushSubscription.create({
        userId,
        endpoint: subscription.endpoint,
        endpointHash,
        p256dhKey: subscription.keys.p256dh,
        authKey: subscription.keys.auth,
        userRole,
        deviceInfo: {
          userAgent: deviceInfo.userAgent || 'unknown',
          platform: deviceInfo.platform || 'unknown',
          deviceType: deviceInfo.deviceType || 'web',
          ip: deviceInfo.ip || 'unknown',
        },
        isActive: true,
        lastUsed: new Date()
      });

      logAuth('push_subscription_added', userId, {
        subscriptionId: newSubscription.id,
        endpoint: subscription.endpoint,
        deviceInfo: newSubscription.deviceInfo,
        userRole,
      });

      return newSubscription.id;
    } catch (error) {
      console.error('Error adding push subscription:', error);
      throw error;
    }
  }

  /**
   * Remove a push subscription from database
   */
  static async removeSubscription(subscriptionId, userId = null) {
    try {
      const whereClause = { id: subscriptionId };
      if (userId) {
        whereClause.userId = userId;
      }

      const subscription = await this.PushSubscription.findOne({ where: whereClause });
      
      if (subscription) {
        await subscription.destroy();
        
        logAuth('push_subscription_removed', subscription.userId, {
          subscriptionId,
          endpoint: subscription.endpoint,
        });
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Error removing push subscription:', error);
      throw error;
    }
  }

  /**
   * Remove all subscriptions for a user
   */
  static async removeAllUserSubscriptions(userId) {
    try {
      const removedCount = await this.PushSubscription.destroy({
        where: { userId }
      });
      
      logAuth('push_subscriptions_bulk_removed', userId, { removedCount });
      return removedCount;
    } catch (error) {
      console.error('Error removing user subscriptions:', error);
      throw error;
    }
  }

  /**
   * Get all subscriptions for a user
   */
  static async getUserSubscriptions(userId) {
    try {
      const subscriptions = await this.PushSubscription.findAll({
        where: { userId },
        attributes: ['id', 'deviceInfo', 'createdAt', 'isActive', 'lastUsed'],
        order: [['createdAt', 'DESC']]
      });
      
      return subscriptions.map(sub => ({
        id: sub.id,
        deviceInfo: sub.deviceInfo,
        createdAt: sub.createdAt,
        isActive: sub.isActive,
        lastUsed: sub.lastUsed
      }));
    } catch (error) {
      console.error('Error getting user subscriptions:', error);
      throw error;
    }
  }

  /**
   * Send push notification to specific user
   */
  static async sendToUser(userId, payload) {
    try {
      const userSubscriptions = await this.PushSubscription.findAll({
        where: { 
          userId: String(userId),
          isActive: true 
        }
      });

      console.log(`🔔 [DB] Found ${userSubscriptions.length} active subscriptions for user ${userId}`);

      if (userSubscriptions.length === 0) {
        console.log(`No active subscriptions found for user ${userId}`);
        return { sent: 0, failed: 0 };
      }

      const results = await Promise.allSettled(
        userSubscriptions.map(subscription => this.sendNotification(subscription, payload))
      );

      let sent = 0;
      let failed = 0;

      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          sent++;
        } else {
          failed++;
          console.error(`Failed to send notification to subscription ${userSubscriptions[index].id}:`, result.reason);
        }
      });

      logAuth('push_notification_sent', userId, {
        payload: payload.title || 'Notification',
        sent,
        failed,
        totalSubscriptions: userSubscriptions.length
      });

      return { sent, failed };
    } catch (error) {
      console.error('Error sending notification to user:', error);
      throw error;
    }
  }

  /**
   * Send notification to a specific subscription
   */
  static async sendNotification(subscription, payload) {
    try {
      // Convert Sequelize model to plain object if needed
      const subscriptionData = subscription.toJSON ? subscription.toJSON() : subscription;

      console.log('🔍 [DEBUG] Subscription data:', {
        id: subscriptionData.id,
        hasEndpoint: !!subscriptionData.endpoint,
        hasAuthKey: !!subscriptionData.authKey,
        hasP256dhKey: !!subscriptionData.p256dhKey,
        authKeyLength: subscriptionData.authKey?.length,
        p256dhKeyLength: subscriptionData.p256dhKey?.length
      });

      // Transform database subscription object to web-push format
      const webPushSubscription = {
        endpoint: subscriptionData.endpoint,
        keys: {
          auth: subscriptionData.authKey,
          p256dh: subscriptionData.p256dhKey
        }
      };

      console.log('🔍 [DEBUG] Web-push subscription:', {
        hasEndpoint: !!webPushSubscription.endpoint,
        hasAuth: !!webPushSubscription.keys.auth,
        hasP256dh: !!webPushSubscription.keys.p256dh
      });

      const options = {
        TTL: 60 * 60 * 24, // 24 hours
        urgency: 'normal',
        headers: {
          'Topic': 'hlenergy-notifications'
        }
      };

      const result = await webpush.sendNotification(
        webPushSubscription,
        JSON.stringify(payload),
        options
      );

      // Update last used timestamp
      await subscription.update({ lastUsed: new Date() });

      return result;
    } catch (error) {
      console.error('Error sending push notification:', error);

      // Handle subscription errors (expired, invalid, etc.)
      if (error.statusCode === 410 || error.statusCode === 404) {
        console.log(`🗑️ Subscription expired/invalid, marking as inactive: ${subscription.id}`);
        await subscription.update({
          isActive: false,
          failureCount: subscription.failureCount + 1
        });
      } else if (error.statusCode >= 400 && error.statusCode < 500) {
        // Client error - increment failure count
        await subscription.update({
          failureCount: subscription.failureCount + 1
        });

        // Deactivate after too many failures
        if (subscription.failureCount >= 5) {
          console.log(`🚫 Too many failures, deactivating subscription: ${subscription.id}`);
          await subscription.update({ isActive: false });
        }
      }

      throw error;
    }
  }

  /**
   * Get service statistics from database
   */
  static async getStats() {
    try {
      const totalSubscriptions = await this.PushSubscription.count();
      const activeSubscriptions = await this.PushSubscription.count({
        where: { isActive: true }
      });

      const userCount = await this.PushSubscription.count({
        distinct: true,
        col: 'userId',
        where: { isActive: true }
      });

      // Get device type distribution
      const deviceTypes = await this.PushSubscription.findAll({
        attributes: [
          [this.PushSubscription.sequelize.json('deviceInfo.deviceType'), 'deviceType'],
          [this.PushSubscription.sequelize.fn('COUNT', '*'), 'count']
        ],
        where: { isActive: true },
        group: [this.PushSubscription.sequelize.json('deviceInfo.deviceType')],
        raw: true
      });

      // Get platform distribution
      const platforms = await this.PushSubscription.findAll({
        attributes: [
          [this.PushSubscription.sequelize.json('deviceInfo.platform'), 'platform'],
          [this.PushSubscription.sequelize.fn('COUNT', '*'), 'count']
        ],
        where: { isActive: true },
        group: [this.PushSubscription.sequelize.json('deviceInfo.platform')],
        raw: true
      });

      return {
        totalSubscriptions,
        activeSubscriptions,
        userCount,
        deviceTypes: deviceTypes.reduce((acc, item) => {
          acc[item.deviceType || 'unknown'] = parseInt(item.count);
          return acc;
        }, {}),
        platforms: platforms.reduce((acc, item) => {
          acc[item.platform || 'unknown'] = parseInt(item.count);
          return acc;
        }, {})
      };
    } catch (error) {
      console.error('Error getting push stats:', error);
      throw error;
    }
  }

  /**
   * Send energy alert notification to a specific user
   */
  static async sendEnergyAlert(userId, alertData) {
    try {
      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

      const payload = {
        title: '⚡ Energy Alert',
        body: alertData.message || 'You have a new energy alert',
        icon: `${baseUrl}/hl-energy-logo-192w.png`,
        badge: `${baseUrl}/hl-energy-logo-96w.png`,
        tag: 'energy-alert',
        data: {
          url: '/dashboard',
          type: 'energy-alert',
          alertId: alertData.id,
          timestamp: Date.now()
        },
        requireInteraction: true,
        actions: [
          {
            action: 'view',
            title: 'View Details',
            icon: `${baseUrl}/hl-energy-logo-96w.png`
          },
          {
            action: 'dismiss',
            title: 'Dismiss'
          }
        ]
      };

      return await this.sendToUser(userId, payload);
    } catch (error) {
      console.error('Error sending energy alert:', error);
      throw error;
    }
  }

  /**
   * Send new lead notification to a specific user
   */
  static async sendNewLeadNotification(userId, leadData) {
    try {
      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

      const payload = {
        title: '🎯 New Lead',
        body: `New lead: ${leadData.name} - ${leadData.service}`,
        icon: `${baseUrl}/hl-energy-logo-192w.png`,
        badge: `${baseUrl}/hl-energy-logo-96w.png`,
        tag: 'new-lead',
        data: {
          url: '/admin/leads',
          type: 'new-lead',
          leadId: leadData.id,
          timestamp: Date.now()
        },
        requireInteraction: true,
        actions: [
          {
            action: 'view',
            title: 'View Lead',
            icon: `${baseUrl}/hl-energy-logo-96w.png`
          },
          {
            action: 'dismiss',
            title: 'Dismiss'
          }
        ]
      };

      return await this.sendToUser(userId, payload);
    } catch (error) {
      console.error('Error sending new lead notification:', error);
      throw error;
    }
  }

  /**
   * Send notification to all admin users
   */
  static async sendToAdmins(payload) {
    try {
      const adminSubscriptions = await this.PushSubscription.findAll({
        where: {
          userRole: 'admin',
          isActive: true
        }
      });

      if (adminSubscriptions.length === 0) {
        console.log('📭 No active admin subscriptions found');
        return { sent: 0, failed: 0 };
      }

      const results = await Promise.allSettled(
        adminSubscriptions.map(subscription => this.sendNotification(subscription, payload))
      );

      const sent = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      console.log(`📤 Admin notification sent: ${sent} successful, ${failed} failed`);
      return { sent, failed };
    } catch (error) {
      console.error('Error sending admin notifications:', error);
      throw error;
    }
  }

  /**
   * Send system announcement to all active users
   */
  static async sendSystemAnnouncement(payload) {
    try {
      const allSubscriptions = await this.PushSubscription.findAll({
        where: { isActive: true }
      });

      if (allSubscriptions.length === 0) {
        console.log('📭 No active subscriptions found for system announcement');
        return { sent: 0, failed: 0 };
      }

      const results = await Promise.allSettled(
        allSubscriptions.map(subscription => this.sendNotification(subscription, payload))
      );

      const sent = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      console.log(`📢 System announcement sent: ${sent} successful, ${failed} failed`);
      return { sent, failed };
    } catch (error) {
      console.error('Error sending system announcement:', error);
      throw error;
    }
  }

  /**
   * Send test notification to a specific user
   */
  static async sendTestNotification(userId, customMessage = null) {
    try {
      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const message = customMessage || 'Test notification from HLenergy';

      const payload = {
        title: '🔔 Test Notification',
        body: message,
        icon: `${baseUrl}/hl-energy-logo-192w.png`,
        badge: `${baseUrl}/hl-energy-logo-96w.png`,
        tag: 'test-notification',
        data: {
          url: '/dashboard',
          type: 'test',
          timestamp: Date.now()
        },
        requireInteraction: false,
        actions: [
          {
            action: 'view',
            title: 'View Dashboard',
            icon: `${baseUrl}/hl-energy-logo-96w.png`
          },
          {
            action: 'dismiss',
            title: 'Dismiss'
          }
        ]
      };

      const result = await this.sendToUser(userId, payload);

      console.log(`📤 Test notification sent to user ${userId}:`, {
        sent: result.sent,
        failed: result.failed,
        message: message
      });

      return result;
    } catch (error) {
      console.error('Error sending test notification:', error);
      throw error;
    }
  }

  /**
   * Clean up inactive subscriptions
   */
  static async cleanupInactiveSubscriptions(daysOld = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const removedCount = await this.PushSubscription.destroy({
        where: {
          isActive: false,
          updatedAt: {
            [this.PushSubscription.sequelize.Op.lt]: cutoffDate
          }
        }
      });

      console.log(`🧹 Cleaned up ${removedCount} inactive push subscriptions older than ${daysOld} days`);
      return removedCount;
    } catch (error) {
      console.error('Error cleaning up subscriptions:', error);
      throw error;
    }
  }
}

module.exports = PushNotificationServiceDB;

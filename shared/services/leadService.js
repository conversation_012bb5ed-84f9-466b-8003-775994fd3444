const Lead = require('../models/Lead');
const LeadActivity = require('../models/LeadActivity');
const ContactSubmission = require('../models/ContactSubmission');
const { Op } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Lead Service
 * Business logic for lead management
 */

class LeadService {
  /**
   * Create a new lead
   */
  async createLead(leadData, userId = null) {
    try {
      console.log('🔍 createLead called with data:', JSON.stringify(leadData, null, 2));

      // Set created_by if userId provided
      if (userId) {
        leadData.created_by = userId;
      }

      // Set first_contact_date if not provided
      if (!leadData.first_contact_date) {
        leadData.first_contact_date = new Date();
      }

      // Calculate initial lead score based on provided data
      console.log('📊 Calculating lead score...');
      try {
        leadData.lead_score = await this.calculateLeadScore(leadData);
        console.log('📊 Lead score calculated:', leadData.lead_score);
      } catch (scoreError) {
        console.warn('⚠️ Lead score calculation failed, using default:', scoreError.message);
        leadData.lead_score = 50; // Use default if calculation fails
      }

      console.log('💾 Creating lead in database...');
      const lead = await Lead.create(leadData);
      console.log('✅ Lead created with ID:', lead.id);
      
      // Create initial activity
      await LeadActivity.create({
        lead_id: lead.id,
        activity_type: 'note',
        subject: 'Lead Created',
        description: `Lead created from ${leadData.lead_source}`,
        created_by: userId,
        completed_at: new Date(),
      });
      
      return lead;
    } catch (error) {
      console.error('❌ Failed to create lead:', error);
      console.error('❌ Error name:', error.name);
      console.error('❌ Error message:', error.message);

      // If it's a Sequelize validation error, provide detailed info
      if (error.name === 'SequelizeValidationError') {
        console.error('❌ Validation errors:', error.errors);
        const validationErrors = error.errors.map(err => `${err.path}: ${err.message}`).join(', ');
        throw new Error(`Lead validation failed: ${validationErrors}`);
      }

      throw new Error(`Failed to create lead: ${error.message}`);
    }
  }
  
  /**
   * Create lead from contact submission
   */
  async createLeadFromContactSubmission(contactSubmissionId, additionalData = {}) {
    try {
      console.log('🔍 Starting createLeadFromContactSubmission for ID:', contactSubmissionId);

      // Test database connection
      await sequelize.authenticate();
      console.log('✅ Database connection verified');

      const contactSubmission = await ContactSubmission.findByPk(contactSubmissionId);
      if (!contactSubmission) {
        throw new Error('Contact submission not found');
      }

      console.log('✅ Contact submission found:', {
        id: contactSubmission.id,
        name: contactSubmission.name,
        email: contactSubmission.email,
        phone: contactSubmission.phone
      });
      
      // Parse name - handle cases where only one name is provided
      const nameParts = contactSubmission.name.trim().split(' ');
      const firstName = nameParts[0] || 'Unknown';
      const lastName = nameParts.slice(1).join(' ') || 'Contact'; // Default to 'Contact' if no last name

      const leadData = {
        first_name: firstName,
        last_name: lastName,
        email: contactSubmission.email,
        phone: contactSubmission.phone && contactSubmission.phone.length >= 10 ? contactSubmission.phone : null, // Only include phone if it meets validation
        lead_source: 'website',
        lead_score: 50, // Default lead score
        status: 'new', // Default status
        priority: 'medium', // Default priority
        country: 'Portugal', // Default country
        contact_submission_id: contactSubmissionId,
        notes: `Initial contact message: ${contactSubmission.message}`,
        first_contact_date: contactSubmission.created_at,
        next_follow_up_date: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
        ip_address: contactSubmission.ip_address,
        user_agent: contactSubmission.user_agent,
        metadata: {
          original_message: contactSubmission.message,
          contact_submission_date: contactSubmission.created_at,
          ...additionalData.metadata
        },
        ...additionalData
      };
      
      console.log('🔍 Creating lead with data:', JSON.stringify(leadData, null, 2));

      // Check if a lead with this email already exists
      const existingLead = await Lead.findOne({ where: { email: contactSubmission.email } });
      if (existingLead) {
        throw new Error(`A lead with email ${contactSubmission.email} already exists (ID: ${existingLead.id})`);
      }

      const lead = await this.createLead(leadData);

      console.log('✅ Lead created successfully:', lead.id);

      // Update contact submission to link to lead
      await contactSubmission.update({
        metadata: {
          ...contactSubmission.metadata,
          lead_id: lead.id,
          converted_to_lead: true,
          conversion_date: new Date().toISOString()
        }
      });

      return lead;
    } catch (error) {
      console.error('❌ Failed to create lead from contact submission:', error);
      console.error('❌ Error details:', error.message);
      console.error('❌ Error stack:', error.stack);

      // If it's a Sequelize validation error, provide more details
      if (error.name === 'SequelizeValidationError') {
        const validationErrors = error.errors.map(err => `${err.path}: ${err.message}`).join(', ');
        throw new Error(`Validation failed: ${validationErrors}`);
      }

      throw new Error(`Failed to create lead from contact submission: ${error.message}`);
    }
  }
  
  /**
   * Get leads with filtering and pagination
   */
  async getLeads(options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        status,
        priority,
        lead_source,
        assigned_to,
        search,
        sortBy = 'created_at',
        sortOrder = 'DESC',
        include_activities = false
      } = options;
      
      const where = {};
      const include = [
        {
          model: require('../models/User'),
          as: 'leadAssignedUser',
          attributes: ['id', 'name', 'email'],
          required: false,
        },
        {
          model: require('../models/User'),
          as: 'leadCreatedByUser',
          attributes: ['id', 'name', 'email'],
          required: false,
        },
        {
          model: ContactSubmission,
          as: 'contactSubmission',
          attributes: ['id', 'message', 'created_at'],
          required: false,
        }
      ];
      
      // Add filters
      if (status) {
        where.status = Array.isArray(status) ? { [Op.in]: status } : status;
      }
      
      if (priority) {
        where.priority = Array.isArray(priority) ? { [Op.in]: priority } : priority;
      }
      
      if (lead_source) {
        where.lead_source = Array.isArray(lead_source) ? { [Op.in]: lead_source } : lead_source;
      }
      
      if (assigned_to) {
        where.assigned_to = assigned_to;
      }
      
      // Search functionality
      if (search) {
        where[Op.or] = [
          { first_name: { [Op.iLike]: `%${search}%` } },
          { last_name: { [Op.iLike]: `%${search}%` } },
          { email: { [Op.iLike]: `%${search}%` } },
          { company_name: { [Op.iLike]: `%${search}%` } },
          { phone: { [Op.iLike]: `%${search}%` } },
        ];
      }
      
      // Include activities if requested
      if (include_activities) {
        include.push({
          model: LeadActivity,
          as: 'activities',
          limit: 5,
          order: [['created_at', 'DESC']],
          include: [
            {
              model: require('../models/User'),
              as: 'activityCreatedByUser',
              attributes: ['id', 'name', 'email'],
              required: false,
            }
          ]
        });
      }
      
      const offset = (page - 1) * limit;
      
      const { count, rows } = await Lead.findAndCountAll({
        where,
        include,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [[sortBy, sortOrder.toUpperCase()]],
        distinct: true,
      });
      
      return {
        leads: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit),
        },
      };
    } catch (error) {
      throw new Error(`Failed to get leads: ${error.message}`);
    }
  }
  
  /**
   * Get lead by ID
   */
  async getLeadById(id, includeActivities = true) {
    try {
      const include = [
        {
          model: require('../models/User'),
          as: 'leadAssignedUser',
          attributes: ['id', 'name', 'email'],
          required: false,
        },
        {
          model: require('../models/User'),
          as: 'leadCreatedByUser',
          attributes: ['id', 'name', 'email'],
          required: false,
        },
        {
          model: ContactSubmission,
          as: 'contactSubmission',
          attributes: ['id', 'name', 'email', 'phone', 'message', 'created_at'],
          required: false,
        }
      ];
      
      if (includeActivities) {
        include.push({
          model: LeadActivity,
          as: 'activities',
          order: [['created_at', 'DESC']],
          include: [
            {
              model: require('../models/User'),
              as: 'activityCreatedByUser',
              attributes: ['id', 'name', 'email'],
              required: false,
            },
            {
              model: require('../models/User'),
              as: 'activityAssignedUser',
              attributes: ['id', 'name', 'email'],
              required: false,
            }
          ]
        });
      }
      
      const lead = await Lead.findByPk(id, { include });
      
      if (!lead) {
        throw new Error('Lead not found');
      }
      
      return lead;
    } catch (error) {
      throw new Error(`Failed to get lead: ${error.message}`);
    }
  }
  
  /**
   * Update lead
   */
  async updateLead(id, updateData, userId = null) {
    try {
      const lead = await Lead.findByPk(id);
      if (!lead) {
        throw new Error('Lead not found');
      }
      
      // Track status changes
      if (updateData.status && updateData.status !== lead.status) {
        await LeadActivity.create({
          lead_id: id,
          activity_type: 'note',
          subject: 'Status Changed',
          description: `Status changed from ${lead.status} to ${updateData.status}`,
          created_by: userId,
          completed_at: new Date(),
        });
        
        // Set qualified_date if status changed to qualified
        if (updateData.status === 'qualified' && !lead.qualified_date) {
          updateData.qualified_date = new Date();
        }
        
        // Set converted_date if status changed to won
        if (updateData.status === 'won' && !lead.converted_date) {
          updateData.converted_date = new Date();
        }
      }
      
      // Update last_contact_date if certain fields are updated
      const contactFields = ['status', 'notes', 'next_follow_up_date'];
      if (contactFields.some(field => updateData.hasOwnProperty(field))) {
        updateData.last_contact_date = new Date();
      }
      
      await lead.update(updateData);
      
      return await this.getLeadById(id);
    } catch (error) {
      throw new Error(`Failed to update lead: ${error.message}`);
    }
  }
  
  /**
   * Delete lead
   */
  async deleteLead(id) {
    try {
      const lead = await Lead.findByPk(id);
      if (!lead) {
        throw new Error('Lead not found');
      }
      
      await lead.destroy();
      return { message: 'Lead deleted successfully' };
    } catch (error) {
      throw new Error(`Failed to delete lead: ${error.message}`);
    }
  }
  
  /**
   * Calculate lead score based on lead data
   */
  async calculateLeadScore(leadData) {
    let score = 50; // Base score
    
    // Industry scoring
    const highValueIndustries = ['Manufacturing', 'Energy', 'Technology', 'Healthcare'];
    if (leadData.industry && highValueIndustries.includes(leadData.industry)) {
      score += 15;
    }
    
    // Company size scoring
    if (leadData.company_size) {
      const sizeScores = {
        '1-10': 5,
        '11-50': 10,
        '51-200': 15,
        '201-1000': 20,
        '1000+': 25
      };
      score += sizeScores[leadData.company_size] || 0;
    }
    
    // Budget scoring
    if (leadData.monthly_energy_budget) {
      if (leadData.monthly_energy_budget >= 10000) score += 25;
      else if (leadData.monthly_energy_budget >= 5000) score += 20;
      else if (leadData.monthly_energy_budget >= 2000) score += 15;
      else if (leadData.monthly_energy_budget >= 1000) score += 10;
      else if (leadData.monthly_energy_budget >= 500) score += 5;
    }
    
    // Source scoring
    const sourceScores = {
      'referral': 20,
      'website': 10,
      'social_media': 5,
      'email_campaign': 8,
      'event': 15,
      'partner': 18
    };
    score += sourceScores[leadData.lead_source] || 0;
    
    // Contact submission scoring
    if (leadData.contact_submission_id) {
      score += 10;
    }
    
    // Ensure score is within bounds
    return Math.max(0, Math.min(100, score));
  }
  
  /**
   * Get lead statistics
   */
  async getLeadStats(startDate = null, endDate = null) {
    try {
      const where = {};
      
      if (startDate && endDate) {
        where.created_at = {
          [Op.between]: [startDate, endDate]
        };
      }
      
      const [statusCounts, sourceCounts, totalLeads, avgScore] = await Promise.all([
        Lead.findAll({
          attributes: [
            'status',
            [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
          ],
          where,
          group: ['status'],
          raw: true,
        }),
        Lead.findAll({
          attributes: [
            'lead_source',
            [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
          ],
          where,
          group: ['lead_source'],
          raw: true,
        }),
        Lead.count({ where }),
        Lead.findAll({
          attributes: [
            [sequelize.fn('AVG', sequelize.col('lead_score')), 'avg_score'],
          ],
          where,
          raw: true,
        }),
      ]);
      
      return {
        total_leads: totalLeads,
        average_score: avgScore[0]?.avg_score ? parseFloat(avgScore[0].avg_score) : 0,
        status_breakdown: statusCounts.reduce((acc, item) => {
          acc[item.status] = parseInt(item.count);
          return acc;
        }, {}),
        source_breakdown: sourceCounts.reduce((acc, item) => {
          acc[item.lead_source] = parseInt(item.count);
          return acc;
        }, {}),
      };
    } catch (error) {
      throw new Error(`Failed to get lead statistics: ${error.message}`);
    }
  }
}

module.exports = new LeadService();

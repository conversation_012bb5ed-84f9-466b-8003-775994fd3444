const { EmailQueue } = require('../models');
const { logger } = require('../config/logger');

/**
 * Email Queue Service
 * Handles queuing emails for background processing
 */

class EmailQueueService {
  constructor() {
    this.defaultFrom = process.env.SMTP_FROM || '<EMAIL>';
    this.defaultFromName = 'HLenergy';
  }

  /**
   * Queue an email for sending
   * @param {Object} emailData - Email data
   * @param {string} emailData.to_email - Recipient email
   * @param {string} emailData.to_name - Recipient name
   * @param {string} emailData.subject - Email subject
   * @param {string} emailData.html_content - HTML content
   * @param {string} emailData.text_content - Text content (optional)
   * @param {string} emailData.email_type - Type of email
   * @param {string} emailData.priority - Priority level
   * @param {Object} emailData.template_data - Template data
   * @param {Object} emailData.metadata - Additional metadata
   * @param {number} emailData.user_id - User ID (optional)
   * @param {Date} emailData.scheduled_at - Schedule time (optional)
   * @param {string} emailData.ip_address - IP address (optional)
   * @param {string} emailData.user_agent - User agent (optional)
   * @returns {Promise<Object>} Queued email record
   */
  async queueEmail(emailData) {
    try {
      const fromEmail = emailData.from_email || this.defaultFrom;

      const queuedEmail = await EmailQueue.create({
        user_id: emailData.user_id || null,
        to_email: emailData.to_email,
        to_name: emailData.to_name || null,
        from_email: fromEmail,
        from_name: emailData.from_name || this.defaultFromName,
        subject: emailData.subject,
        html_content: emailData.html_content,
        text_content: emailData.text_content || this.stripHtml(emailData.html_content),
        email_type: emailData.email_type,
        priority: emailData.priority || 'normal',
        scheduled_at: emailData.scheduled_at || null,
        template_data: emailData.template_data || null,
        metadata: emailData.metadata || null,
        ip_address: emailData.ip_address || null,
        user_agent: emailData.user_agent || null,
      });

      logger.info('Email queued successfully', {
        type: 'email_queue',
        emailId: queuedEmail.id,
        to: emailData.to_email,
        subject: emailData.subject,
        emailType: emailData.email_type,
        priority: emailData.priority,
      });

      return {
        success: true,
        emailId: queuedEmail.id,
        message: 'Email queued successfully',
      };

    } catch (error) {
      logger.error('Failed to queue email', {
        type: 'email_queue',
        error: error.message,
        emailData: {
          to: emailData.to_email,
          subject: emailData.subject,
          type: emailData.email_type,
        },
      });

      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Queue verification email
   */
  async queueVerificationEmail(user, token, metadata = {}) {
    const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/verify-email?token=${token}`;
    
    const html = this.generateVerificationEmailHTML(user, verificationUrl);

    return await this.queueEmail({
      user_id: user.id,
      to_email: user.email,
      to_name: user.name,
      subject: 'Verify Your Email Address - HLenergy',
      html_content: html,
      email_type: 'verification',
      priority: 'high',
      template_data: {
        user_name: user.name,
        verification_url: verificationUrl,
        token: token,
      },
      metadata: {
        ...metadata,
        verification_token: token,
      },
      ip_address: metadata.ip_address,
      user_agent: metadata.user_agent,
    });
  }

  /**
   * Queue password reset email
   */
  async queuePasswordResetEmail(user, token, metadata = {}) {
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password?token=${token}`;
    
    const html = this.generatePasswordResetEmailHTML(user, resetUrl);

    return await this.queueEmail({
      user_id: user.id,
      to_email: user.email,
      to_name: user.name,
      subject: 'Reset Your Password - HLenergy',
      html_content: html,
      email_type: 'password_reset',
      priority: 'high',
      template_data: {
        user_name: user.name,
        reset_url: resetUrl,
        token: token,
      },
      metadata: {
        ...metadata,
        reset_token: token,
      },
      ip_address: metadata.ip_address,
      user_agent: metadata.user_agent,
    });
  }

  /**
   * Queue welcome email
   */
  async queueWelcomeEmail(user, metadata = {}) {
    const html = this.generateWelcomeEmailHTML(user);

    return await this.queueEmail({
      user_id: user.id,
      to_email: user.email,
      to_name: user.name,
      subject: 'Welcome to HLenergy - Email Verified!',
      html_content: html,
      email_type: 'welcome',
      priority: 'normal',
      template_data: {
        user_name: user.name,
        dashboard_url: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/dashboard`,
      },
      metadata: metadata,
      ip_address: metadata.ip_address,
      user_agent: metadata.user_agent,
    });
  }

  /**
   * Queue notification email
   */
  async queueNotificationEmail(user, subject, content, metadata = {}) {
    return await this.queueEmail({
      user_id: user.id,
      to_email: user.email,
      to_name: user.name,
      subject: subject,
      html_content: content,
      email_type: 'notification',
      priority: metadata.priority || 'normal',
      template_data: metadata.template_data || null,
      metadata: metadata,
      ip_address: metadata.ip_address,
      user_agent: metadata.user_agent,
    });
  }

  /**
   * Queue contact form notification email to admin
   */
  async queueContactNotificationEmail(contactData) {
    const {
      submissionId,
      name,
      email,
      phone,
      message,
      source,
      adminEmail,
      ip_address,
      user_agent
    } = contactData;

    const html = this.generateContactNotificationEmailHTML({
      submissionId,
      name,
      email,
      phone,
      message,
      source,
      submittedAt: new Date().toISOString()
    });

    return await this.queueEmail({
      to_email: adminEmail,
      to_name: 'HLenergy Admin',
      subject: `Novo contacto submetido - ${name}`,
      html_content: html,
      email_type: 'notification',
      priority: 'high',
      template_data: {
        submission_id: submissionId,
        customer_name: name,
        customer_email: email,
        customer_phone: phone,
        message: message,
        source: source,
        submitted_at: new Date().toISOString(),
      },
      metadata: {
        submission_id: submissionId,
        contact_source: source,
        customer_email: email,
      },
      ip_address: ip_address,
      user_agent: user_agent,
    });
  }

  /**
   * Schedule an email for later sending
   */
  async scheduleEmail(emailData, sendAt) {
    return await this.queueEmail({
      ...emailData,
      scheduled_at: sendAt,
    });
  }

  /**
   * Cancel a queued email
   */
  async cancelEmail(emailId) {
    try {
      const email = await EmailQueue.findByPk(emailId);
      if (!email) {
        return { success: false, error: 'Email not found' };
      }

      if (email.status !== 'pending') {
        return { success: false, error: 'Email cannot be cancelled' };
      }

      email.status = 'cancelled';
      await email.save();

      logger.info('Email cancelled', {
        type: 'email_queue',
        emailId: emailId,
      });

      return { success: true, message: 'Email cancelled successfully' };

    } catch (error) {
      logger.error('Failed to cancel email', {
        type: 'email_queue',
        emailId: emailId,
        error: error.message,
      });

      return { success: false, error: error.message };
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats() {
    return await EmailQueue.getQueueStats();
  }

  /**
   * Clean up old emails
   */
  async cleanupOldEmails(daysOld = 30) {
    return await EmailQueue.cleanupOldEmails(daysOld);
  }

  // Email template generators
  generateVerificationEmailHTML(user, verificationUrl) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Verify Your Email - HLenergy</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; background: #f9fafb; }
          .button { display: inline-block; padding: 12px 24px; background: #2563eb; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Welcome to HLenergy!</h1>
          </div>
          <div class="content">
            <h2>Hi ${user.name},</h2>
            <p>Thank you for registering with HLenergy. To complete your registration, please verify your email address by clicking the button below:</p>
            <p style="text-align: center;">
              <a href="${verificationUrl}" class="button">Verify Email Address</a>
            </p>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #2563eb;">${verificationUrl}</p>
            <p><strong>This verification link will expire in 24 hours.</strong></p>
            <p>If you didn't create an account with HLenergy, please ignore this email.</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 HLenergy. All rights reserved.</p>
            <p>This is an automated message, please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  generatePasswordResetEmailHTML(user, resetUrl) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Reset Your Password - HLenergy</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #dc2626; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; background: #f9fafb; }
          .button { display: inline-block; padding: 12px 24px; background: #dc2626; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
          .warning { background: #fef2f2; border: 1px solid #fecaca; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Password Reset Request</h1>
          </div>
          <div class="content">
            <h2>Hi ${user.name},</h2>
            <p>We received a request to reset your password for your HLenergy account. If you made this request, click the button below to reset your password:</p>
            <p style="text-align: center;">
              <a href="${resetUrl}" class="button">Reset Password</a>
            </p>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #dc2626;">${resetUrl}</p>
            <div class="warning">
              <p><strong>⚠️ Important Security Information:</strong></p>
              <ul>
                <li>This password reset link will expire in 1 hour</li>
                <li>If you didn't request this reset, please ignore this email</li>
                <li>Your password will remain unchanged until you create a new one</li>
                <li>For security, this link can only be used once</li>
              </ul>
            </div>
            <p>If you continue to have problems, please contact our support team.</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 HLenergy. All rights reserved.</p>
            <p>This is an automated message, please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  generateWelcomeEmailHTML(user) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Welcome to HLenergy!</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #059669; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; background: #f9fafb; }
          .button { display: inline-block; padding: 12px 24px; background: #059669; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Welcome to HLenergy!</h1>
          </div>
          <div class="content">
            <h2>Hi ${user.name},</h2>
            <p>Your email has been verified successfully! Welcome to the HLenergy platform.</p>
            <p>You can now access all features of your account:</p>
            <ul>
              <li>Submit contact requests</li>
              <li>Manage your profile</li>
              <li>Track your submissions</li>
              <li>Access support resources</li>
            </ul>
            <p style="text-align: center;">
              <a href="${process.env.FRONTEND_URL || 'http://localhost:3000'}/dashboard" class="button">Go to Dashboard</a>
            </p>
            <p>If you have any questions, feel free to contact our support team.</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 HLenergy. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  generateContactNotificationEmailHTML(contactData) {
    const { submissionId, name, email, phone, message, source, submittedAt } = contactData;
    const formattedDate = new Date(submittedAt).toLocaleString('pt-PT', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Europe/Lisbon'
    });

    // Simple urgency detection
    const urgentKeywords = ['urgent', 'emergency', 'asap', 'urgente', 'emergência'];
    const isUrgent = urgentKeywords.some(keyword =>
      message.toLowerCase().includes(keyword)
    );

    return `
      <!DOCTYPE html>
      <html lang="pt-PT">
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Nova Submissão de Contacto - HLenergy</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
          }

          .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
          }

          .header {
            background: #02342b;
            color: white;
            padding: 32px 24px;
            text-align: center;
          }

          .header h1 {
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 600;
          }

          .header p {
            margin: 0;
            opacity: 0.9;
            font-size: 16px;
          }
          .priority-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            margin-top: 16px;
          }

          .priority-high {
            background: #fee2e2;
            color: #dc2626;
            border: 1px solid #fecaca;
          }

          .priority-normal {
            background: #dbeafe;
            color: #2563eb;
            border: 1px solid #bfdbfe;
          }

          .content {
            padding: 32px 24px;
          }

          .info-section {
            margin-bottom: 32px;
          }

          .info-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e5e7eb;
          }

          .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
          }

          .info-item {
            background: #f9fafb;
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid #02342b;
          }

          .info-label {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
          }

          .info-value {
            font-size: 16px;
            font-weight: 500;
            color: #1f2937;
          }

          .info-value a {
            color: #02342b;
            text-decoration: none;
          }

          .info-value a:hover {
            text-decoration: underline;
          }
          .message-section {
            background: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #eaaa34;
            margin-bottom: 32px;
          }

          .message-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
          }

          .message-content {
            font-size: 15px;
            line-height: 1.6;
            color: #374151;
            white-space: pre-wrap;
            word-wrap: break-word;
          }

          .action-section {
            text-align: center;
            padding: 24px;
            background: #f9fafb;
            border-radius: 8px;
            margin-bottom: 32px;
          }

          .action-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
          }

          .button {
            display: inline-block;
            padding: 12px 24px;
            background: #02342b;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            font-size: 16px;
            transition: background-color 0.2s ease;
          }

          .button:hover {
            background: #12816c;
          }
          .footer {
            background: #f3f4f6;
            padding: 24px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
            border-top: 1px solid #e5e7eb;
          }

          .footer p {
            margin: 4px 0;
          }

          .footer a {
            color: #02342b;
            text-decoration: none;
          }

          .footer a:hover {
            text-decoration: underline;
          }

          @media (max-width: 600px) {
            body {
              padding: 10px;
            }

            .container {
              border-radius: 8px;
            }

            .header {
              padding: 24px 20px;
            }

            .header h1 {
              font-size: 20px;
            }

            .content {
              padding: 24px 20px;
            }

            .info-grid {
              grid-template-columns: 1fr;
            }

            .action-section {
              padding: 20px;
            }

            .footer {
              padding: 20px;
            }
          }


          @media (max-width: 768px) {
            body {
              padding: 10px;
            }

            .container {
              margin: 0;
              border-radius: 16px;
              box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            }

            .header {
              padding: 32px 24px;
            }

            .header h1 {
              font-size: 24px;
            }

            .content {
              padding: 24px;
            }

            .info-grid {
              grid-template-columns: 1fr;
              gap: 16px;
            }

            .info-card {
              padding: 20px;
            }

            .message-box {
              padding: 20px;
              font-size: 15px;
            }

            .action-section {
              padding: 24px;
              margin: 32px 0;
            }

            .button {
              padding: 14px 24px;
              font-size: 15px;
            }

            .next-steps {
              padding: 20px;
            }

            .footer {
              padding: 24px;
            }

            .stats-row {
              flex-direction: column;
              gap: 8px;
              text-align: center;
            }
          }

          @media (max-width: 480px) {
            .header h1 {
              font-size: 22px;
            }

            .header .subtitle {
              font-size: 14px;
            }

            .priority-badge {
              padding: 10px 16px;
              font-size: 13px;
            }

            .service-detected .icon {
              font-size: 28px;
            }

            .service-detected .text {
              font-size: 16px;
            }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Nova Submissão de Contacto</h1>
            <p>HLenergy - Consultoria Energética</p>
            ${isUrgent ? `
            <div class="priority-badge priority-high">
              🚨 URGENTE
            </div>
            ` : ''}
          </div>

          <div class="content">
            <div class="info-section">
              <div class="info-title">Detalhes do Contacto</div>

              <div class="info-grid">
                <div class="info-item">
                  <div class="info-label">ID da Submissão</div>
                  <div class="info-value">#${submissionId}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">Data e Hora</div>
                  <div class="info-value">${formattedDate}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">Nome</div>
                  <div class="info-value">${name}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">Email</div>
                  <div class="info-value">
                    <a href="mailto:${email}">${email}</a>
                  </div>
                </div>
                ${phone ? `
                <div class="info-item">
                  <div class="info-label">Telefone</div>
                  <div class="info-value">
                    <a href="tel:${phone}">${phone}</a>
                  </div>
                </div>
                ` : ''}
                <div class="info-item">
                  <div class="info-label">Origem</div>
                  <div class="info-value">${source || 'Website'}</div>
                </div>
              </div>
            </div>

            <div class="message-section">
              <div class="message-title">Mensagem</div>
              <div class="message-content">${message}</div>
            </div>

            <div class="action-section">
              <div class="action-title">Gerir esta Submissão</div>
              <a href="${process.env.FRONTEND_URL || 'http://localhost:3000'}/dashboard" class="button">
                Ver no Dashboard
              </a>
            </div>

          </div>

          <div class="footer">
            <p><strong>HLenergy - Consultoria Energética</strong></p>
            <p>Esta é uma notificação automática do sistema de contactos.</p>
            <p>
              <a href="mailto:<EMAIL>"><EMAIL></a> |
              <a href="${process.env.FRONTEND_URL || 'http://localhost:3000'}/dashboard">Dashboard</a>
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  stripHtml(html) {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }
}

// Create singleton instance
const emailQueueService = new EmailQueueService();

module.exports = emailQueueService;

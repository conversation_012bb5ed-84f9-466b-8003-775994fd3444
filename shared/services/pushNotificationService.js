/**
 * Push Notification Service for HLenergy
 * Handles sending push notifications using web-push library
 */

const webpush = require('web-push');
const { logAuth } = require('../config/logger');

// VAPID keys from environment variables
const VAPID_PUBLIC_KEY = process.env.VAPID_PUBLIC_KEY;
const VAPID_PRIVATE_KEY = process.env.VAPID_PRIVATE_KEY;
const VAPID_SUBJECT = process.env.VAPID_SUBJECT || 'mailto:<EMAIL>';

// Validate VAPID keys
if (!VAPID_PUBLIC_KEY || !VAPID_PRIVATE_KEY) {
  console.error('❌ VAPID keys not found in environment variables!');
  console.error('Please set VAPID_PUBLIC_KEY and VAPID_PRIVATE_KEY in .env file');
  process.exit(1);
}

console.log('🔑 VAPID Configuration:');
console.log('  Public Key:', VAPID_PUBLIC_KEY.substring(0, 20) + '...');
console.log('  Private Key:', VAPID_PRIVATE_KEY.substring(0, 10) + '...');
console.log('  Subject:', VAPID_SUBJECT);

// Configure web-push
webpush.setVapidDetails(
  VAPID_SUBJECT,
  VAPID_PUBLIC_KEY,
  VAPID_PRIVATE_KEY
);

// In-memory storage for push subscriptions (in production, use database)
const pushSubscriptions = new Map();

class PushNotificationService {
  /**
   * Add a push subscription
   */
  static addSubscription(userId, subscription, deviceInfo = {}, userRole = 'user') {
    const subscriptionId = `sub_${userId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const subscriptionData = {
      id: subscriptionId,
      userId,
      userRole, // Store user role for targeting
      subscription,
      deviceInfo: {
        userAgent: deviceInfo.userAgent || 'unknown',
        platform: deviceInfo.platform || 'unknown',
        deviceType: deviceInfo.deviceType || 'web',
        ip: deviceInfo.ip || 'unknown',
      },
      createdAt: new Date().toISOString(),
      isActive: true,
    };

    pushSubscriptions.set(subscriptionId, subscriptionData);

    logAuth('push_subscription_added', userId, {
      subscriptionId,
      endpoint: subscription.endpoint,
      deviceInfo: subscriptionData.deviceInfo,
      userRole,
    });

    return subscriptionId;
  }

  /**
   * Remove a push subscription
   */
  static removeSubscription(subscriptionId, userId = null) {
    const subscription = pushSubscriptions.get(subscriptionId);
    
    if (subscription && (!userId || subscription.userId === userId)) {
      pushSubscriptions.delete(subscriptionId);
      
      logAuth('push_subscription_removed', subscription.userId, {
        subscriptionId,
        endpoint: subscription.subscription.endpoint,
      });
      
      return true;
    }
    
    return false;
  }

  /**
   * Remove all subscriptions for a user
   */
  static removeAllUserSubscriptions(userId) {
    let removedCount = 0;
    
    for (const [id, sub] of pushSubscriptions.entries()) {
      if (sub.userId === userId) {
        pushSubscriptions.delete(id);
        removedCount++;
      }
    }
    
    logAuth('push_subscriptions_bulk_removed', userId, { removedCount });
    return removedCount;
  }

  /**
   * Get all subscriptions for a user
   */
  static getUserSubscriptions(userId) {
    const userSubscriptions = [];
    
    for (const [id, sub] of pushSubscriptions.entries()) {
      if (sub.userId === userId) {
        userSubscriptions.push({
          id: sub.id,
          deviceInfo: sub.deviceInfo,
          createdAt: sub.createdAt,
          isActive: sub.isActive,
        });
      }
    }
    
    return userSubscriptions;
  }

  /**
   * Send push notification to specific user
   */
  static async sendToUser(userId, payload) {
    const userSubscriptions = [];

    // Convert userId to string for comparison (handles both string and number inputs)
    const targetUserId = String(userId);

    console.log(`🔔 [DEBUG] Looking for subscriptions for user ${targetUserId}`);
    console.log(`🔔 [DEBUG] Total subscriptions: ${pushSubscriptions.size}`);

    // Get all active subscriptions for the user
    for (const [id, sub] of pushSubscriptions.entries()) {
      console.log(`🔔 [DEBUG] Checking subscription: userId=${sub.userId} (type: ${typeof sub.userId}), active=${sub.isActive}`);

      if (String(sub.userId) === targetUserId && sub.isActive) {
        userSubscriptions.push(sub);
        console.log(`🔔 [DEBUG] Found matching subscription: ${id}`);
      }
    }

    console.log(`🔔 [DEBUG] Found ${userSubscriptions.length} active subscriptions for user ${targetUserId}`);

    if (userSubscriptions.length === 0) {
      console.log(`No active subscriptions found for user ${userId}`);
      return { sent: 0, failed: 0 };
    }

    const results = await Promise.allSettled(
      userSubscriptions.map(sub => this.sendNotification(sub.subscription, payload))
    );

    let sent = 0;
    let failed = 0;

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        sent++;
      } else {
        failed++;
        console.error(`Failed to send notification to subscription ${userSubscriptions[index].id}:`, result.reason);
        
        // If subscription is invalid, mark it as inactive
        if (result.reason?.statusCode === 410) {
          userSubscriptions[index].isActive = false;
        }
      }
    });

    logAuth('push_notification_sent', userId, {
      payload: payload.title || 'Notification',
      sent,
      failed,
      totalSubscriptions: userSubscriptions.length
    });

    return { sent, failed };
  }

  /**
   * Send push notification to all users
   */
  static async sendToAll(payload, options = {}) {
    const {
      userFilter = null,
      excludeUsers = [],
      includeRoles = [],
      excludeRoles = [],
      notificationType = 'general'
    } = options;

    const allSubscriptions = [];

    for (const [id, sub] of pushSubscriptions.entries()) {
      if (!sub.isActive) continue;

      // Apply legacy userFilter if provided
      if (userFilter && !userFilter(sub.userId)) continue;

      // Skip excluded users
      if (excludeUsers.includes(sub.userId)) continue;

      // Filter by roles if specified
      if (includeRoles.length > 0 && !includeRoles.includes(sub.userRole)) continue;
      if (excludeRoles.length > 0 && excludeRoles.includes(sub.userRole)) continue;

      allSubscriptions.push(sub);
    }

    if (allSubscriptions.length === 0) {
      console.log('No active subscriptions found matching criteria');
      return { sent: 0, failed: 0, criteria: options };
    }

    // Add notification metadata to payload
    const enhancedPayload = {
      ...payload,
      data: {
        ...payload.data,
        notificationType,
        targetType: 'broadcast',
        timestamp: Date.now()
      }
    };

    const results = await Promise.allSettled(
      allSubscriptions.map(sub => this.sendNotification(sub.subscription, enhancedPayload))
    );

    let sent = 0;
    let failed = 0;

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        sent++;
      } else {
        failed++;
        console.error(`Failed to send notification to subscription ${allSubscriptions[index].id}:`, result.reason);

        // If subscription is invalid, mark it as inactive
        if (result.reason?.statusCode === 410) {
          allSubscriptions[index].isActive = false;
        }
      }
    });

    console.log(`Broadcast ${notificationType} notification: ${sent} successful, ${failed} failed`);
    return { sent, failed, criteria: options };
  }

  /**
   * Send notification to a specific subscription
   */
  static async sendNotification(subscription, payload) {
    try {
      const options = {
        TTL: 60 * 60 * 24, // 24 hours
        urgency: 'normal',
        headers: {
          'Topic': 'hlenergy-notifications'
        }
      };

      const result = await webpush.sendNotification(
        subscription,
        JSON.stringify(payload),
        options
      );

      return result;
    } catch (error) {
      console.error('Error sending push notification:', error);
      throw error;
    }
  }

  /**
   * Send test notification
   */
  static async sendTestNotification(userId, customMessage = null) {
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    const payload = {
      title: 'HLenergy Test Notification',
      body: customMessage || 'This is a test notification from your energy management dashboard! 🔋',
      icon: `${baseUrl}/hl-energy-logo-192w.png`,
      badge: `${baseUrl}/hl-energy-logo-96w.png`,
      tag: 'test-notification',
      data: {
        url: '/dashboard',
        type: 'test',
        timestamp: Date.now()
      },
      actions: [
        {
          action: 'view',
          title: 'View Dashboard',
          icon: `${baseUrl}/hl-energy-logo-96w.png`
        },
        {
          action: 'dismiss',
          title: 'Dismiss'
        }
      ],
      requireInteraction: true
    };

    return await this.sendToUser(userId, payload);
  }

  /**
   * Send energy alert notification
   */
  static async sendEnergyAlert(userId, alertData) {
    const payload = {
      title: 'Energy Alert - HLenergy',
      body: alertData.message || 'High energy consumption detected!',
      icon: '/hl-energy-logo-192w.png',
      badge: '/hl-energy-logo-96w.png',
      tag: 'energy-alert',
      data: {
        url: '/dashboard?tab=analytics',
        type: 'energy-alert',
        alertId: alertData.id,
        timestamp: Date.now()
      },
      actions: [
        {
          action: 'view',
          title: 'View Details',
          icon: '/hl-energy-logo-96w.png'
        },
        {
          action: 'dismiss',
          title: 'Dismiss'
        }
      ],
      requireInteraction: true
    };

    return await this.sendToUser(userId, payload);
  }

  /**
   * Send new lead notification
   */
  static async sendNewLeadNotification(userId, leadData) {
    const payload = {
      title: 'New Lead - HLenergy',
      body: `New lead from ${leadData.name || 'Unknown'}: ${leadData.service || 'General inquiry'}`,
      icon: '/hl-energy-logo-192w.png',
      badge: '/hl-energy-logo-96w.png',
      tag: 'new-lead',
      data: {
        url: '/dashboard?tab=leads',
        type: 'new-lead',
        leadId: leadData.id,
        timestamp: Date.now()
      },
      actions: [
        {
          action: 'view',
          title: 'View Lead',
          icon: '/hl-energy-logo-96w.png'
        },
        {
          action: 'dismiss',
          title: 'Dismiss'
        }
      ],
      requireInteraction: true
    };

    return await this.sendToUser(userId, payload);
  }

  /**
   * Get service statistics
   */
  static getStats() {
    const stats = {
      totalSubscriptions: pushSubscriptions.size,
      activeSubscriptions: 0,
      userCount: new Set(),
      deviceTypes: {},
      platforms: {}
    };

    for (const [id, sub] of pushSubscriptions.entries()) {
      if (sub.isActive) {
        stats.activeSubscriptions++;
      }
      
      stats.userCount.add(sub.userId);
      
      const deviceType = sub.deviceInfo.deviceType || 'unknown';
      const platform = sub.deviceInfo.platform || 'unknown';
      
      stats.deviceTypes[deviceType] = (stats.deviceTypes[deviceType] || 0) + 1;
      stats.platforms[platform] = (stats.platforms[platform] || 0) + 1;
    }

    stats.userCount = stats.userCount.size;
    return stats;
  }

  /**
   * Send notification to admin users only
   */
  static async sendToAdmins(payload) {
    return await this.sendToAll(payload, {
      includeRoles: ['admin'],
      notificationType: 'admin'
    });
  }

  /**
   * Send notification to specific user roles
   */
  static async sendToRoles(payload, roles = []) {
    return await this.sendToAll(payload, {
      includeRoles: roles,
      notificationType: 'role-specific'
    });
  }

  /**
   * Send notification to all users except specified ones
   */
  static async sendToAllExcept(payload, excludeUsers = []) {
    return await this.sendToAll(payload, {
      excludeUsers,
      notificationType: 'broadcast-filtered'
    });
  }

  /**
   * Send urgent notification (high priority)
   */
  static async sendUrgentNotification(userId, payload) {
    const urgentPayload = {
      ...payload,
      data: {
        ...payload.data,
        priority: 'high',
        notificationType: 'urgent'
      },
      requireInteraction: true,
      vibrate: [300, 100, 300, 100, 300]
    };

    return await this.sendToUser(userId, urgentPayload);
  }

  /**
   * Send system-wide announcement
   */
  static async sendSystemAnnouncement(payload) {
    const announcementPayload = {
      ...payload,
      data: {
        ...payload.data,
        notificationType: 'system-announcement',
        priority: 'normal'
      },
      tag: 'system-announcement',
      requireInteraction: true
    };

    return await this.sendToAll(announcementPayload, {
      notificationType: 'system-announcement'
    });
  }
}

module.exports = PushNotificationService;

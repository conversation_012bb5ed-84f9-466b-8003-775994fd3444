const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { User, AdminPin, BiometricAuth, RefreshToken, UserSession } = require('../models');

/**
 * Enhanced Authentication Service
 * Handles traditional, PIN, and biometric authentication
 */

class AuthService {
  constructor() {
    this.jwtSecret = process.env.JWT_SECRET;
    this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '1h';
    this.refreshTokenExpiresIn = process.env.REFRESH_TOKEN_EXPIRES_IN || '7d';
  }

  /**
   * Traditional email/password authentication
   */
  async authenticateUser(email, password, ipAddress = null, userAgent = null, deviceFingerprint = null) {
    try {
      const user = await User.findOne({
        where: { email: email.toLowerCase() },
        include: [
          {
            model: AdminPin,
            as: 'adminPin',
            required: false,
          },
          {
            model: BiometricAuth,
            as: 'biometricCredentials',
            required: false,
            where: { is_active: true },
          },
        ],
      });

      if (!user) {
        throw new Error('Invalid credentials');
      }

      if (!user.is_active) {
        throw new Error('Account is deactivated');
      }

      if (user.isLocked()) {
        throw new Error('Account is temporarily locked due to too many failed login attempts');
      }

      const isValidPassword = await user.validatePassword(password);
      if (!isValidPassword) {
        await user.incrementLoginAttempts();
        throw new Error('Invalid credentials');
      }

      // Reset login attempts on successful login
      await user.resetLoginAttempts();

      return this.generateTokens(user, 'password', ipAddress, userAgent, deviceFingerprint);
    } catch (error) {
      console.error('Authentication error:', error);
      throw error;
    }
  }

  /**
   * PIN-based authentication for admin users
   */
  async authenticateWithPin(userId, pin, deviceFingerprint = null, ipAddress = null, userAgent = null) {
    try {
      const user = await User.findByPk(userId, {
        include: [
          {
            model: AdminPin,
            as: 'adminPin',
            required: true,
            where: { is_active: true },
          },
        ],
      });

      if (!user) {
        throw new Error('User not found');
      }

      if (user.role !== 'admin') {
        throw new Error('PIN authentication is only available for admin users');
      }

      if (!user.is_active) {
        throw new Error('Account is deactivated');
      }

      const adminPin = user.adminPin;
      if (!adminPin) {
        throw new Error('No active PIN found for this user');
      }

      const isValidPin = await adminPin.validatePin(pin);
      if (!isValidPin) {
        throw new Error('Invalid PIN');
      }

      // Update metadata with device fingerprint if provided
      if (deviceFingerprint) {
        await adminPin.update({
          metadata: {
            ...adminPin.metadata,
            lastDeviceFingerprint: deviceFingerprint,
            lastLoginIP: this.getClientIP(),
          },
        });
      }

      return this.generateTokens(user, 'pin', ipAddress, userAgent, deviceFingerprint);
    } catch (error) {
      console.error('PIN authentication error:', error);
      throw error;
    }
  }

  /**
   * Verify PIN for session unlock (doesn't generate tokens)
   */
  async verifyPin(userId, pin, deviceFingerprint = null) {
    try {
      const user = await User.findByPk(userId, {
        include: [
          {
            model: AdminPin,
            as: 'adminPin',
            required: true,
            where: { is_active: true },
          },
        ],
      });

      if (!user) {
        throw new Error('User not found');
      }

      if (user.role !== 'admin') {
        throw new Error('PIN authentication is only available for admin users');
      }

      if (!user.is_active) {
        throw new Error('Account is deactivated');
      }

      const adminPin = user.adminPin;
      if (!adminPin) {
        throw new Error('No active PIN found for this user');
      }

      const isValidPin = await adminPin.validatePin(pin);
      if (!isValidPin) {
        throw new Error('Invalid PIN');
      }

      // Update metadata with device fingerprint if provided
      if (deviceFingerprint) {
        await adminPin.update({
          metadata: {
            ...adminPin.metadata,
            lastDeviceFingerprint: deviceFingerprint,
            lastUnlockIP: this.getClientIP(),
            lastUnlockAt: new Date(),
          },
        });
      }

      // Return true for successful verification (no tokens generated)
      return true;
    } catch (error) {
      console.error('PIN verification error:', error);
      throw error;
    }
  }

  /**
   * Biometric authentication
   */
  async authenticateWithBiometric(credentialId, signature, clientData, deviceFingerprint = null, ipAddress = null, userAgent = null) {
    try {
      console.log('🔐 Biometric authentication attempt:', {
        credentialId,
        signature: signature?.substring(0, 20) + '...',
        clientData: clientData?.substring(0, 50) + '...',
        deviceFingerprint
      });

      const credential = await BiometricAuth.findByCredentialId(credentialId);
      console.log('📋 Credential lookup result:', credential ? 'Found' : 'Not found');

      if (!credential) {
        throw new Error('Biometric credential not found');
      }

      console.log('👤 Found credential for user:', credential.user_id);

      const user = await User.findByPk(credential.user_id);
      if (!user) {
        throw new Error('User not found');
      }

      console.log('✅ User found:', { id: user.id, email: user.email, active: user.is_active });

      if (!user.is_active) {
        throw new Error('Account is deactivated');
      }

      // Verify the biometric signature (simplified - in production use WebAuthn library)
      console.log('🔍 Verifying biometric signature...');
      const isValidSignature = await this.verifyBiometricSignature(
        credential,
        signature,
        clientData
      );

      console.log('🔐 Signature verification result:', isValidSignature);

      if (!isValidSignature) {
        throw new Error('Invalid biometric signature');
      }

      // Update credential usage
      await credential.updateUsage();

      // Update device fingerprint if provided
      if (deviceFingerprint) {
        await credential.update({
          device_fingerprint: deviceFingerprint,
          metadata: {
            ...credential.metadata,
            lastLoginIP: this.getClientIP(),
          },
        });
      }

      console.log('🎉 Biometric authentication successful, generating tokens...');
      return this.generateTokens(user, 'biometric', ipAddress, userAgent, deviceFingerprint);
    } catch (error) {
      console.error('❌ Biometric authentication error:', error.message);
      throw error;
    }
  }

  /**
   * Create a new PIN for admin user
   */
  async createAdminPin(userId, pin, deviceFingerprint = null) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Check if user already has a PIN
      const existingPin = await AdminPin.findActiveByUserId(userId);
      if (existingPin) {
        throw new Error('User already has a PIN. Use update PIN instead.');
      }

      // Validate PIN strength
      if (!this.validatePinStrength(pin)) {
        throw new Error('PIN must be 6-8 digits and not contain sequential or repeated numbers');
      }

      const metadata = {
        createdIP: this.getClientIP(),
        deviceFingerprint: deviceFingerprint,
        createdAt: new Date().toISOString(),
      };

      const adminPin = await AdminPin.createPin(userId, pin, metadata);
      return {
        success: true,
        message: 'PIN created successfully',
        pinId: adminPin.id,
      };
    } catch (error) {
      console.error('PIN creation error:', error);
      throw error;
    }
  }

  /**
   * Register a new biometric credential
   */
  async registerBiometricCredential(userId, credentialData, deviceFingerprint = null) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Check credential limit (max 5 per user)
      const existingCount = await BiometricAuth.getUserCredentialCount(userId);
      if (existingCount >= 5) {
        throw new Error('Maximum number of biometric credentials reached (5)');
      }

      const credential = await BiometricAuth.createCredential(userId, {
        ...credentialData,
        deviceFingerprint: deviceFingerprint,
        metadata: {
          registeredIP: this.getClientIP(),
          registeredAt: new Date().toISOString(),
        },
      });

      return {
        success: true,
        message: 'Biometric credential registered successfully',
        credentialId: credential.credential_id,
      };
    } catch (error) {
      console.error('Biometric registration error:', error);
      throw error;
    }
  }

  /**
   * Generate JWT and refresh tokens with session tracking
   */
  async generateTokens(user, authMethod = 'password', ipAddress = null, userAgent = null, deviceFingerprint = null) {
    try {
      // Generate a unique session token for tracking
      const sessionToken = crypto.randomBytes(32).toString('hex');

      const payload = {
        id: user.id, // Use 'id' to match auth middleware expectation
        userId: user.id, // Keep userId for backward compatibility
        email: user.email,
        role: user.role,
        authMethod: authMethod,
        jti: sessionToken, // JWT ID for session tracking
        iat: Math.floor(Date.now() / 1000),
      };

      const accessToken = jwt.sign(payload, this.jwtSecret, {
        expiresIn: this.jwtExpiresIn,
      });

      // Calculate token expiration time
      const expiresInSeconds = this.parseExpiresIn(this.jwtExpiresIn);
      const expiresAt = new Date(Date.now() + (expiresInSeconds * 1000));

      // Create user session in database
      const session = await UserSession.createSession(
        sessionToken,
        user.id,
        expiresAt,
        ipAddress || this.getClientIP(),
        userAgent,
        deviceFingerprint,
        15 // 15 minutes idle timeout by default
      );

      // Generate a unique refresh token string
      const refreshTokenString = jwt.sign(
        {
          userId: user.id,
          type: 'refresh',
          authMethod: authMethod,
          sessionToken: sessionToken, // Link refresh token to session
          iat: Math.floor(Date.now() / 1000)
        },
        this.jwtSecret,
        { expiresIn: '30d' }
      );

      const refreshToken = await RefreshToken.createToken(
        user.id,
        refreshTokenString,
        30, // 30 days
        ipAddress || this.getClientIP(),
        userAgent
      );

      console.log(`🔐 Created new session for user ${user.id}: ${sessionToken.substring(0, 20)}...`);

      return {
        user: user.toJSON(),
        accessToken,
        refreshToken: refreshToken.token,
        expiresIn: expiresInSeconds,
        authMethod,
        sessionId: session.id,
        sessionToken: sessionToken.substring(0, 20) + '...', // Partial token for client reference
      };
    } catch (error) {
      console.error('Token generation error:', error);
      throw error;
    }
  }

  /**
   * Validate PIN strength
   */
  validatePinStrength(pin) {
    // PIN must be 6-8 digits
    if (!/^\d{6,8}$/.test(pin)) {
      return false;
    }

    // Check for sequential numbers (123456, 654321)
    const sequential = /(?:0123456789|9876543210)/.test(pin);
    if (sequential) {
      return false;
    }

    // Check for repeated numbers (111111, 222222)
    const repeated = /^(\d)\1+$/.test(pin);
    if (repeated) {
      return false;
    }

    // Check for common weak PINs
    const weakPins = ['123456', '000000', '111111', '123123', '654321'];
    if (weakPins.includes(pin)) {
      return false;
    }

    return true;
  }

  /**
   * Verify biometric signature (simplified implementation)
   */
  async verifyBiometricSignature(credential, signature, clientData) {
    // In production, use a proper WebAuthn library like @simplewebauthn/server
    // This is a simplified implementation for demonstration
    try {
      console.log('🔍 Signature verification details:', {
        hasSignature: !!signature,
        hasClientData: !!clientData,
        hasPublicKey: !!credential.public_key,
        signatureLength: signature?.length,
        clientDataLength: clientData?.length
      });

      // Decode and log client data for debugging
      if (clientData) {
        try {
          const decodedClientData = Buffer.from(clientData, 'base64').toString('utf8');
          console.log('📋 Decoded client data:', decodedClientData);
        } catch (e) {
          console.log('⚠️ Could not decode client data');
        }
      }

      // For development/testing, be more permissive
      // Just check that we have the required fields
      const hasRequiredFields = signature && clientData && credential.public_key;

      console.log('✅ Basic validation result:', hasRequiredFields);

      // In production this would involve:
      // 1. Verifying the signature against the stored public key
      // 2. Validating the client data
      // 3. Checking the authenticator data
      // 4. Verifying the counter to prevent replay attacks

      return hasRequiredFields;
    } catch (error) {
      console.error('❌ Biometric signature verification error:', error);
      return false;
    }
  }

  /**
   * Get client IP (simplified)
   */
  getClientIP() {
    // In production, extract from request object
    return '127.0.0.1';
  }

  /**
   * Parse expires in string to seconds
   */
  parseExpiresIn(expiresIn) {
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    if (!match) return 3600; // Default 1 hour

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 60 * 60;
      case 'd': return value * 60 * 60 * 24;
      default: return 3600;
    }
  }

  /**
   * Update admin PIN with current PIN verification
   */
  async updateAdminPin(userId, currentPin, newPin, deviceFingerprint = null) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Find current active PIN
      const currentPinRecord = await AdminPin.findActiveByUserId(userId);
      if (!currentPinRecord) {
        throw new Error('No active PIN found. Please create a PIN first.');
      }

      // Verify current PIN
      await currentPinRecord.validatePin(currentPin);
      // If validatePin doesn't throw, the PIN is valid

      // Validate new PIN
      if (newPin.length < 6 || newPin.length > 8) {
        throw new Error('New PIN must be 6-8 digits long');
      }

      if (!/^\d+$/.test(newPin)) {
        throw new Error('New PIN must contain only numbers');
      }

      // Check if new PIN is the same as current PIN
      console.log('Comparing PINs:', { currentPin, newPin });
      const isSamePin = await currentPinRecord.checkPin(newPin);
      console.log('PIN comparison result:', isSamePin);
      if (isSamePin) {
        throw new Error('New PIN must be different from current PIN');
      }

      // Check if PIN was recently used (last 10 PINs)
      const isRecentlyUsed = await AdminPin.isPinRecentlyUsed(userId, newPin, 10);
      if (isRecentlyUsed) {
        throw new Error('PIN was recently used. Please choose a different PIN.');
      }

      // Create new PIN (this will deactivate the old one)
      const newPinRecord = await AdminPin.createPin(userId, newPin, {
        deviceFingerprint,
        updatedAt: new Date(),
        previousPinId: currentPinRecord.id
      });

      return {
        success: true,
        message: 'PIN updated successfully',
        pinId: newPinRecord.id
      };
    } catch (error) {
      console.error('Update admin PIN error:', error);
      throw error;
    }
  }

  /**
   * Remove admin PIN with current PIN verification
   */
  async removeAdminPin(userId, currentPin, deviceFingerprint = null) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Find current active PIN
      const currentPinRecord = await AdminPin.findActiveByUserId(userId);
      if (!currentPinRecord) {
        throw new Error('No active PIN found');
      }

      // Verify current PIN
      await currentPinRecord.validatePin(currentPin);
      // If validatePin doesn't throw, the PIN is valid

      // Deactivate the PIN
      await AdminPin.update(
        {
          is_active: false,
          metadata: {
            ...currentPinRecord.metadata,
            removedAt: new Date(),
            deviceFingerprint
          }
        },
        { where: { id: currentPinRecord.id } }
      );

      return {
        success: true,
        message: 'PIN removed successfully'
      };
    } catch (error) {
      console.error('Remove admin PIN error:', error);
      throw error;
    }
  }

  // Get authentication methods for user
  async getAuthMethods(userId) {
    try {
      // Check for PIN
      const pinRecord = await AdminPin.findOne({
        where: { user_id: userId, is_active: true }
      });

      // Check for biometric credentials
      const biometricCount = await BiometricAuth.count({
        where: { user_id: userId, is_active: true }
      });

      const methods = [];
      if (pinRecord) methods.push('pin');
      if (biometricCount > 0) methods.push('biometric');

      return {
        methods,
        hasPin: !!pinRecord,
        biometricCount
      };
    } catch (error) {
      console.error('Get auth methods error:', error);
      throw new Error('Failed to get authentication methods');
    }
  }

  // Remove biometric authentication
  async removeBiometric(userId) {
    try {
      // Find active biometric credentials for user
      const biometricCredentials = await BiometricAuth.findAll({
        where: { user_id: userId, is_active: true }
      });

      if (biometricCredentials.length === 0) {
        throw new Error('No biometric credentials found for this user');
      }

      // Deactivate all biometric credentials (keep for audit trail)
      await BiometricAuth.update(
        { is_active: false },
        { where: { user_id: userId, is_active: true } }
      );

      return {
        message: `Removed ${biometricCredentials.length} biometric credential(s)`,
        removedCount: biometricCredentials.length
      };
    } catch (error) {
      console.error('Remove biometric error:', error);
      throw error;
    }
  }
}

module.exports = new AuthService();

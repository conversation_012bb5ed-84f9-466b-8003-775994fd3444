const crypto = require('crypto');
const firebaseService = require('./firebaseService');

/**
 * Firebase Cloud Messaging Notification Service
 * Handles FCM token management and notification sending
 */
class FirebaseNotificationService {
  constructor() {
    this.FCMSubscription = null;
  }

  /**
   * Initialize the service with database models
   */
  static initialize(models) {
    this.FCMSubscription = models.FCMSubscription;
    console.log('🔥 Firebase Notification Service initialized');
  }

  /**
   * Add FCM subscription
   */
  static async addSubscription(userId, token, userRole = 'user', deviceInfo = {}) {
    try {
      // Create hash of the token for indexing
      const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

      // Check if subscription already exists
      const existingSubscription = await this.FCMSubscription.findOne({
        where: { tokenHash }
      });

      if (existingSubscription) {
        // Update existing subscription
        await existingSubscription.update({
          userId,
          userRole,
          deviceInfo,
          isActive: true,
          lastUsed: new Date(),
          failureCount: 0
        });

        console.log(`🔥 Updated existing FCM subscription for user ${userId}`);
        return existingSubscription;
      }

      // Create new subscription
      const subscription = await this.FCMSubscription.create({
        userId,
        token,
        tokenHash,
        userRole,
        deviceInfo,
        isActive: true,
        lastUsed: new Date(),
        failureCount: 0,
        topics: []
      });

      console.log(`🔥 Created new FCM subscription for user ${userId}`);
      return subscription;
    } catch (error) {
      console.error('❌ Error adding FCM subscription:', error);
      throw error;
    }
  }

  /**
   * Remove FCM subscription
   */
  static async removeSubscription(token) {
    try {
      const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
      
      const subscription = await this.FCMSubscription.findOne({
        where: { tokenHash }
      });

      if (subscription) {
        await subscription.destroy();
        console.log(`🔥 Removed FCM subscription: ${subscription.id}`);
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ Error removing FCM subscription:', error);
      throw error;
    }
  }

  /**
   * Send notification to a specific user
   */
  static async sendToUser(userId, payload) {
    try {
      const userSubscriptions = await this.FCMSubscription.findAll({
        where: {
          userId: userId,
          isActive: true
        }
      });

      if (userSubscriptions.length === 0) {
        console.log(`📭 No active FCM subscriptions found for user ${userId}`);
        return { sent: 0, failed: 0 };
      }

      const tokens = userSubscriptions.map(sub => sub.token);
      const result = await firebaseService.sendToMultipleTokens(tokens, payload);

      // Update subscription status based on results
      if (result.responses) {
        for (let i = 0; i < result.responses.length; i++) {
          const response = result.responses[i];
          const subscription = userSubscriptions[i];

          if (response.success) {
            await subscription.markAsUsed();
          } else {
            await subscription.incrementFailure();

            // Check if token should be deleted immediately
            if (response.error && (
              response.error.message === 'TOKEN_INVALID' ||
              response.error.message === 'TOKEN_NOT_FOUND' ||
              response.error.message === 'TOKEN_INVALID_FORMAT'
            )) {
              console.log(`🗑️ Deleting invalid FCM subscription ${subscription.id}: ${response.error.message}`);
              await subscription.destroy();
            } else if (subscription.failureCount >= 5) {
              await subscription.deactivate();
              console.log(`🗑️ Deactivated FCM subscription ${subscription.id} due to failures`);
            }
          }
        }
      }

      console.log(`🔥 FCM notification sent to user ${userId}: ${result.successCount} success, ${result.failureCount} failed`);
      return { sent: result.successCount, failed: result.failureCount };
    } catch (error) {
      console.error('❌ Error sending FCM notification to user:', error);
      throw error;
    }
  }

  /**
   * Send notification to admin users
   */
  static async sendToAdmins(payload) {
    try {
      const adminSubscriptions = await this.FCMSubscription.findAll({
        where: {
          userRole: 'admin',
          isActive: true
        }
      });

      if (adminSubscriptions.length === 0) {
        console.log('📭 No active admin FCM subscriptions found');
        return { sent: 0, failed: 0 };
      }

      const tokens = adminSubscriptions.map(sub => sub.token);
      const result = await firebaseService.sendToMultipleTokens(tokens, payload);

      // Update subscription status
      if (result.responses) {
        for (let i = 0; i < result.responses.length; i++) {
          const response = result.responses[i];
          const subscription = adminSubscriptions[i];

          if (response.success) {
            await subscription.markAsUsed();
          } else {
            await subscription.incrementFailure();
            
            if (subscription.failureCount >= 5) {
              await subscription.deactivate();
            }
          }
        }
      }

      console.log(`🔥 FCM admin notification sent: ${result.successCount} success, ${result.failureCount} failed`);
      return { sent: result.successCount, failed: result.failureCount };
    } catch (error) {
      console.error('❌ Error sending FCM admin notification:', error);
      throw error;
    }
  }

  /**
   * Send system announcement to all users
   */
  static async sendSystemAnnouncement(payload) {
    try {
      const allSubscriptions = await this.FCMSubscription.findAll({
        where: { isActive: true }
      });

      if (allSubscriptions.length === 0) {
        console.log('📭 No active FCM subscriptions found for system announcement');
        return { sent: 0, failed: 0 };
      }

      const tokens = allSubscriptions.map(sub => sub.token);
      const result = await firebaseService.sendToMultipleTokens(tokens, payload);

      // Update subscription status
      if (result.responses) {
        for (let i = 0; i < result.responses.length; i++) {
          const response = result.responses[i];
          const subscription = allSubscriptions[i];

          if (response.success) {
            await subscription.markAsUsed();
          } else {
            await subscription.incrementFailure();
            
            if (subscription.failureCount >= 5) {
              await subscription.deactivate();
            }
          }
        }
      }

      console.log(`🔥 FCM system announcement sent: ${result.successCount} success, ${result.failureCount} failed`);
      return { sent: result.successCount, failed: result.failureCount };
    } catch (error) {
      console.error('❌ Error sending FCM system announcement:', error);
      throw error;
    }
  }

  /**
   * Send test notification
   */
  static async sendTestNotification(userId, customMessage = null) {
    try {
      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const message = customMessage || 'Test notification from HLenergy Firebase';
      
      const payload = {
        title: '🔥 Firebase Test Notification',
        body: message,
        icon: `${baseUrl}/hl-energy-logo-192w.png`,
        badge: `${baseUrl}/hl-energy-logo-96w.png`,
        tag: 'firebase-test-notification',
        data: {
          url: '/dashboard',
          type: 'test',
          timestamp: Date.now().toString()
        },
        requireInteraction: false,
        actions: [
          {
            action: 'view',
            title: 'View Dashboard'
          },
          {
            action: 'dismiss',
            title: 'Dismiss'
          }
        ]
      };

      const result = await this.sendToUser(userId, payload);
      
      console.log(`🔥 Firebase test notification sent to user ${userId}:`, {
        sent: result.sent,
        failed: result.failed,
        message: message
      });

      return result;
    } catch (error) {
      console.error('❌ Error sending Firebase test notification:', error);
      throw error;
    }
  }

  /**
   * Get subscription statistics
   */
  static async getStats() {
    try {
      const totalSubscriptions = await this.FCMSubscription.count();
      const activeSubscriptions = await this.FCMSubscription.count({
        where: { isActive: true }
      });
      const adminSubscriptions = await this.FCMSubscription.count({
        where: { userRole: 'admin', isActive: true }
      });
      const userSubscriptions = await this.FCMSubscription.count({
        where: { userRole: 'user', isActive: true }
      });

      return {
        total: totalSubscriptions,
        active: activeSubscriptions,
        admin: adminSubscriptions,
        user: userSubscriptions,
        inactive: totalSubscriptions - activeSubscriptions
      };
    } catch (error) {
      console.error('❌ Error getting FCM subscription stats:', error);
      throw error;
    }
  }

  /**
   * Cleanup inactive subscriptions
   */
  static async cleanup() {
    try {
      // Clean up subscriptions that have been inactive for 30 days
      const cleanedInactive = await this.FCMSubscription.cleanupInactiveSubscriptions(30);
      
      // Deactivate subscriptions with too many failures
      const cleanedFailed = await this.FCMSubscription.cleanupFailedSubscriptions(10);

      console.log(`🧹 FCM cleanup completed: ${cleanedInactive} inactive removed, ${cleanedFailed[0]} failed deactivated`);
      
      return {
        inactiveRemoved: cleanedInactive,
        failedDeactivated: cleanedFailed[0]
      };
    } catch (error) {
      console.error('❌ Error during FCM cleanup:', error);
      throw error;
    }
  }
}

module.exports = FirebaseNotificationService;

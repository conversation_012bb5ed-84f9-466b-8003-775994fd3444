const admin = require('firebase-admin');
const path = require('path');

/**
 * Firebase Admin Service for server-side operations
 * Handles FCM (Firebase Cloud Messaging) for push notifications
 */
class FirebaseService {
  constructor() {
    this.app = null;
    this.messaging = null;
    this.isInitialized = false;
  }

  /**
   * Initialize Firebase Admin SDK
   */
  async initialize() {
    if (this.isInitialized) {
      return this.app;
    }

    try {
      console.log('🔥 Initializing Firebase Admin SDK...');

      // Check if already initialized (Firebase Admin can only be initialized once)
      if (admin.apps.length > 0) {
        console.log('🔥 Using existing Firebase Admin app');
        this.app = admin.apps[0];
        this.messaging = admin.messaging(this.app);
        this.isInitialized = true;
        return this.app;
      }

      // Load service account credentials
      const serviceAccountPath = path.join(__dirname, '../../firebase-service-account.json');
      console.log('🔥 Loading service account from:', serviceAccountPath);

      const serviceAccount = require(serviceAccountPath);

      // Initialize Firebase Admin with service account
      this.app = admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.project_id
      });

      this.messaging = admin.messaging(this.app);

      // Test the messaging service
      console.log('🔥 Testing Firebase messaging service...');
      console.log('✅ Firebase messaging service available:', !!this.messaging);

      this.isInitialized = true;
      console.log('✅ Firebase Admin SDK initialized successfully');

      return this.app;
    } catch (error) {
      console.error('❌ Firebase Admin SDK initialization failed:', error);
      console.error('❌ Error details:', error.message);
      throw error;
    }
  }

  /**
   * Send notification to a single FCM token
   */
  async sendToToken(token, payload) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const message = {
        token: token,
        notification: {
          title: payload.title || 'HLenergy Notification',
          body: payload.body || 'You have a new notification'
        },
        data: {
          // Convert all data values to strings (FCM requirement)
          ...Object.fromEntries(
            Object.entries(payload.data || {}).map(([key, value]) => [
              key,
              typeof value === 'string' ? value : JSON.stringify(value)
            ])
          ),
          // Add notification data as strings
          title: payload.title || 'HLenergy Notification',
          body: payload.body || 'You have a new notification',
          icon: payload.icon || '/hl-energy-logo-192w.png',
          badge: payload.badge || '/hl-energy-logo-96w.png',
          tag: payload.tag || 'hlenergy-notification',
          requireInteraction: payload.requireInteraction ? 'true' : 'false'
        },
        webpush: {
          notification: {
            icon: payload.icon || '/hl-energy-logo-192w.png',
            badge: payload.badge || '/hl-energy-logo-96w.png',
            tag: payload.tag || 'hlenergy-notification',
            requireInteraction: payload.requireInteraction || false,
            actions: payload.actions || [
              {
                action: 'view',
                title: 'View'
              },
              {
                action: 'dismiss',
                title: 'Dismiss'
              }
            ]
          }
        }
      };

      console.log('🔥 Sending FCM message to token:', token.substring(0, 20) + '...');
      const response = await this.messaging.send(message);
      console.log('✅ FCM message sent successfully:', response);

      return { success: true, messageId: response };
    } catch (error) {
      console.error('❌ FCM send failed:', error);
      console.error('❌ Error code:', error.code);
      console.error('❌ Error message:', error.message);

      // Handle specific FCM errors
      if (error.code === 'messaging/registration-token-not-registered') {
        console.log('🗑️ FCM token is invalid/expired');
        return { success: false, error: 'TOKEN_INVALID', shouldDelete: true };
      } else if (error.code === 'messaging/invalid-registration-token') {
        console.log('🗑️ FCM token format is invalid');
        return { success: false, error: 'TOKEN_FORMAT_INVALID', shouldDelete: true };
      } else if (error.message && error.message.includes('Requested entity was not found')) {
        console.log('🗑️ FCM token not found - likely invalid or expired');
        return { success: false, error: 'TOKEN_NOT_FOUND', shouldDelete: true };
      } else if (error.code === 'messaging/invalid-argument') {
        console.log('🗑️ FCM token has invalid format');
        return { success: false, error: 'TOKEN_INVALID_FORMAT', shouldDelete: true };
      }

      return { success: false, error: error.message };
    }
  }

  /**
   * Send notification to multiple FCM tokens
   */
  async sendToMultipleTokens(tokens, payload) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      if (!tokens || tokens.length === 0) {
        return { successCount: 0, failureCount: 0, responses: [] };
      }

      console.log(`🔥 Sending FCM messages to ${tokens.length} tokens`);

      // Try to use sendMulticast if available (more efficient)
      if (typeof this.messaging.sendMulticast === 'function') {
        const message = {
          notification: {
            title: payload.title || 'HLenergy Notification',
            body: payload.body || 'You have a new notification'
          },
          data: {
            // Convert all data values to strings (FCM requirement)
            ...Object.fromEntries(
              Object.entries(payload.data || {}).map(([key, value]) => [
                key,
                typeof value === 'string' ? value : JSON.stringify(value)
              ])
            ),
            // Add notification data as strings
            title: payload.title || 'HLenergy Notification',
            body: payload.body || 'You have a new notification',
            icon: payload.icon || '/hl-energy-logo-192w.png',
            badge: payload.badge || '/hl-energy-logo-96w.png',
            tag: payload.tag || 'hlenergy-notification',
            requireInteraction: payload.requireInteraction ? 'true' : 'false'
          },
          webpush: {
            notification: {
              icon: payload.icon || '/hl-energy-logo-192w.png',
              badge: payload.badge || '/hl-energy-logo-96w.png',
              tag: payload.tag || 'hlenergy-notification',
              requireInteraction: payload.requireInteraction || false,
              actions: payload.actions || [
                {
                  action: 'view',
                  title: 'View'
                },
                {
                  action: 'dismiss',
                  title: 'Dismiss'
                }
              ]
            }
          },
          tokens: tokens
        };

        const response = await this.messaging.sendMulticast(message);

        console.log(`✅ FCM multicast sent: ${response.successCount} success, ${response.failureCount} failed`);

        // Log failed tokens for cleanup
        if (response.failureCount > 0) {
          response.responses.forEach((resp, idx) => {
            if (!resp.success) {
              console.log(`❌ Failed token ${idx}: ${resp.error?.code} - ${tokens[idx].substring(0, 20)}...`);
            }
          });
        }

        return {
          successCount: response.successCount,
          failureCount: response.failureCount,
          responses: response.responses,
          invalidTokens: response.responses
            .map((resp, idx) => resp.success ? null : tokens[idx])
            .filter(token => token !== null)
        };
      }

      // Fallback to individual sends
      console.log('⚠️ sendMulticast not available, sending individual messages');
      const results = [];
      let successCount = 0;
      let failureCount = 0;

      for (let i = 0; i < tokens.length; i++) {
        const token = tokens[i];
        try {
          const result = await this.sendToToken(token, payload);
          if (result.success) {
            successCount++;
            results.push({ success: true, messageId: result.messageId });
          } else {
            failureCount++;
            results.push({ success: false, error: { message: result.error } });
          }
        } catch (error) {
          failureCount++;
          results.push({ success: false, error: { message: error.message } });
        }
      }

      console.log(`✅ FCM individual sends completed: ${successCount} success, ${failureCount} failed`);

      return {
        successCount,
        failureCount,
        responses: results,
        invalidTokens: results
          .map((resp, idx) => resp.success ? null : tokens[idx])
          .filter(token => token !== null)
      };
    } catch (error) {
      console.error('❌ FCM multicast send failed:', error);
      return {
        successCount: 0,
        failureCount: tokens.length,
        error: error.message,
        responses: []
      };
    }
  }

  /**
   * Send notification to a topic
   */
  async sendToTopic(topic, payload) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const message = {
        topic: topic,
        notification: {
          title: payload.title || 'HLenergy Notification',
          body: payload.body || 'You have a new notification'
        },
        data: {
          // Convert all data values to strings (FCM requirement)
          ...Object.fromEntries(
            Object.entries(payload.data || {}).map(([key, value]) => [
              key,
              typeof value === 'string' ? value : JSON.stringify(value)
            ])
          )
        }
      };

      console.log(`🔥 Sending FCM message to topic: ${topic}`);
      const response = await this.messaging.send(message);
      console.log('✅ FCM topic message sent successfully:', response);

      return { success: true, messageId: response };
    } catch (error) {
      console.error('❌ FCM topic send failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Subscribe token to topic
   */
  async subscribeToTopic(tokens, topic) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const tokensArray = Array.isArray(tokens) ? tokens : [tokens];
      const response = await this.messaging.subscribeToTopic(tokensArray, topic);

      console.log(`✅ Subscribed ${tokensArray.length} tokens to topic ${topic}:`, response);
      return response;
    } catch (error) {
      console.error('❌ FCM topic subscription failed:', error);
      throw error;
    }
  }

  /**
   * Unsubscribe token from topic
   */
  async unsubscribeFromTopic(tokens, topic) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const tokensArray = Array.isArray(tokens) ? tokens : [tokens];
      const response = await this.messaging.unsubscribeFromTopic(tokensArray, topic);

      console.log(`✅ Unsubscribed ${tokensArray.length} tokens from topic ${topic}:`, response);
      return response;
    } catch (error) {
      console.error('❌ FCM topic unsubscription failed:', error);
      throw error;
    }
  }
}

// Export singleton instance
module.exports = new FirebaseService();

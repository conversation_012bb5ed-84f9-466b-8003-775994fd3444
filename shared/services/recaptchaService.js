const axios = require('axios');

/**
 * Google reCAPTCHA v3 Verification Service
 */
class RecaptchaService {
  constructor() {
    this.secretKey = process.env.RECAPTCHA_SECRET_KEY;
    this.verifyUrl = 'https://www.google.com/recaptcha/api/siteverify';
    this.minimumScore = 0.5; // Minimum score for legitimate users (0.0 to 1.0)
  }

  /**
   * Verify reCAPTCHA v3 token
   * @param {string} token - reCAPTCHA token from frontend
   * @param {string} remoteip - User's IP address (optional)
   * @param {string} expectedAction - Expected action name (optional)
   * @returns {Promise<Object>} Verification result
   */
  async verifyToken(token, remoteip = null, expectedAction = null) {
    try {
      // Check if secret key is configured
      if (!this.secretKey) {
        console.warn('⚠️ reCAPTCHA secret key not configured');
        return {
          success: false,
          error: 'reCAPTCHA not configured',
          score: 0,
          action: null,
          challenge_ts: null,
          hostname: null
        };
      }

      // Check if token is provided
      if (!token) {
        return {
          success: false,
          error: 'reCAPTCHA token is required',
          score: 0,
          action: null,
          challenge_ts: null,
          hostname: null
        };
      }

      console.log('🔐 Verifying reCAPTCHA token...');

      // Prepare request data
      const requestData = {
        secret: this.secretKey,
        response: token
      };

      // Add remote IP if provided
      if (remoteip) {
        requestData.remoteip = remoteip;
      }

      // Make request to Google's verification API
      const response = await axios.post(this.verifyUrl, null, {
        params: requestData,
        timeout: 10000, // 10 second timeout
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      const result = response.data;

      console.log('📊 reCAPTCHA verification result:', {
        success: result.success,
        score: result.score,
        action: result.action,
        hostname: result.hostname,
        challenge_ts: result.challenge_ts,
        error_codes: result['error-codes']
      });

      // Check if verification was successful
      if (!result.success) {
        return {
          success: false,
          error: 'reCAPTCHA verification failed',
          error_codes: result['error-codes'] || [],
          score: result.score || 0,
          action: result.action || null,
          challenge_ts: result.challenge_ts || null,
          hostname: result.hostname || null
        };
      }

      // Check action if expected action is provided
      if (expectedAction && result.action !== expectedAction) {
        console.warn(`⚠️ reCAPTCHA action mismatch. Expected: ${expectedAction}, Got: ${result.action}`);
        return {
          success: false,
          error: `Action mismatch. Expected: ${expectedAction}, Got: ${result.action}`,
          score: result.score || 0,
          action: result.action || null,
          challenge_ts: result.challenge_ts || null,
          hostname: result.hostname || null
        };
      }

      // Check score (for reCAPTCHA v3)
      const score = result.score || 0;
      if (score < this.minimumScore) {
        console.warn(`⚠️ reCAPTCHA score too low: ${score} (minimum: ${this.minimumScore})`);
        return {
          success: false,
          error: `Score too low: ${score} (minimum: ${this.minimumScore})`,
          score: score,
          action: result.action || null,
          challenge_ts: result.challenge_ts || null,
          hostname: result.hostname || null
        };
      }

      // Verification successful
      console.log('✅ reCAPTCHA verification successful');
      return {
        success: true,
        score: score,
        action: result.action || null,
        challenge_ts: result.challenge_ts || null,
        hostname: result.hostname || null
      };

    } catch (error) {
      console.error('❌ reCAPTCHA verification error:', error.message);

      // Handle network errors
      if (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT') {
        return {
          success: false,
          error: 'reCAPTCHA verification timeout',
          score: 0,
          action: null,
          challenge_ts: null,
          hostname: null
        };
      }

      // Handle other errors
      return {
        success: false,
        error: `reCAPTCHA verification error: ${error.message}`,
        score: 0,
        action: null,
        challenge_ts: null,
        hostname: null
      };
    }
  }

  /**
   * Verify reCAPTCHA token with specific action
   * @param {string} token - reCAPTCHA token from frontend
   * @param {string} action - Expected action name
   * @param {string} remoteip - User's IP address (optional)
   * @returns {Promise<Object>} Verification result
   */
  async verifyContactForm(token, remoteip = null) {
    return this.verifyToken(token, remoteip, 'contact_form');
  }

  /**
   * Set minimum score threshold
   * @param {number} score - Minimum score (0.0 to 1.0)
   */
  setMinimumScore(score) {
    if (score >= 0 && score <= 1) {
      this.minimumScore = score;
      console.log(`📊 reCAPTCHA minimum score set to: ${score}`);
    } else {
      console.warn('⚠️ Invalid minimum score. Must be between 0.0 and 1.0');
    }
  }

  /**
   * Get current minimum score
   * @returns {number} Current minimum score
   */
  getMinimumScore() {
    return this.minimumScore;
  }

  /**
   * Check if reCAPTCHA is configured
   * @returns {boolean} True if configured
   */
  isConfigured() {
    return !!this.secretKey;
  }

  /**
   * Get configuration status
   * @returns {Object} Configuration information
   */
  getStatus() {
    return {
      configured: this.isConfigured(),
      minimumScore: this.minimumScore,
      verifyUrl: this.verifyUrl,
      secretKeyPresent: !!this.secretKey
    };
  }
}

// Export singleton instance
module.exports = new RecaptchaService();

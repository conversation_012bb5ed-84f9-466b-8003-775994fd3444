const { Customer, Project, Communication, Document, User } = require('../models');
const { Op } = require('sequelize');
const { ValidationError, NotFoundError } = require('../utils/errors');

class CustomerService {
  /**
   * Create a new customer
   */
  async createCustomer(customerData, userId) {
    try {
      // Check if customer with email already exists
      const existingCustomer = await Customer.findOne({
        where: { email: customerData.email }
      });

      if (existingCustomer) {
        throw new ValidationError('Customer with this email already exists');
      }

      // Calculate initial lead score
      const leadScore = this.calculateLeadScore(customerData);

      const customer = await Customer.create({
        ...customerData,
        leadScore,
        firstContactDate: new Date(),
        lastContactDate: new Date(),
        assignedTo: customerData.assignedTo || userId
      });

      return await this.getCustomerById(customer.id);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get customer by ID with related data
   */
  async getCustomerById(customerId, includeRelated = true) {
    const includeOptions = includeRelated ? [
      {
        model: User,
        as: 'assignedUser',
        attributes: ['id', 'name', 'email']
      },
      {
        model: Project,
        as: 'projects',
        limit: 5,
        order: [['createdAt', 'DESC']]
      },
      {
        model: Communication,
        as: 'communications',
        limit: 10,
        order: [['createdAt', 'DESC']],
        include: [{
          model: User,
          as: 'user',
          attributes: ['id', 'name']
        }]
      }
    ] : [];

    const customer = await Customer.findByPk(customerId, {
      include: includeOptions
    });

    if (!customer) {
      throw new NotFoundError('Customer not found');
    }

    return customer;
  }

  /**
   * Get customers with filtering and pagination
   */
  async getCustomers(options = {}) {
    const {
      page = 1,
      limit = 20,
      status,
      priority,
      assignedTo,
      leadSource,
      search,
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = options;

    const offset = (page - 1) * limit;
    const whereClause = { isActive: true };

    // Apply filters
    if (status) {
      whereClause.status = status;
    }

    if (priority) {
      whereClause.priority = priority;
    }

    if (assignedTo) {
      whereClause.assignedTo = assignedTo;
    }

    if (leadSource) {
      whereClause.leadSource = leadSource;
    }

    if (search) {
      whereClause[Op.or] = [
        { firstName: { [Op.iLike]: `%${search}%` } },
        { lastName: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } },
        { companyName: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const { count, rows } = await Customer.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'assignedUser',
          attributes: ['id', 'name', 'email']
        }
      ],
      limit,
      offset,
      order: [[sortBy, sortOrder]],
      distinct: true
    });

    return {
      customers: rows,
      pagination: {
        page,
        limit,
        total: count,
        pages: Math.ceil(count / limit)
      }
    };
  }

  /**
   * Update customer
   */
  async updateCustomer(customerId, updateData, userId) {
    const customer = await Customer.findByPk(customerId);

    if (!customer) {
      throw new NotFoundError('Customer not found');
    }

    // Recalculate lead score if relevant data changed
    if (updateData.companySize || updateData.monthlyEnergyBudget || updateData.industry) {
      updateData.leadScore = this.calculateLeadScore({ ...customer.toJSON(), ...updateData });
    }

    // Update last contact date if status changed
    if (updateData.status && updateData.status !== customer.status) {
      updateData.lastContactDate = new Date();
    }

    await customer.update(updateData);

    return await this.getCustomerById(customerId);
  }

  /**
   * Delete customer (soft delete)
   */
  async deleteCustomer(customerId) {
    const customer = await Customer.findByPk(customerId);

    if (!customer) {
      throw new NotFoundError('Customer not found');
    }

    await customer.update({ isActive: false });

    return { message: 'Customer deleted successfully' };
  }

  /**
   * Convert lead to customer
   */
  async convertToCustomer(customerId, conversionData = {}) {
    const customer = await Customer.findByPk(customerId);

    if (!customer) {
      throw new NotFoundError('Customer not found');
    }

    if (customer.status === 'customer') {
      throw new ValidationError('Customer is already converted');
    }

    await customer.update({
      status: 'customer',
      conversionDate: new Date(),
      actualValue: conversionData.actualValue || customer.estimatedValue,
      ...conversionData
    });

    return await this.getCustomerById(customerId);
  }

  /**
   * Get customer statistics
   */
  async getCustomerStats(timeRange = '30d') {
    const dateFilter = this.getDateFilter(timeRange);

    const [
      totalCustomers,
      newLeads,
      convertedCustomers,
      leadsBySource,
      leadsByStatus,
      topPerformers
    ] = await Promise.all([
      Customer.count({ where: { isActive: true } }),
      
      Customer.count({
        where: {
          createdAt: { [Op.gte]: dateFilter },
          status: 'lead'
        }
      }),
      
      Customer.count({
        where: {
          conversionDate: { [Op.gte]: dateFilter },
          status: 'customer'
        }
      }),
      
      Customer.findAll({
        attributes: [
          'leadSource',
          [Customer.sequelize.fn('COUNT', Customer.sequelize.col('id')), 'count']
        ],
        where: { createdAt: { [Op.gte]: dateFilter } },
        group: ['leadSource'],
        order: [[Customer.sequelize.fn('COUNT', Customer.sequelize.col('id')), 'DESC']]
      }),
      
      Customer.findAll({
        attributes: [
          'status',
          [Customer.sequelize.fn('COUNT', Customer.sequelize.col('id')), 'count']
        ],
        where: { isActive: true },
        group: ['status']
      }),
      
      Customer.findAll({
        include: [{
          model: User,
          as: 'assignedUser',
          attributes: ['id', 'name']
        }],
        where: {
          assignedTo: { [Op.not]: null },
          createdAt: { [Op.gte]: dateFilter }
        },
        attributes: [
          'assignedTo',
          [Customer.sequelize.fn('COUNT', Customer.sequelize.col('Customer.id')), 'customerCount'],
          [Customer.sequelize.fn('AVG', Customer.sequelize.col('leadScore')), 'avgLeadScore']
        ],
        group: ['assignedTo', 'assignedUser.id', 'assignedUser.name'],
        order: [[Customer.sequelize.fn('COUNT', Customer.sequelize.col('Customer.id')), 'DESC']],
        limit: 10
      })
    ]);

    return {
      overview: {
        totalCustomers,
        newLeads,
        convertedCustomers,
        conversionRate: newLeads > 0 ? ((convertedCustomers / newLeads) * 100).toFixed(2) : 0
      },
      leadsBySource: leadsBySource.map(item => ({
        source: item.leadSource,
        count: parseInt(item.dataValues.count)
      })),
      leadsByStatus: leadsByStatus.map(item => ({
        status: item.status,
        count: parseInt(item.dataValues.count)
      })),
      topPerformers: topPerformers.map(item => ({
        userId: item.assignedTo,
        userName: item.assignedUser?.name,
        customerCount: parseInt(item.dataValues.customerCount),
        avgLeadScore: parseFloat(item.dataValues.avgLeadScore || 0).toFixed(1)
      }))
    };
  }

  /**
   * Calculate lead score based on customer data
   */
  calculateLeadScore(customerData) {
    let score = 0;

    // Company size scoring
    const companySizeScores = {
      '1-10': 10,
      '11-50': 20,
      '51-200': 30,
      '201-1000': 40,
      '1000+': 50
    };
    score += companySizeScores[customerData.companySize] || 0;

    // Budget scoring
    if (customerData.monthlyEnergyBudget) {
      if (customerData.monthlyEnergyBudget > 10000) score += 30;
      else if (customerData.monthlyEnergyBudget > 5000) score += 20;
      else if (customerData.monthlyEnergyBudget > 1000) score += 10;
    }

    // Industry scoring
    const highValueIndustries = ['manufacturing', 'healthcare', 'retail', 'hospitality'];
    if (highValueIndustries.includes(customerData.industry?.toLowerCase())) {
      score += 15;
    }

    // Contact completeness
    if (customerData.phone) score += 5;
    if (customerData.companyName) score += 5;

    return Math.min(score, 100); // Cap at 100
  }

  /**
   * Get date filter for time range
   */
  getDateFilter(timeRange) {
    const now = new Date();
    const ranges = {
      '7d': new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      '30d': new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
      '90d': new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000),
      '1y': new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
    };
    return ranges[timeRange] || ranges['30d'];
  }
}

module.exports = new CustomerService();

const swaggerJsdoc = require('swagger-jsdoc');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'HLenergy API',
      version: '1.0.0',
      description: `
        HLenergy Backend API for energy consultation services.

        ## API Versioning

        This API supports versioning through multiple methods:

        ### URL Path Versioning (Recommended)
        Include the version in the URL path:
        - \`/api/v1/auth/login\` - Version 1
        - \`/api/v2/auth/login\` - Version 2 (when available)

        ### Header Versioning
        Use the Accept header to specify the version:
        - \`Accept: application/vnd.hlenergy.v1+json\`
        - \`Accept: application/vnd.hlenergy.v2+json\`

        ### Default Behavior
        If no version is specified, the API defaults to v1.

        ### Version Information
        All responses include version headers:
        - \`API-Version\`: Current version used
        - \`API-Latest-Version\`: Latest available version
        - \`API-Supported-Versions\`: All supported versions
        - \`API-Deprecated\`: Whether the current version is deprecated

        ### Legacy Support
        Non-versioned endpoints (e.g., \`/api/auth/login\`) are maintained for backward compatibility but are deprecated.
      `,
      contact: {
        name: 'HLenergy Team',
        email: '<EMAIL>',
      },
    },
    servers: [
      {
        url: process.env.NODE_ENV === 'production' 
          ? 'https://api.hlenergy.com' 
          : `http://localhost:${process.env.PORT || 3001}`,
        description: process.env.NODE_ENV === 'production' ? 'Production server' : 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'Enter JWT token',
        },
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              description: 'User ID',
            },
            name: {
              type: 'string',
              description: 'User full name',
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'User email address',
            },
            role: {
              type: 'string',
              enum: ['admin', 'staff', 'client'],
              description: 'User role',
            },
            created_at: {
              type: 'string',
              format: 'date-time',
              description: 'Account creation timestamp',
            },
          },
        },
        AuthResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
            },
            data: {
              type: 'object',
              properties: {
                user: {
                  $ref: '#/components/schemas/User',
                },
                token: {
                  type: 'string',
                  description: 'JWT access token',
                },
                refreshToken: {
                  type: 'string',
                  description: 'JWT refresh token',
                },
              },
            },
            message: {
              type: 'string',
            },
          },
        },
        ContactSubmission: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              description: 'Submission ID',
            },
            name: {
              type: 'string',
              description: 'Contact name',
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'Contact email',
            },
            phone: {
              type: 'string',
              description: 'Contact phone number',
            },
            message: {
              type: 'string',
              description: 'Contact message',
            },
            status: {
              type: 'string',
              enum: ['new', 'in_progress', 'resolved'],
              description: 'Submission status',
            },
            created_at: {
              type: 'string',
              format: 'date-time',
              description: 'Submission timestamp',
            },
          },
        },
        Error: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false,
            },
            error: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  description: 'Error message',
                },
                details: {
                  type: 'array',
                  items: {
                    type: 'object',
                  },
                  description: 'Validation error details',
                },
              },
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: [
    './shared/routes/**/*.js',
    './shared/index.js',
    './shared/routes/v1/*.js',
    './shared/routes/*.js'
  ], // paths to files containing OpenAPI definitions
};

const specs = swaggerJsdoc(options);

module.exports = specs;

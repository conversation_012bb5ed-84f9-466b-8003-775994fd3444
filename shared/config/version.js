const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Server Version Configuration
 * Tracks server version, build info, and deployment details
 */

// Read package.json for version info
const packageJsonPath = path.join(__dirname, '../../package.json');
let packageInfo = {};
try {
  packageInfo = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
} catch (error) {
  console.warn('Could not read package.json:', error.message);
}

// Get Git information
const getGitInfo = () => {
  try {
    const gitCommit = execSync('git rev-parse HEAD', { encoding: 'utf8', stdio: ['ignore', 'pipe', 'ignore'] }).trim();
    const gitBranch = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8', stdio: ['ignore', 'pipe', 'ignore'] }).trim();
    const gitCommitShort = gitCommit.substring(0, 7);
    const gitCommitDate = execSync('git log -1 --format=%ci', { encoding: 'utf8', stdio: ['ignore', 'pipe', 'ignore'] }).trim();
    const gitAuthor = execSync('git log -1 --format=%an', { encoding: 'utf8', stdio: ['ignore', 'pipe', 'ignore'] }).trim();

    // Try to get tag, but don't fail if there are no tags
    let gitTag = null;
    try {
      gitTag = execSync('git describe --tags --abbrev=0', { encoding: 'utf8', stdio: ['ignore', 'pipe', 'ignore'] }).trim();
    } catch (tagError) {
      // No tags available, that's okay
      gitTag = 'v1.0.0'; // Use the tag we created
    }

    return {
      commit: gitCommit,
      commit_short: gitCommitShort,
      branch: gitBranch,
      tag: gitTag,
      commit_date: gitCommitDate,
      author: gitAuthor,
    };
  } catch (error) {
    return {
      commit: 'unknown',
      commit_short: 'unknown',
      branch: 'unknown',
      tag: null,
      commit_date: 'unknown',
      author: 'unknown',
      error: 'Git not available or not a git repository',
    };
  }
};

// Get build information
const getBuildInfo = () => {
  const buildTime = new Date().toISOString();
  const nodeVersion = process.version;
  const platform = process.platform;
  const arch = process.arch;
  
  return {
    build_time: buildTime,
    node_version: nodeVersion,
    platform,
    architecture: arch,
    environment: process.env.NODE_ENV || 'development',
  };
};

// Get deployment information
const getDeploymentInfo = () => {
  return {
    deployed_at: process.env.DEPLOY_TIME || new Date().toISOString(),
    deployed_by: process.env.DEPLOY_USER || process.env.USER || 'unknown',
    deployment_id: process.env.DEPLOYMENT_ID || `local-${Date.now()}`,
    environment: process.env.NODE_ENV || 'development',
    server_instance: process.env.SERVER_INSTANCE || 'local',
    region: process.env.DEPLOY_REGION || 'local',
  };
};

// Get runtime information
const getRuntimeInfo = () => {
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();
  
  return {
    uptime_seconds: Math.floor(uptime),
    uptime_human: formatUptime(uptime),
    memory_usage: {
      rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
      heap_total: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
      heap_used: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
      external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`,
    },
    pid: process.pid,
    started_at: new Date(Date.now() - uptime * 1000).toISOString(),
  };
};

// Format uptime in human readable format
const formatUptime = (seconds) => {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  const parts = [];
  if (days > 0) parts.push(`${days}d`);
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0) parts.push(`${minutes}m`);
  if (secs > 0) parts.push(`${secs}s`);
  
  return parts.join(' ') || '0s';
};

// Get dependency versions
const getDependencyVersions = () => {
  const dependencies = packageInfo.dependencies || {};
  const devDependencies = packageInfo.devDependencies || {};
  
  // Key dependencies to track
  const keyDeps = [
    'express',
    'jsonwebtoken',
    'bcryptjs',
    'helmet',
    'cors',
    'swagger-jsdoc',
    'swagger-ui-express',
    'express-validator',
    'express-rate-limit',
  ];
  
  const keyDevDeps = [
    'nodemon',
    'typescript',
    '@types/node',
  ];
  
  const tracked = {};
  
  keyDeps.forEach(dep => {
    if (dependencies[dep]) {
      tracked[dep] = dependencies[dep];
    }
  });
  
  keyDevDeps.forEach(dep => {
    if (devDependencies[dep]) {
      tracked[`${dep} (dev)`] = devDependencies[dep];
    }
  });
  
  return tracked;
};

// Main version configuration
const VERSION_CONFIG = {
  // Server version info
  server: {
    name: packageInfo.name || 'hlenergy-api',
    version: packageInfo.version || '1.0.0',
    description: packageInfo.description || 'HLenergy Backend API',
    author: packageInfo.author || 'HLenergy Team',
  },
  
  // API versioning
  api: {
    current: 'v1',
    supported: ['v1', 'v2'],
    latest: 'v1',
    default: 'v1',
  },
  
  // Git information
  git: getGitInfo(),
  
  // Build information
  build: getBuildInfo(),
  
  // Deployment information
  deployment: getDeploymentInfo(),
  
  // Dependencies
  dependencies: getDependencyVersions(),
  
  // Feature flags
  features: {
    authentication: true,
    contact_forms: true,
    api_versioning: true,
    swagger_docs: true,
    redoc_docs: true,
    rate_limiting: true,
    cors_enabled: true,
    helmet_security: true,
  },
  
  // Configuration
  config: {
    port: process.env.PORT || 3001,
    environment: process.env.NODE_ENV || 'development',
    log_level: process.env.LOG_LEVEL || 'info',
    jwt_expires_in: process.env.JWT_EXPIRES_IN || '7d',
    rate_limit_window: process.env.RATE_LIMIT_WINDOW_MS || '900000',
    rate_limit_max: process.env.RATE_LIMIT_MAX_REQUESTS || '100',
  },
};

// Function to get current runtime info (dynamic)
const getCurrentVersionInfo = () => {
  return {
    ...VERSION_CONFIG,
    runtime: getRuntimeInfo(),
    timestamp: new Date().toISOString(),
  };
};

// Function to get version summary
const getVersionSummary = () => {
  const git = getGitInfo();
  return {
    server_version: VERSION_CONFIG.server.version,
    api_version: VERSION_CONFIG.api.current,
    git_commit: git.commit_short,
    git_branch: git.branch,
    environment: VERSION_CONFIG.build.environment,
    uptime: formatUptime(process.uptime()),
    timestamp: new Date().toISOString(),
  };
};

module.exports = {
  VERSION_CONFIG,
  getCurrentVersionInfo,
  getVersionSummary,
  getRuntimeInfo,
  getGitInfo,
  getBuildInfo,
  getDeploymentInfo,
  getDependencyVersions,
};

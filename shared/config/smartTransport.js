const winston = require('winston');
const Transport = require('winston-transport');
const DailyRotateFile = require('winston-daily-rotate-file');
const DatabaseTransport = require('./databaseTransport');
const path = require('path');
const fs = require('fs');

/**
 * Smart Transport for Winston
 * Tries database first, falls back to file logging if database fails
 */

class SmartTransport extends Transport {
  constructor(opts = {}) {
    super(opts);
    
    this.name = 'smart';
    this.level = opts.level || 'info';
    this.silent = opts.silent || false;
    
    // Initialize database transport
    this.databaseTransport = new DatabaseTransport({
      level: this.level,
    });
    
    // Initialize file transports as fallback
    this.initializeFileTransports();
    
    // Statistics
    this.stats = {
      totalLogs: 0,
      databaseSuccess: 0,
      fileFallback: 0,
      errors: 0,
    };
  }
  
  initializeFileTransports() {
    // Ensure logs directory exists
    const logsDir = path.join(__dirname, '../../logs');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    
    // Create file transports for fallback
    this.fileTransports = {
      // Combined logs - all levels
      combined: new DailyRotateFile({
        filename: path.join(logsDir, 'fallback-combined-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        maxSize: '20m',
        maxFiles: '7d', // Keep fallback logs for shorter time
        zippedArchive: true,
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.json()
        ),
      }),
      
      // Error logs only
      error: new DailyRotateFile({
        filename: path.join(logsDir, 'fallback-error-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        level: 'error',
        maxSize: '10m',
        maxFiles: '14d',
        zippedArchive: true,
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.json()
        ),
      }),
    };
  }
  
  async log(info, callback) {
    setImmediate(() => {
      this.emit('logged', info);
    });
    
    this.stats.totalLogs++;
    
    try {
      // Try database first
      const success = await this.tryDatabaseLog(info);
      
      if (success) {
        this.stats.databaseSuccess++;
        if (callback) callback();
        return true;
      }
      
      // Fallback to file logging
      await this.fallbackToFile(info);
      this.stats.fileFallback++;
      
      if (callback) callback();
      return true;
      
    } catch (error) {
      this.stats.errors++;
      console.error('Smart transport error:', error);
      
      // Last resort - try file logging anyway
      try {
        await this.fallbackToFile(info);
      } catch (fileError) {
        console.error('File fallback also failed:', fileError);
      }
      
      if (callback) callback(error);
      return false;
    }
  }
  
  async tryDatabaseLog(info) {
    try {
      // Check if database transport is ready
      if (!this.databaseTransport.isReady || !this.databaseTransport.LogModel) {
        return false;
      }
      
      // Prepare log data
      const logData = this.databaseTransport.prepareLogData(info);
      
      // Try to save to database
      const success = await this.databaseTransport.saveToDatabase(logData);
      return success;
      
    } catch (error) {
      console.error('Database logging attempt failed:', error.message);
      return false;
    }
  }
  
  async fallbackToFile(info) {
    try {
      // Log to combined file
      this.fileTransports.combined.log(info, () => {});
      
      // Log errors to separate error file
      if (info.level === 'error') {
        this.fileTransports.error.log(info, () => {});
      }
      
      // Add fallback indicator to the log
      const fallbackInfo = {
        ...info,
        fallback: true,
        fallbackReason: 'Database unavailable',
        fallbackTimestamp: new Date().toISOString(),
      };
      
      // Log the fallback event (but don't create infinite loop)
      if (info.type !== 'fallback') {
        console.warn('Logging fallback to file:', {
          level: info.level,
          message: info.message,
          type: info.type,
        });
      }
      
    } catch (error) {
      console.error('File fallback failed:', error);
      throw error;
    }
  }
  
  // Get transport statistics
  getStats() {
    const successRate = this.stats.totalLogs > 0
      ? (this.stats.databaseSuccess / this.stats.totalLogs * 100).toFixed(2)
      : 0;

    const fallbackRate = this.stats.totalLogs > 0
      ? (this.stats.fileFallback / this.stats.totalLogs * 100).toFixed(2)
      : 0;

    // Get circuit breaker status if available
    const circuitBreakerStatus = this.databaseTransport.getCircuitBreakerStatus
      ? this.databaseTransport.getCircuitBreakerStatus()
      : null;

    return {
      ...this.stats,
      databaseSuccessRate: `${successRate}%`,
      fileFallbackRate: `${fallbackRate}%`,
      databaseReady: this.databaseTransport.isReady,
      pendingLogs: this.databaseTransport.pendingLogs.length,
      circuitBreaker: circuitBreakerStatus,
    };
  }
  
  // Force database reconnection
  async reconnectDatabase() {
    try {
      this.databaseTransport.isReady = false;
      await this.databaseTransport.initializeModel();
      return true;
    } catch (error) {
      console.error('Database reconnection failed:', error);
      return false;
    }
  }
  
  // Get recent logs (tries database first, then files)
  async getRecentLogs(limit = 100, level = null) {
    try {
      // Try database first
      if (this.databaseTransport.isReady) {
        return await this.databaseTransport.getRecentLogs(limit, level);
      }
      
      // Fallback to reading file logs
      return await this.getRecentFileLogs(limit, level);
      
    } catch (error) {
      console.error('Error getting recent logs:', error);
      return [];
    }
  }
  
  async getRecentFileLogs(limit = 100, level = null) {
    try {
      const logsDir = path.join(__dirname, '../../logs');
      const files = fs.readdirSync(logsDir)
        .filter(file => file.startsWith('fallback-combined-') && file.endsWith('.log'))
        .sort()
        .reverse();
      
      if (files.length === 0) {
        return [];
      }
      
      // Read the most recent file
      const latestFile = path.join(logsDir, files[0]);
      const content = fs.readFileSync(latestFile, 'utf8');
      const lines = content.split('\n').filter(line => line.trim());
      
      // Parse and filter logs
      const logs = lines
        .map(line => {
          try {
            return JSON.parse(line);
          } catch {
            return null;
          }
        })
        .filter(log => log !== null)
        .filter(log => !level || log.level === level)
        .slice(-limit)
        .reverse();
      
      return logs;
      
    } catch (error) {
      console.error('Error reading file logs:', error);
      return [];
    }
  }
  
  // Cleanup old fallback files
  async cleanupFallbackFiles(daysToKeep = 7) {
    try {
      const logsDir = path.join(__dirname, '../../logs');
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      
      const files = fs.readdirSync(logsDir)
        .filter(file => file.startsWith('fallback-'))
        .filter(file => {
          const filePath = path.join(logsDir, file);
          const stats = fs.statSync(filePath);
          return stats.mtime < cutoffDate;
        });
      
      let deletedCount = 0;
      files.forEach(file => {
        const filePath = path.join(logsDir, file);
        fs.unlinkSync(filePath);
        deletedCount++;
      });
      
      return deletedCount;
      
    } catch (error) {
      console.error('Error cleaning up fallback files:', error);
      return 0;
    }
  }
}

module.exports = SmartTransport;

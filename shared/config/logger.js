const winston = require('winston');
const SmartTransport = require('./smartTransport');

/**
 * Logging Configuration for HLenergy API
 * Provides structured logging with multiple transports and log levels
 */

// Create smart transport instance
const smartTransport = new SmartTransport({
  level: process.env.LOG_LEVEL || 'info',
});

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: {
    service: 'hlenergy-api',
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
  },
  transports: [
    // Smart transport - tries database first, falls back to files
    smartTransport,
  ],
});

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
    level: 'debug',
  }));
}

// Custom log levels for HTTP requests
winston.addColors({
  error: 'red',
  warn: 'yellow',
  info: 'cyan',
  http: 'magenta',
  debug: 'green',
});

// Helper functions for structured logging
const logHelpers = {
  // Log HTTP requests
  logRequest: (req, res, responseTime) => {
    // Get real IP address (use realIP if available, fallback to req.ip)
    const getRealIP = (req) => {
      return req.realIP ||
             req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
             req.headers['x-real-ip'] ||
             req.connection?.remoteAddress ||
             req.socket?.remoteAddress ||
             req.ip ||
             'unknown';
    };

    const logData = {
      type: 'request',
      method: req.method || 'unknown',
      url: req.originalUrl || req.url || 'unknown',
      ip: getRealIP(req),
      userAgent: req.get('User-Agent') || 'unknown',
      statusCode: res.statusCode || 0,
      responseTime: responseTime ? `${responseTime}ms` : 'unknown',
      contentLength: res.get('Content-Length') || '0',
      apiVersion: req.apiVersion || 'unknown',
      userId: req.user?.id || null,
      requestId: req.requestId || 'unknown',
      timestamp: new Date().toISOString(),
      // Additional useful fields
      referer: req.get('Referer') || null,
      origin: req.get('Origin') || null,
      host: req.get('Host') || 'unknown',
      protocol: req.protocol || 'unknown',
      httpVersion: req.httpVersion || 'unknown',
    };

    // Add request body for POST/PUT/PATCH (excluding sensitive data)
    if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
      const body = { ...req.body };
      // Remove sensitive fields
      delete body.password;
      delete body.token;
      delete body.refreshToken;
      delete body.pin;
      delete body.currentPassword;
      delete body.newPassword;
      logData.requestBody = Object.keys(body).length > 0 ? body : null;
    }

    // Add query parameters
    if (req.query && Object.keys(req.query).length > 0) {
      logData.queryParams = req.query;
    }

    // Add route parameters if available
    if (req.params && Object.keys(req.params).length > 0) {
      logData.routeParams = req.params;
    }

    // Determine log level based on status code
    let level = 'http';
    if (res.statusCode >= 500) level = 'error';
    else if (res.statusCode >= 400) level = 'warn';
    else if (res.statusCode >= 300) level = 'info';

    logger.log(level, `${req.method} ${req.originalUrl} ${res.statusCode} - ${responseTime}ms`, logData);
  },
  
  // Log errors with context
  logError: (error, req = null, additionalContext = {}) => {
    // Get real IP address helper
    const getRealIP = (req) => {
      if (!req) return 'unknown';
      return req.realIP ||
             req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
             req.headers['x-real-ip'] ||
             req.connection?.remoteAddress ||
             req.socket?.remoteAddress ||
             req.ip ||
             'unknown';
    };

    const errorData = {
      type: 'error',
      name: error.name || 'UnknownError',
      message: error.message || 'No error message provided',
      stack: error.stack || 'No stack trace available',
      code: error.code || null,
      statusCode: error.statusCode || error.status || null,
      timestamp: new Date().toISOString(),
      ...additionalContext,
    };

    // Add request context if available
    if (req) {
      errorData.request = {
        method: req.method || 'unknown',
        url: req.originalUrl || req.url || 'unknown',
        ip: getRealIP(req),
        userAgent: req.get('User-Agent') || 'unknown',
        userId: req.user?.id || null,
        apiVersion: req.apiVersion || 'unknown',
        requestId: req.requestId || 'unknown',
        referer: req.get('Referer') || null,
        origin: req.get('Origin') || null,
      };

      // Add request body for debugging (excluding sensitive data)
      if (req.body && Object.keys(req.body).length > 0) {
        const body = { ...req.body };
        // Remove sensitive fields
        delete body.password;
        delete body.token;
        delete body.refreshToken;
        delete body.pin;
        delete body.currentPassword;
        delete body.newPassword;
        errorData.request.body = body;
      }
    }

    logger.error('Application Error', errorData);
  },
  
  // Log database operations
  logDatabase: (operation, details = {}) => {
    logger.debug('Database Operation', {
      type: 'database',
      operation,
      timestamp: new Date().toISOString(),
      ...details,
    });
  },
  
  // Log authentication events
  logAuth: (event, userId, details = {}) => {
    logger.info('Authentication Event', {
      type: 'authentication',
      event,
      userId,
      timestamp: new Date().toISOString(),
      ...details,
    });
  },
  
  // Log security events
  logSecurity: (event, details = {}) => {
    logger.warn('Security Event', {
      type: 'security',
      event,
      timestamp: new Date().toISOString(),
      ...details,
    });
  },
  
  // Log performance metrics
  logPerformance: (metric, value, details = {}) => {
    logger.info('Performance Metric', {
      type: 'performance',
      metric,
      value,
      timestamp: new Date().toISOString(),
      ...details,
    });
  },

  // Log Socket.io events
  logSocket: (event, socketId, userId, details = {}) => {
    logger.info('Socket.io Event', {
      type: 'socket',
      event,
      socketId,
      userId,
      timestamp: new Date().toISOString(),
      ...details,
    });
  },

  // Log Socket.io connections
  logSocketConnection: (socketId, userId, userData, connectionType = 'connect') => {
    const logData = {
      type: 'socket_connection',
      event: connectionType,
      socketId,
      userId,
      userData: {
        email: userData.email,
        role: userData.role,
        name: userData.name,
        isTemporary: userData.isTemporary || false,
      },
      timestamp: new Date().toISOString(),
    };

    logger.info('Socket.io Connection', logData);
  },

  // Log Socket.io authentication attempts
  logSocketAuth: (socketId, success, details = {}) => {
    const level = success ? 'info' : 'warn';
    logger.log(level, 'Socket.io Authentication', {
      type: 'socket_authentication',
      socketId,
      success,
      timestamp: new Date().toISOString(),
      ...details,
    });
  },

  // Log Socket.io room activities
  logSocketRoom: (event, socketId, userId, roomId, details = {}) => {
    logger.debug('Socket.io Room Activity', {
      type: 'socket_room',
      event,
      socketId,
      userId,
      roomId,
      timestamp: new Date().toISOString(),
      ...details,
    });
  },

  // Log Socket.io real-time analytics
  logSocketAnalytics: (event, socketId, userId, analyticsData) => {
    logger.info('Socket.io Analytics', {
      type: 'socket_analytics',
      event,
      socketId,
      userId,
      analyticsData,
      timestamp: new Date().toISOString(),
    });
  },

  // Log Socket.io communication events
  logSocketCommunication: (event, socketId, userId, communicationData) => {
    logger.info('Socket.io Communication', {
      type: 'socket_communication',
      event,
      socketId,
      userId,
      communicationData: {
        ...communicationData,
        // Remove sensitive content for logging
        content: communicationData.content ? '[CONTENT_LOGGED]' : undefined,
      },
      timestamp: new Date().toISOString(),
    });
  },

  // Get logging statistics
  getLoggingStats: () => {
    return smartTransport.getStats();
  },

  // Get recent logs (tries database first, then files)
  getRecentLogs: async (limit = 100, level = null) => {
    return await smartTransport.getRecentLogs(limit, level);
  },

  // Force database reconnection
  reconnectDatabase: async () => {
    return await smartTransport.reconnectDatabase();
  },

  // Cleanup old fallback files
  cleanupFallbackFiles: async (daysToKeep = 7) => {
    return await smartTransport.cleanupFallbackFiles(daysToKeep);
  },
};

// Export logger and helpers
module.exports = {
  logger,
  ...logHelpers,
};

const winston = require('winston');
const Transport = require('winston-transport');

/**
 * Database Transport for Winston
 * Saves logs to the database for better querying and analytics
 */

class DatabaseTransport extends Transport {
  constructor(opts = {}) {
    super(opts);

    // Store options
    this.name = 'database';
    this.level = opts.level || 'info';
    this.silent = opts.silent || false;
    this.tableName = opts.tableName || 'logs';

    // We'll set the model after it's initialized
    this.LogModel = null;
    this.isReady = false;
    this.pendingLogs = [];

    // Circuit breaker for database protection
    this.circuitBreaker = {
      state: 'CLOSED', // CLOSED, OPEN, HALF_OPEN
      failureCount: 0,
      failureThreshold: 5, // Open circuit after 5 consecutive failures
      timeout: 30000, // 30 seconds before trying again
      lastFailureTime: null,
      successCount: 0,
      halfOpenMaxAttempts: 3 // Max attempts in HALF_OPEN state
    };

    // Rate limiting to prevent spam
    this.rateLimiter = {
      requests: [],
      maxRequests: 10, // Max 10 requests per window
      windowMs: 5000, // 5 second window
      lastCleanup: Date.now()
    };

    // Initialize when models are ready
    this.initializeModel();
  }
  
  async initializeModel() {
    try {
      // Wait a bit for models to be initialized
      setTimeout(async () => {
        try {
          const { Log } = require('../models');
          this.LogModel = Log;
          this.isReady = true;
          
          // Process any pending logs
          await this.processPendingLogs();
        } catch (error) {
          console.warn('Database transport: Log model not available yet, will retry...');
          // Retry in 5 seconds
          setTimeout(() => this.initializeModel(), 5000);
        }
      }, 1000);
    } catch (error) {
      console.error('Database transport initialization error:', error);
    }
  }
  
  async processPendingLogs() {
    if (this.pendingLogs.length > 0) {
      console.log(`Processing ${this.pendingLogs.length} pending logs...`);
      
      for (const logData of this.pendingLogs) {
        try {
          await this.saveToDatabase(logData);
        } catch (error) {
          console.error('Error processing pending log:', error);
        }
      }
      
      this.pendingLogs = [];
    }
  }
  
  log(info, callback) {
    setImmediate(() => {
      this.emit('logged', info);
    });
    
    // Prepare log data
    const logData = this.prepareLogData(info);
    
    if (!this.isReady || !this.LogModel) {
      // Store in pending logs if model not ready
      this.pendingLogs.push(logData);
      if (callback) callback();
      return true;
    }
    
    // Save to database
    this.saveToDatabase(logData)
      .then(() => {
        if (callback) callback();
      })
      .catch((error) => {
        console.error('Database transport error:', error);
        if (callback) callback(error);
      });
    
    return true;
  }
  
  prepareLogData(info) {
    const logData = {
      level: info.level,
      message: info.message,
      created_at: new Date(),
    };

    // Extract metadata
    if (info.type) logData.type = info.type;

    // Handle user_id - only set if it's a valid integer (real user)
    // For temporary users (string IDs), store in metadata instead
    if (info.userId) {
      if (this.isTemporaryUserId(info.userId)) {
        // Don't set user_id for temporary users (would cause DB error)
        logData.user_id = null;
      } else {
        // Real user ID - should be integer
        const userId = parseInt(info.userId);
        if (!isNaN(userId)) {
          logData.user_id = userId;
        } else {
          logData.user_id = null;
        }
      }
    }

    if (info.requestId) logData.request_id = info.requestId;
    if (info.ip) logData.ip_address = info.ip;
    if (info.userAgent) logData.user_agent = info.userAgent;
    if (info.method) logData.method = info.method;
    if (info.url) logData.url = info.url;
    if (info.statusCode) logData.status_code = info.statusCode;
    if (info.responseTime) {
      // Extract numeric value from responseTime string
      const timeMatch = info.responseTime.toString().match(/(\d+)/);
      if (timeMatch) {
        logData.response_time = parseInt(timeMatch[1]);
      }
    }
    if (info.stack) logData.error_stack = info.stack;

    // Store additional metadata
    const metadata = { ...info };

    // For temporary users, store the temporary user ID in metadata
    if (info.userId && this.isTemporaryUserId(info.userId)) {
      metadata.temporaryUserId = info.userId;
    }

    // Remove fields that have dedicated columns
    delete metadata.level;
    delete metadata.message;
    delete metadata.timestamp;
    delete metadata.type;
    delete metadata.userId;
    delete metadata.requestId;
    delete metadata.ip;
    delete metadata.userAgent;
    delete metadata.method;
    delete metadata.url;
    delete metadata.statusCode;
    delete metadata.responseTime;
    delete metadata.stack;
    delete metadata.service;
    delete metadata.version;
    delete metadata.environment;

    // Only store metadata if there's something left
    if (Object.keys(metadata).length > 0) {
      logData.metadata = metadata;
    }

    return logData;
  }

  // Helper method to detect temporary user IDs
  isTemporaryUserId(userId) {
    if (typeof userId !== 'string') {
      return false;
    }

    // Check for temporary user ID patterns
    return userId.startsWith('temp_') ||
           userId.startsWith('temp-') ||
           userId.includes('temp') && userId.includes('-');
  }

  // Circuit breaker methods
  canMakeRequest() {
    const now = Date.now();

    switch (this.circuitBreaker.state) {
      case 'CLOSED':
        return true;

      case 'OPEN':
        // Check if timeout has passed
        if (now - this.circuitBreaker.lastFailureTime >= this.circuitBreaker.timeout) {
          this.circuitBreaker.state = 'HALF_OPEN';
          this.circuitBreaker.successCount = 0;
          console.log('Database circuit breaker: OPEN -> HALF_OPEN');
          return true;
        }
        return false;

      case 'HALF_OPEN':
        return this.circuitBreaker.successCount < this.circuitBreaker.halfOpenMaxAttempts;

      default:
        return false;
    }
  }

  onSuccess() {
    switch (this.circuitBreaker.state) {
      case 'HALF_OPEN':
        this.circuitBreaker.successCount++;
        if (this.circuitBreaker.successCount >= this.circuitBreaker.halfOpenMaxAttempts) {
          this.circuitBreaker.state = 'CLOSED';
          this.circuitBreaker.failureCount = 0;
          console.log('Database circuit breaker: HALF_OPEN -> CLOSED');
        }
        break;

      case 'CLOSED':
        this.circuitBreaker.failureCount = 0;
        break;
    }
  }

  onFailure(error) {
    this.circuitBreaker.failureCount++;
    this.circuitBreaker.lastFailureTime = Date.now();

    // Check if we should open the circuit
    if (this.circuitBreaker.failureCount >= this.circuitBreaker.failureThreshold) {
      this.circuitBreaker.state = 'OPEN';
      console.warn(`Database circuit breaker: ${this.circuitBreaker.state === 'HALF_OPEN' ? 'HALF_OPEN' : 'CLOSED'} -> OPEN (${this.circuitBreaker.failureCount} failures)`);
      console.warn(`Database will be unavailable for ${this.circuitBreaker.timeout / 1000} seconds`);
    }
  }

  // Rate limiting methods
  isWithinRateLimit() {
    this.cleanupOldRequests();
    return this.rateLimiter.requests.length < this.rateLimiter.maxRequests;
  }

  addRequest() {
    const now = Date.now();
    this.rateLimiter.requests.push(now);
  }

  cleanupOldRequests() {
    const now = Date.now();
    const cutoff = now - this.rateLimiter.windowMs;

    // Only cleanup if it's been a while since last cleanup (performance optimization)
    if (now - this.rateLimiter.lastCleanup > 1000) {
      this.rateLimiter.requests = this.rateLimiter.requests.filter(time => time > cutoff);
      this.rateLimiter.lastCleanup = now;
    }
  }

  // Get circuit breaker status
  getCircuitBreakerStatus() {
    return {
      state: this.circuitBreaker.state,
      failureCount: this.circuitBreaker.failureCount,
      failureThreshold: this.circuitBreaker.failureThreshold,
      lastFailureTime: this.circuitBreaker.lastFailureTime,
      timeUntilRetry: this.circuitBreaker.state === 'OPEN'
        ? Math.max(0, this.circuitBreaker.timeout - (Date.now() - this.circuitBreaker.lastFailureTime))
        : 0,
      rateLimitStatus: {
        currentRequests: this.rateLimiter.requests.length,
        maxRequests: this.rateLimiter.maxRequests,
        windowMs: this.rateLimiter.windowMs
      }
    };
  }
  
  async saveToDatabase(logData) {
    // Check circuit breaker state
    if (!this.canMakeRequest()) {
      console.warn('Database circuit breaker is OPEN - skipping database write');
      return false;
    }

    // Check rate limiting
    if (!this.isWithinRateLimit()) {
      console.warn('Database rate limit exceeded - skipping database write');
      return false;
    }

    try {
      if (!this.LogModel) {
        throw new Error('Log model not available');
      }

      // Add request to rate limiter
      this.addRequest();

      // Set a timeout for the database operation
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Database operation timeout')), 10000); // 10 second timeout
      });

      const createPromise = this.LogModel.create(logData);

      // Race between the actual operation and timeout
      await Promise.race([createPromise, timeoutPromise]);

      // Success - reset circuit breaker
      this.onSuccess();
      return true;

    } catch (error) {
      // Handle failure - update circuit breaker
      this.onFailure(error);

      // Don't throw errors for database logging to avoid infinite loops
      console.error('Failed to save log to database:', error.message);

      // If it's a connection error, add back to pending and mark as not ready
      if (error.name === 'SequelizeConnectionError' ||
          error.name === 'SequelizeConnectionRefusedError' ||
          error.name === 'SequelizeDatabaseError' ||
          error.message.includes('timeout')) {
        this.pendingLogs.push(logData);
        this.isReady = false;
        // Try to reinitialize after a delay
        setTimeout(() => this.initializeModel(), 10000); // Increased delay
      }

      return false; // Failed - should fallback to file logging
    }
  }
  
  // Method to get recent logs from database
  async getRecentLogs(limit = 100, level = null) {
    if (!this.LogModel) {
      return [];
    }
    
    try {
      const where = {};
      if (level) where.level = level;
      
      return await this.LogModel.findAll({
        where,
        order: [['created_at', 'DESC']],
        limit,
        include: [
          {
            association: 'user',
            attributes: ['id', 'name', 'email'],
          },
        ],
      });
    } catch (error) {
      console.error('Error fetching logs from database:', error);
      return [];
    }
  }
  
  // Method to get log statistics
  async getLogStats() {
    if (!this.LogModel) {
      return null;
    }
    
    try {
      return await this.LogModel.getLogStatistics();
    } catch (error) {
      console.error('Error fetching log statistics:', error);
      return null;
    }
  }
  
  // Method to cleanup old logs
  async cleanupOldLogs(daysToKeep = 30) {
    if (!this.LogModel) {
      return 0;
    }
    
    try {
      return await this.LogModel.cleanupOldLogs(daysToKeep);
    } catch (error) {
      console.error('Error cleaning up old logs:', error);
      return 0;
    }
  }
}

module.exports = DatabaseTransport;

'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('push_subscriptions', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
      },
      endpoint: {
        type: Sequelize.TEXT, // Use TEXT for long URLs, no direct indexing
        allowNull: false
      },
      endpointHash: {
        type: Sequelize.STRING(64), // SHA-256 hash for indexing and uniqueness
        allowNull: false,
        unique: true
      },
      p256dhKey: {
        type: Sequelize.STRING(255), // Reduced size - base64 keys are ~88 chars
        allowNull: false
      },
      authKey: {
        type: Sequelize.STRING(255), // Reduced size - base64 keys are ~24 chars
        allowNull: false
      },
      userRole: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: 'user'
      },
      deviceInfo: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: {}
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        allowNull: false
      },
      lastUsed: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        allowNull: false
      },
      failureCount: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        allowNull: false
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes for better performance (with duplicate checking)
    try {
      await queryInterface.addIndex('push_subscriptions', ['userId'], {
        name: 'push_subscriptions_user_id_idx'
      });
    } catch (error) {
      if (!error.message.includes('Duplicate key name')) {
        throw error;
      }
      console.log('Index push_subscriptions_user_id_idx already exists, skipping...');
    }

    try {
      await queryInterface.addIndex('push_subscriptions', ['endpointHash'], {
        name: 'push_subscriptions_endpoint_hash_idx',
        unique: true
      });
    } catch (error) {
      if (!error.message.includes('Duplicate key name')) {
        throw error;
      }
      console.log('Index push_subscriptions_endpoint_hash_idx already exists, skipping...');
    }

    try {
      await queryInterface.addIndex('push_subscriptions', ['isActive'], {
        name: 'push_subscriptions_is_active_idx'
      });
    } catch (error) {
      if (!error.message.includes('Duplicate key name')) {
        throw error;
      }
      console.log('Index push_subscriptions_is_active_idx already exists, skipping...');
    }

    try {
      await queryInterface.addIndex('push_subscriptions', ['userRole'], {
        name: 'push_subscriptions_user_role_idx'
      });
    } catch (error) {
      if (!error.message.includes('Duplicate key name')) {
        throw error;
      }
      console.log('Index push_subscriptions_user_role_idx already exists, skipping...');
    }

    try {
      await queryInterface.addIndex('push_subscriptions', ['lastUsed'], {
        name: 'push_subscriptions_last_used_idx'
      });
    } catch (error) {
      if (!error.message.includes('Duplicate key name')) {
        throw error;
      }
      console.log('Index push_subscriptions_last_used_idx already exists, skipping...');
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('push_subscriptions');
  }
};

'use strict';

/**
 * MySQL-Compatible Push Subscriptions Migration
 * 
 * This migration handles MySQL's limitations with TEXT column indexing
 * by using appropriate column types and index strategies.
 */

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('push_subscriptions', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
      },
      endpoint: {
        type: Sequelize.STRING(2048), // VARCHAR(2048) - supports indexing
        allowNull: false,
        unique: true
      },
      endpointHash: {
        type: Sequelize.STRING(64), // SHA-256 hash for faster lookups
        allowNull: false,
        unique: true
      },
      p256dhKey: {
        type: Sequelize.STRING(512), // VARCHAR(512) - sufficient for base64 keys
        allowNull: false
      },
      authKey: {
        type: Sequelize.STRING(512), // VARCHAR(512) - sufficient for base64 keys
        allowNull: false
      },
      userRole: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: 'user'
      },
      deviceInfo: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: {}
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        allowNull: false
      },
      lastUsed: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        allowNull: false
      },
      failureCount: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        allowNull: false
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes for better performance
    await queryInterface.addIndex('push_subscriptions', ['userId'], {
      name: 'push_subscriptions_user_id_idx'
    });

    await queryInterface.addIndex('push_subscriptions', ['endpointHash'], {
      name: 'push_subscriptions_endpoint_hash_idx',
      unique: true
    });

    await queryInterface.addIndex('push_subscriptions', ['isActive'], {
      name: 'push_subscriptions_is_active_idx'
    });

    await queryInterface.addIndex('push_subscriptions', ['userRole'], {
      name: 'push_subscriptions_user_role_idx'
    });

    await queryInterface.addIndex('push_subscriptions', ['lastUsed'], {
      name: 'push_subscriptions_last_used_idx'
    });

    // Composite index for active subscriptions by user
    await queryInterface.addIndex('push_subscriptions', ['userId', 'isActive'], {
      name: 'push_subscriptions_user_active_idx'
    });

    // Composite index for role-based targeting
    await queryInterface.addIndex('push_subscriptions', ['userRole', 'isActive'], {
      name: 'push_subscriptions_role_active_idx'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('push_subscriptions');
  }
};

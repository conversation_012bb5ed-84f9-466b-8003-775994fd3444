-- Migration to update log type validation for Socket.io and memory management
-- This adds new log types for Socket.io monitoring and memory management

-- First, let's see the current constraint
SELECT CONSTRAINT_NAME, CHECK_CLAUSE 
FROM INFORMATION_SCHEMA.CHECK_CONSTRAINTS 
WHERE TABLE_NAME = 'logs' AND COLUMN_NAME = 'type';

-- Drop the existing check constraint if it exists
-- Note: MySQL doesn't use named check constraints in the same way, so we'll modify the column

-- Update the type column to allow new log types
-- This is a safe operation that expands the allowed values
ALTER TABLE logs MODIFY COLUMN type VARCHAR(50) NULL;

-- Add a comment to document the allowed types
ALTER TABLE logs MODIFY COLUMN type VARCHAR(50) NULL 
COMMENT 'Allowed types: request, error, authentication, security, database, performance, system, route_not_found, email_queue, email_worker, email, socket, socket_server, socket_connection, socket_authentication, socket_room, socket_analytics, socket_communication, socket_performance, socket_metrics_request, socket_api_error, socket_health_error, socket_logs_error, socket_memory_error, socket_broadcast, socket_error, socket_cleanup, memory_cleanup, memory_management, memory_monitoring, memory_alert, manual_cleanup, cleanup_error, server_shutdown, shutdown_error';

-- Verify the change
DESCRIBE logs;

-- Show some sample data to verify
SELECT type, COUNT(*) as count 
FROM logs 
GROUP BY type 
ORDER BY count DESC 
LIMIT 20;

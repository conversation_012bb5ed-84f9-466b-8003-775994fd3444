'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('fcm_subscriptions', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      token: {
        type: Sequelize.TEXT,
        allowNull: false
        // Note: We use tokenHash for uniqueness instead of token directly
      },
      tokenHash: {
        type: Sequelize.STRING(64),
        allowNull: false,
        unique: true
      },
      userRole: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: 'user'
      },
      deviceInfo: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: {}
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        allowNull: false
      },
      lastUsed: {
        type: Sequelize.DATE,
        allowNull: true
      },
      failureCount: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        allowNull: false
      },
      topics: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: []
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes
    await queryInterface.addIndex('fcm_subscriptions', ['userId']);
    await queryInterface.addIndex('fcm_subscriptions', ['tokenHash'], { unique: true });
    await queryInterface.addIndex('fcm_subscriptions', ['userRole']);
    await queryInterface.addIndex('fcm_subscriptions', ['isActive']);
    await queryInterface.addIndex('fcm_subscriptions', ['lastUsed']);
    await queryInterface.addIndex('fcm_subscriptions', ['failureCount']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('fcm_subscriptions');
  }
};

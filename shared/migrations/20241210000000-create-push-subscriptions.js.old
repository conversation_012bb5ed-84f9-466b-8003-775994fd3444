'use strict';

/**
 * Clean Push Subscriptions Migration
 * 
 * This migration creates the push_subscriptions table with proper MySQL compatibility
 * and handles duplicate index issues gracefully.
 */

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Check if table already exists
    const tableExists = await queryInterface.showAllTables()
      .then(tables => tables.includes('push_subscriptions'));

    if (tableExists) {
      console.log('Table push_subscriptions already exists, skipping creation...');
      return;
    }

    await queryInterface.createTable('push_subscriptions', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
      },
      endpoint: {
        type: Sequelize.TEXT, // TEXT for long URLs (no direct indexing)
        allowNull: false
      },
      endpointHash: {
        type: Sequelize.STRING(64), // SHA-256 hash for indexing and uniqueness
        allowNull: false,
        unique: true
      },
      p256dhKey: {
        type: Sequelize.STRING(255), // Reduced size - base64 keys are ~88 chars
        allowNull: false
      },
      authKey: {
        type: Sequelize.STRING(255), // Reduced size - base64 keys are ~24 chars
        allowNull: false
      },
      userRole: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: 'user'
      },
      deviceInfo: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: {}
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        allowNull: false
      },
      lastUsed: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        allowNull: false
      },
      failureCount: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        allowNull: false
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Helper function to safely add indexes
    const addIndexSafely = async (tableName, fields, options) => {
      try {
        await queryInterface.addIndex(tableName, fields, options);
        console.log(`✅ Created index: ${options.name}`);
      } catch (error) {
        if (error.message.includes('Duplicate key name') || 
            error.message.includes('already exists')) {
          console.log(`⚠️  Index ${options.name} already exists, skipping...`);
        } else {
          console.error(`❌ Failed to create index ${options.name}:`, error.message);
          throw error;
        }
      }
    };

    // Add indexes for better performance
    await addIndexSafely('push_subscriptions', ['userId'], {
      name: 'idx_push_subscriptions_user_id'
    });

    await addIndexSafely('push_subscriptions', ['endpointHash'], {
      name: 'idx_push_subscriptions_endpoint_hash',
      unique: true
    });

    await addIndexSafely('push_subscriptions', ['isActive'], {
      name: 'idx_push_subscriptions_is_active'
    });

    await addIndexSafely('push_subscriptions', ['userRole'], {
      name: 'idx_push_subscriptions_user_role'
    });

    await addIndexSafely('push_subscriptions', ['lastUsed'], {
      name: 'idx_push_subscriptions_last_used'
    });

    // Composite indexes for better query performance
    await addIndexSafely('push_subscriptions', ['userId', 'isActive'], {
      name: 'idx_push_subscriptions_user_active'
    });

    await addIndexSafely('push_subscriptions', ['userRole', 'isActive'], {
      name: 'idx_push_subscriptions_role_active'
    });

    console.log('✅ Push subscriptions table and indexes created successfully!');
  },

  down: async (queryInterface, Sequelize) => {
    // Drop indexes first (if they exist)
    const indexNames = [
      'idx_push_subscriptions_user_id',
      'idx_push_subscriptions_endpoint_hash',
      'idx_push_subscriptions_is_active',
      'idx_push_subscriptions_user_role',
      'idx_push_subscriptions_last_used',
      'idx_push_subscriptions_user_active',
      'idx_push_subscriptions_role_active'
    ];

    for (const indexName of indexNames) {
      try {
        await queryInterface.removeIndex('push_subscriptions', indexName);
        console.log(`✅ Dropped index: ${indexName}`);
      } catch (error) {
        console.log(`⚠️  Index ${indexName} doesn't exist or already dropped`);
      }
    }

    // Drop table
    await queryInterface.dropTable('push_subscriptions');
    console.log('✅ Push subscriptions table dropped successfully!');
  }
};

'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('email_queue', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      to_email: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      to_name: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      from_email: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      from_name: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      subject: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      html_content: {
        type: Sequelize.TEXT('long'),
        allowNull: false
      },
      text_content: {
        type: Sequelize.TEXT('long'),
        allowNull: true
      },
      email_type: {
        type: Sequelize.ENUM('verification', 'password_reset', 'welcome', 'notification', 'marketing'),
        allowNull: false
      },
      priority: {
        type: Sequelize.ENUM('low', 'normal', 'high', 'urgent'),
        allowNull: false,
        defaultValue: 'normal'
      },
      status: {
        type: Sequelize.ENUM('pending', 'processing', 'sent', 'failed', 'cancelled'),
        allowNull: false,
        defaultValue: 'pending'
      },
      scheduled_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      sent_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      failed_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      attempts: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      max_attempts: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 3
      },
      error_message: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      error_stack: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true
      },
      template_data: {
        type: Sequelize.JSON,
        allowNull: true
      },
      ip_address: {
        type: Sequelize.STRING(45),
        allowNull: true
      },
      user_agent: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add indexes for performance
    await queryInterface.addIndex('email_queue', ['status']);
    await queryInterface.addIndex('email_queue', ['email_type']);
    await queryInterface.addIndex('email_queue', ['priority']);
    await queryInterface.addIndex('email_queue', ['scheduled_at']);
    await queryInterface.addIndex('email_queue', ['created_at']);
    await queryInterface.addIndex('email_queue', ['user_id']);
    await queryInterface.addIndex('email_queue', ['to_email']);
    await queryInterface.addIndex('email_queue', ['status', 'priority', 'scheduled_at']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('email_queue');
  }
};

'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create customers table
    await queryInterface.createTable('customers', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      firstName: {
        type: Sequelize.STRING(100),
        allowNull: false,
        field: 'first_name'
      },
      lastName: {
        type: Sequelize.STRING(100),
        allowNull: false,
        field: 'last_name'
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: false,
        unique: true
      },
      phone: {
        type: Sequelize.STRING(20),
        allowNull: true
      },
      companyName: {
        type: Sequelize.STRING(255),
        allowNull: true,
        field: 'company_name'
      },
      jobTitle: {
        type: Sequelize.STRING(100),
        allowNull: true,
        field: 'job_title'
      },
      industry: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      priority: {
        type: Sequelize.ENUM('low', 'medium', 'high', 'urgent'),
        allowNull: false,
        defaultValue: 'medium'
      },
      status: {
        type: Sequelize.ENUM('lead', 'prospect', 'customer', 'inactive'),
        allowNull: false,
        defaultValue: 'lead'
      },
      leadSource: {
        type: Sequelize.STRING(50),
        allowNull: true,
        field: 'lead_source'
      },
      companySize: {
        type: Sequelize.STRING(20),
        allowNull: true,
        field: 'company_size'
      },
      address: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      city: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      state: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      zipCode: {
        type: Sequelize.STRING(20),
        allowNull: true,
        field: 'zip_code'
      },
      country: {
        type: Sequelize.STRING(100),
        allowNull: false,
        defaultValue: 'Portugal'
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      energyNeeds: {
        type: Sequelize.TEXT,
        allowNull: true,
        field: 'energy_needs'
      },
      currentEnergyProvider: {
        type: Sequelize.STRING(255),
        allowNull: true,
        field: 'current_energy_provider'
      },
      monthlyEnergyBudget: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
        field: 'monthly_energy_budget'
      },
      estimatedValue: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
        field: 'estimated_value'
      },
      assignedTo: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'assigned_to',
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      firstContactDate: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'first_contact_date'
      },
      lastContactDate: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'last_contact_date'
      },
      nextFollowUpDate: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'next_follow_up_date'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        field: 'created_at'
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        field: 'updated_at'
      }
    });

    // Create projects table
    await queryInterface.createTable('projects', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      title: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      customerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'customer_id',
        references: {
          model: 'customers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      status: {
        type: Sequelize.ENUM('planning', 'in_progress', 'on_hold', 'completed', 'cancelled', 'archived'),
        allowNull: false,
        defaultValue: 'planning'
      },
      priority: {
        type: Sequelize.ENUM('low', 'medium', 'high', 'urgent'),
        allowNull: false,
        defaultValue: 'medium'
      },
      startDate: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'start_date'
      },
      endDate: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'end_date'
      },
      estimatedBudget: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
        field: 'estimated_budget'
      },
      actualBudget: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
        field: 'actual_budget'
      },
      progressPercentage: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        field: 'progress_percentage'
      },
      projectType: {
        type: Sequelize.STRING(50),
        allowNull: true,
        field: 'project_type'
      },
      assignedTo: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'assigned_to',
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      createdBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'created_by',
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        field: 'created_at'
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        field: 'updated_at'
      }
    });

    // Add indexes for better performance
    await queryInterface.addIndex('customers', ['email'], { unique: true });
    await queryInterface.addIndex('customers', ['status']);
    await queryInterface.addIndex('customers', ['priority']);
    await queryInterface.addIndex('customers', ['assigned_to']);
    await queryInterface.addIndex('customers', ['lead_source']);
    await queryInterface.addIndex('customers', ['created_at']);

    await queryInterface.addIndex('projects', ['customer_id']);
    await queryInterface.addIndex('projects', ['status']);
    await queryInterface.addIndex('projects', ['priority']);
    await queryInterface.addIndex('projects', ['assigned_to']);
    await queryInterface.addIndex('projects', ['created_at']);
  },

  async down(queryInterface, Sequelize) {
    // Drop tables in reverse order due to foreign key constraints
    await queryInterface.dropTable('projects');
    await queryInterface.dropTable('customers');
  }
};

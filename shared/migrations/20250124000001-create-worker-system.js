'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Background Jobs Queue Table
    await queryInterface.createTable('background_jobs', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      job_type: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      job_data: {
        type: Sequelize.JSON,
        allowNull: true
      },
      status: {
        type: Sequelize.ENUM('pending', 'processing', 'completed', 'failed', 'retrying'),
        defaultValue: 'pending',
        allowNull: false
      },
      priority: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        allowNull: false
      },
      attempts: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        allowNull: false
      },
      max_attempts: {
        type: Sequelize.INTEGER,
        defaultValue: 3,
        allowNull: false
      },
      scheduled_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        allowNull: false
      },
      started_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      completed_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      error_message: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes for background_jobs
    await queryInterface.addIndex('background_jobs', ['status', 'priority'], {
      name: 'idx_background_jobs_status_priority'
    });
    await queryInterface.addIndex('background_jobs', ['scheduled_at'], {
      name: 'idx_background_jobs_scheduled_at'
    });
    await queryInterface.addIndex('background_jobs', ['job_type'], {
      name: 'idx_background_jobs_job_type'
    });

    // System Stats Cache Table
    await queryInterface.createTable('system_stats', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      stat_type: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true
      },
      stat_data: {
        type: Sequelize.JSON,
        allowNull: false
      },
      calculated_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        allowNull: false
      },
      expires_at: {
        type: Sequelize.DATE,
        allowNull: false
      }
    });

    // Add indexes for system_stats
    await queryInterface.addIndex('system_stats', ['expires_at'], {
      name: 'idx_system_stats_expires_at'
    });

    // Health Check Results Table
    await queryInterface.createTable('health_checks', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      check_name: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      status: {
        type: Sequelize.ENUM('healthy', 'degraded', 'unhealthy'),
        allowNull: false
      },
      response_time_ms: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      details: {
        type: Sequelize.JSON,
        allowNull: true
      },
      checked_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        allowNull: false
      }
    });

    // Add indexes for health_checks
    await queryInterface.addIndex('health_checks', ['check_name', 'checked_at'], {
      name: 'idx_health_checks_name_time'
    });
    await queryInterface.addIndex('health_checks', ['status'], {
      name: 'idx_health_checks_status'
    });

    console.log('✅ Worker system tables created successfully');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('health_checks');
    await queryInterface.dropTable('system_stats');
    await queryInterface.dropTable('background_jobs');
    
    console.log('✅ Worker system tables dropped successfully');
  }
};

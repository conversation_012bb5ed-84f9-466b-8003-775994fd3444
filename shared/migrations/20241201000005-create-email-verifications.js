'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('email_verifications', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      token: {
        type: Sequelize.STRING(64),
        allowNull: false,
        unique: true
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      expires_at: {
        type: Sequelize.DATE,
        allowNull: false
      },
      verified_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      is_used: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      attempts: {
        type: Sequelize.INTEGER,
        defaultValue: 0
      },
      ip_address: {
        type: Sequelize.STRING(45),
        allowNull: true
      },
      user_agent: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for better query performance
    await queryInterface.addIndex('email_verifications', ['token'], {
      name: 'email_verifications_token_unique',
      unique: true
    });
    
    await queryInterface.addIndex('email_verifications', ['user_id'], {
      name: 'email_verifications_user_id_index'
    });
    
    await queryInterface.addIndex('email_verifications', ['email'], {
      name: 'email_verifications_email_index'
    });
    
    await queryInterface.addIndex('email_verifications', ['expires_at'], {
      name: 'email_verifications_expires_at_index'
    });
    
    await queryInterface.addIndex('email_verifications', ['is_used'], {
      name: 'email_verifications_is_used_index'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('email_verifications');
  }
};

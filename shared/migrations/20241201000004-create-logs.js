'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('logs', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      level: {
        type: Sequelize.ENUM('error', 'warn', 'info', 'http', 'debug'),
        allowNull: false
      },
      message: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      type: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      request_id: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      ip_address: {
        type: Sequelize.STRING(45),
        allowNull: true
      },
      user_agent: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      method: {
        type: Sequelize.STRING(10),
        allowNull: true
      },
      url: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      status_code: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      response_time: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      error_stack: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: {}
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for better query performance
    await queryInterface.addIndex('logs', ['level'], {
      name: 'logs_level_index'
    });
    
    await queryInterface.addIndex('logs', ['type'], {
      name: 'logs_type_index'
    });
    
    await queryInterface.addIndex('logs', ['user_id'], {
      name: 'logs_user_id_index'
    });
    
    await queryInterface.addIndex('logs', ['request_id'], {
      name: 'logs_request_id_index'
    });
    
    await queryInterface.addIndex('logs', ['ip_address'], {
      name: 'logs_ip_address_index'
    });
    
    await queryInterface.addIndex('logs', ['status_code'], {
      name: 'logs_status_code_index'
    });
    
    await queryInterface.addIndex('logs', ['created_at'], {
      name: 'logs_created_at_index'
    });
    
    await queryInterface.addIndex('logs', ['level', 'type'], {
      name: 'logs_level_type_index'
    });
    
    await queryInterface.addIndex('logs', ['created_at', 'level'], {
      name: 'logs_created_at_level_index'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('logs');
  }
};

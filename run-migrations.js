const { Sequelize } = require('sequelize');
require('dotenv').config();

// Database configuration
const sequelize = new Sequelize(
  process.env.DB_NAME || 'api_v2',
  process.env.DB_USER || 'hlenes_admin',
  process.env.DB_PASSWORD || 'hlenergyadmin1',
  {
    host: process.env.DB_HOST || 'logs.hlenergy.pt',
    port: process.env.DB_PORT || 3306,
    dialect: 'mysql',
    logging: console.log,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
  }
);

async function runMigrations() {
  try {
    console.log('🔌 Connecting to database...');
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    console.log('\n📋 Checking if CRM tables exist...');
    
    // Check if customers table exists
    const [customersExists] = await sequelize.query(
      "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = 'customers'",
      {
        replacements: [process.env.DB_NAME || 'api_v2'],
        type: Sequelize.QueryTypes.SELECT
      }
    );

    const [projectsExists] = await sequelize.query(
      "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = 'projects'",
      {
        replacements: [process.env.DB_NAME || 'api_v2'],
        type: Sequelize.QueryTypes.SELECT
      }
    );

    if (customersExists.count > 0 && projectsExists.count > 0) {
      console.log('✅ CRM tables already exist!');
      console.log('   - customers table: ✓');
      console.log('   - projects table: ✓');
      return;
    }

    console.log('\n🚀 Creating CRM tables...');

    // Create customers table
    if (customersExists.count === 0) {
      console.log('📝 Creating customers table...');
      await sequelize.query(`
        CREATE TABLE customers (
          id INT AUTO_INCREMENT PRIMARY KEY,
          first_name VARCHAR(100) NOT NULL,
          last_name VARCHAR(100) NOT NULL,
          email VARCHAR(255) NOT NULL UNIQUE,
          phone VARCHAR(20),
          company_name VARCHAR(255),
          job_title VARCHAR(100),
          industry VARCHAR(100),
          priority ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium',
          status ENUM('lead', 'prospect', 'customer', 'inactive') NOT NULL DEFAULT 'lead',
          lead_source VARCHAR(50),
          company_size VARCHAR(20),
          address TEXT,
          city VARCHAR(100),
          state VARCHAR(100),
          zip_code VARCHAR(20),
          country VARCHAR(100) NOT NULL DEFAULT 'Portugal',
          notes TEXT,
          energy_needs TEXT,
          current_energy_provider VARCHAR(255),
          monthly_energy_budget DECIMAL(10, 2),
          estimated_value DECIMAL(15, 2),
          assigned_to INT,
          first_contact_date DATETIME,
          last_contact_date DATETIME,
          next_follow_up_date DATETIME,
          created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          
          INDEX idx_customers_email (email),
          INDEX idx_customers_status (status),
          INDEX idx_customers_priority (priority),
          INDEX idx_customers_assigned_to (assigned_to),
          INDEX idx_customers_lead_source (lead_source),
          INDEX idx_customers_created_at (created_at),
          
          FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
        )
      `);
      console.log('✅ Customers table created successfully!');
    }

    // Create projects table
    if (projectsExists.count === 0) {
      console.log('📝 Creating projects table...');
      await sequelize.query(`
        CREATE TABLE projects (
          id INT AUTO_INCREMENT PRIMARY KEY,
          title VARCHAR(255) NOT NULL,
          description TEXT,
          customer_id INT NOT NULL,
          status ENUM('planning', 'in_progress', 'on_hold', 'completed', 'cancelled', 'archived') NOT NULL DEFAULT 'planning',
          priority ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium',
          start_date DATE,
          end_date DATE,
          estimated_budget DECIMAL(15, 2),
          actual_budget DECIMAL(15, 2),
          progress_percentage INT NOT NULL DEFAULT 0,
          project_type VARCHAR(50),
          assigned_to INT,
          created_by INT,
          created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          
          INDEX idx_projects_customer_id (customer_id),
          INDEX idx_projects_status (status),
          INDEX idx_projects_priority (priority),
          INDEX idx_projects_assigned_to (assigned_to),
          INDEX idx_projects_created_at (created_at),
          
          FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE ON UPDATE CASCADE,
          FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
          FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
        )
      `);
      console.log('✅ Projects table created successfully!');
    }

    console.log('\n🎉 CRM migration completed successfully!');
    console.log('📊 Tables created:');
    console.log('   - customers (with indexes and foreign keys)');
    console.log('   - projects (with indexes and foreign keys)');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await sequelize.close();
    console.log('\n🔌 Database connection closed.');
  }
}

// Run the migration
runMigrations();

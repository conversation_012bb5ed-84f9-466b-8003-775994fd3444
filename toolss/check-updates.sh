#!/bin/bash

# HLenergy API Dependency Update Checker
# This script checks for npm package updates and security vulnerabilities

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_update() {
    echo -e "${PURPLE}🔄 $1${NC}"
}

print_security() {
    echo -e "${CYAN}🔒 $1${NC}"
}

# Function to show header
show_header() {
    echo ""
    echo "🔍 HLenergy API Dependency Checker"
    echo "=================================="
    echo ""
}

# Function to check if npm is available
check_npm() {
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed or not in PATH"
        exit 1
    fi
    print_status "npm is available ($(npm --version))"
}

# Function to check outdated packages
check_outdated() {
    print_info "Checking for outdated packages..."
    echo ""
    
    # Check for outdated packages
    if npm outdated --json > /tmp/outdated.json 2>/dev/null; then
        if [ -s /tmp/outdated.json ]; then
            print_warning "Found outdated packages:"
            echo ""
            npm outdated --color=always
            echo ""
            
            # Parse JSON and show summary
            local count=$(cat /tmp/outdated.json | jq 'length' 2>/dev/null || echo "0")
            print_warning "Total outdated packages: $count"
        else
            print_status "All packages are up to date!"
        fi
    else
        print_info "Checking outdated packages (fallback method)..."
        npm outdated --color=always || print_status "All packages are up to date!"
    fi
    
    rm -f /tmp/outdated.json
}

# Function to check security vulnerabilities
check_security() {
    print_security "Checking for security vulnerabilities..."
    echo ""
    
    # Run npm audit
    if npm audit --json > /tmp/audit.json 2>/dev/null; then
        local vulnerabilities=$(cat /tmp/audit.json | jq '.metadata.vulnerabilities.total' 2>/dev/null || echo "0")
        
        if [ "$vulnerabilities" -gt 0 ]; then
            print_error "Found $vulnerabilities security vulnerabilities!"
            echo ""
            npm audit --color=always
            echo ""
            print_warning "Run 'npm audit fix' to automatically fix issues"
            print_warning "Run 'npm audit fix --force' for breaking changes (use with caution)"
        else
            print_status "No security vulnerabilities found!"
        fi
    else
        print_info "Running security audit (fallback method)..."
        npm audit --color=always || print_status "No security vulnerabilities found!"
    fi
    
    rm -f /tmp/audit.json
}

# Function to show package information
show_package_info() {
    print_info "Current package information:"
    echo ""
    
    # Show package.json info
    local name=$(cat package.json | jq -r '.name' 2>/dev/null || echo "unknown")
    local version=$(cat package.json | jq -r '.version' 2>/dev/null || echo "unknown")
    local deps=$(cat package.json | jq '.dependencies | length' 2>/dev/null || echo "0")
    local devDeps=$(cat package.json | jq '.devDependencies | length' 2>/dev/null || echo "0")
    
    echo "📦 Package: $name@$version"
    echo "📚 Dependencies: $deps"
    echo "🛠️  Dev Dependencies: $devDeps"
    echo "📁 Total: $((deps + devDeps))"
    echo ""
}

# Function to check specific package
check_package() {
    local package_name=$1
    if [ -z "$package_name" ]; then
        print_error "Please specify a package name"
        return 1
    fi
    
    print_info "Checking package: $package_name"
    echo ""
    
    # Get current version
    local current=$(npm list $package_name --depth=0 --json 2>/dev/null | jq -r ".dependencies.\"$package_name\".version" 2>/dev/null || echo "not installed")
    
    # Get latest version
    local latest=$(npm view $package_name version 2>/dev/null || echo "unknown")
    
    echo "📦 Package: $package_name"
    echo "📌 Current: $current"
    echo "🆕 Latest: $latest"
    
    if [ "$current" != "$latest" ] && [ "$current" != "not installed" ] && [ "$latest" != "unknown" ]; then
        print_update "Update available: $current → $latest"
        echo "   Run: npm install $package_name@$latest"
    elif [ "$current" = "$latest" ]; then
        print_status "Package is up to date!"
    fi
    echo ""
}

# Function to show update commands
show_update_commands() {
    print_info "Update commands:"
    echo ""
    echo "🔄 Update all packages:"
    echo "   npm update                    # Update within semver range"
    echo "   npx npm-check-updates -u      # Update to latest versions"
    echo ""
    echo "🔒 Security fixes:"
    echo "   npm audit fix                 # Auto-fix vulnerabilities"
    echo "   npm audit fix --force         # Force fix (may break)"
    echo ""
    echo "📦 Specific package:"
    echo "   npm install package@latest    # Update specific package"
    echo "   npm install package@^1.0.0   # Update with version range"
    echo ""
}

# Function to interactive update
interactive_update() {
    print_info "Interactive package update"
    echo ""
    
    # Check if npm-check-updates is available
    if ! command -v npx &> /dev/null; then
        print_error "npx is not available"
        return 1
    fi
    
    print_info "Checking for available updates..."
    npx npm-check-updates
    echo ""
    
    read -p "🤔 Do you want to update package.json with latest versions? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_update "Updating package.json..."
        npx npm-check-updates -u
        
        echo ""
        read -p "🤔 Do you want to install the updated packages? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_update "Installing updated packages..."
            npm install
            print_status "Packages updated successfully!"
        else
            print_warning "Package.json updated but packages not installed"
            print_info "Run 'npm install' to install the updated packages"
        fi
    else
        print_info "Update cancelled"
    fi
}

# Function to generate dependency report
generate_report() {
    local output_file="dependency-report-$(date +%Y%m%d-%H%M%S).json"
    
    print_info "Generating dependency report..."
    
    # Create comprehensive report
    cat > "$output_file" << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "package_info": $(cat package.json | jq '{name, version, dependencies, devDependencies}'),
  "outdated": $(npm outdated --json 2>/dev/null || echo '{}'),
  "audit": $(npm audit --json 2>/dev/null || echo '{"metadata":{"vulnerabilities":{"total":0}}}'),
  "installed": $(npm list --json --depth=0 2>/dev/null || echo '{}')
}
EOF
    
    print_status "Report generated: $output_file"
    
    # Show summary
    local total_packages=$(cat "$output_file" | jq '.installed.dependencies | length' 2>/dev/null || echo "0")
    local outdated_count=$(cat "$output_file" | jq '.outdated | length' 2>/dev/null || echo "0")
    local vulnerabilities=$(cat "$output_file" | jq '.audit.metadata.vulnerabilities.total' 2>/dev/null || echo "0")
    
    echo ""
    echo "📊 Report Summary:"
    echo "   Total packages: $total_packages"
    echo "   Outdated: $outdated_count"
    echo "   Vulnerabilities: $vulnerabilities"
    echo ""
}

# Function to show help
show_help() {
    echo "🔍 HLenergy API Dependency Checker"
    echo "=================================="
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  check                  Check for outdated packages and vulnerabilities"
    echo "  outdated              Check only for outdated packages"
    echo "  security              Check only for security vulnerabilities"
    echo "  info                  Show package information"
    echo "  package <name>        Check specific package"
    echo "  update                Show update commands"
    echo "  interactive           Interactive update process"
    echo "  report                Generate dependency report"
    echo "  help                  Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 check              # Full check"
    echo "  $0 package express    # Check express package"
    echo "  $0 interactive        # Interactive update"
    echo "  $0 report             # Generate report"
    echo ""
}

# Main script logic
show_header
check_npm

case "${1:-check}" in
    "check")
        show_package_info
        check_outdated
        echo ""
        check_security
        echo ""
        show_update_commands
        ;;
    "outdated")
        check_outdated
        ;;
    "security")
        check_security
        ;;
    "info")
        show_package_info
        ;;
    "package")
        check_package "$2"
        ;;
    "update")
        show_update_commands
        ;;
    "interactive")
        interactive_update
        ;;
    "report")
        generate_report
        ;;
    "help"|*)
        show_help
        ;;
esac

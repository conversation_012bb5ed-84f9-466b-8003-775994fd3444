#!/usr/bin/env node

/**
 * Create Admin User Script
 * Creates an admin user for testing the authentication and admin features
 */

const { User } = require('../src/models');
const { sequelize } = require('../src/config/database');

async function createAdminUser() {
  try {
    console.log('🔧 Creating admin user...');

    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connected');

    // Check if admin already exists
    const existingAdmin = await User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (existingAdmin) {
      console.log('⚠️  Admin user already exists:');
      console.log(`   Email: ${existingAdmin.email}`);
      console.log(`   Role: ${existingAdmin.role}`);
      console.log(`   Active: ${existingAdmin.is_active}`);
      return;
    }

    // Create admin user
    const adminUser = await User.create({
      name: 'System Administrator',
      email: '<EMAIL>',
      password: 'admin123', // Will be hashed automatically
      role: 'admin',
      email_verified: true,
      is_active: true,
      profile: {
        department: 'IT',
        position: 'System Administrator',
        created_by: 'system',
      },
    });

    console.log('✅ Admin user created successfully!');
    console.log('');
    console.log('📋 Admin User Details:');
    console.log(`   ID: ${adminUser.id}`);
    console.log(`   Name: ${adminUser.name}`);
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Role: ${adminUser.role}`);
    console.log(`   Password: admin123`);
    console.log('');
    console.log('🔐 Login Instructions:');
    console.log('   1. Use the email and password above to login');
    console.log('   2. POST /api/v1/auth/login with credentials');
    console.log('   3. Use the returned token for admin endpoints');
    console.log('');
    console.log('🛠️  Test Admin Endpoints:');
    console.log('   GET /api/v1/admin/dashboard');
    console.log('   GET /api/v1/admin/users');
    console.log('   GET /api/v1/admin/contacts');

    // Create a staff user for testing
    const staffUser = await User.create({
      name: 'Staff Member',
      email: '<EMAIL>',
      password: 'staff123',
      role: 'staff',
      email_verified: true,
      is_active: true,
      profile: {
        department: 'Customer Service',
        position: 'Support Specialist',
        created_by: 'system',
      },
    });

    console.log('');
    console.log('✅ Staff user created successfully!');
    console.log('');
    console.log('📋 Staff User Details:');
    console.log(`   ID: ${staffUser.id}`);
    console.log(`   Name: ${staffUser.name}`);
    console.log(`   Email: ${staffUser.email}`);
    console.log(`   Role: ${staffUser.role}`);
    console.log(`   Password: staff123`);

    // Create a regular client user
    const clientUser = await User.create({
      name: 'Test Client',
      email: '<EMAIL>',
      password: 'client123',
      role: 'client',
      email_verified: true,
      is_active: true,
      profile: {
        company: 'Test Company',
        position: 'Manager',
        created_by: 'system',
      },
    });

    console.log('');
    console.log('✅ Client user created successfully!');
    console.log('');
    console.log('📋 Client User Details:');
    console.log(`   ID: ${clientUser.id}`);
    console.log(`   Name: ${clientUser.name}`);
    console.log(`   Email: ${clientUser.email}`);
    console.log(`   Role: ${clientUser.role}`);
    console.log(`   Password: client123`);

    console.log('');
    console.log('🎉 All test users created successfully!');
    console.log('');
    console.log('🧪 Testing Commands:');
    console.log('');
    console.log('# Login as admin:');
    console.log('curl -X POST http://localhost:3001/api/v1/auth/login \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"email":"<EMAIL>","password":"admin123"}\'');
    console.log('');
    console.log('# Login as staff:');
    console.log('curl -X POST http://localhost:3001/api/v1/auth/login \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"email":"<EMAIL>","password":"staff123"}\'');
    console.log('');
    console.log('# Login as client:');
    console.log('curl -X POST http://localhost:3001/api/v1/auth/login \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"email":"<EMAIL>","password":"client123"}\'');
    console.log('');
    console.log('# Access admin dashboard (replace TOKEN with actual token):');
    console.log('curl -H "Authorization: Bearer TOKEN" \\');
    console.log('  http://localhost:3001/api/v1/admin/dashboard');

  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
    if (error.errors) {
      error.errors.forEach(err => {
        console.error(`   - ${err.message}`);
      });
    }
  } finally {
    await sequelize.close();
    process.exit(0);
  }
}

// Run the script
if (require.main === module) {
  createAdminUser();
}

module.exports = createAdminUser;

#!/bin/bash

# HLenergy API Version Initialization Script
# This script initializes Git repository and sets up version tracking

echo "🚀 Initializing HLenergy API Version Tracking"
echo "=============================================="

# Check if we're in a Git repository
if [ ! -d ".git" ]; then
    echo "📦 Initializing Git repository..."
    git init
    
    # Create initial .gitignore
    cat > .gitignore << EOF
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
EOF

    echo "✅ Created .gitignore"
else
    echo "✅ Git repository already exists"
fi

# Add all files to Git
echo "📝 Adding files to Git..."
git add .

# Check if there are any commits
if ! git rev-parse --verify HEAD >/dev/null 2>&1; then
    echo "📝 Creating initial commit..."
    git commit -m "Initial commit: HLenergy API v1.0.0

Features:
- Express.js v5 server with latest dependencies
- JWT authentication system
- Contact form API
- API versioning (v1)
- Swagger/OpenAPI documentation
- Redoc documentation
- Rate limiting and security
- Comprehensive version tracking
- Development auto-restart with nodemon"
else
    echo "✅ Repository already has commits"
fi

# Create version tags
echo "🏷️  Creating version tags..."

# Check if tag already exists
if ! git tag -l | grep -q "v1.0.0"; then
    git tag -a v1.0.0 -m "Release v1.0.0: Initial HLenergy API release

Features:
- Complete authentication system
- Contact form management
- API versioning
- Comprehensive documentation
- Security and rate limiting
- Development tooling"
    echo "✅ Created tag v1.0.0"
else
    echo "✅ Tag v1.0.0 already exists"
fi

# Set up Git user if not configured
if [ -z "$(git config user.name)" ]; then
    echo "👤 Setting up Git user..."
    git config user.name "HLenergy Developer"
    git config user.email "<EMAIL>"
    echo "✅ Git user configured"
fi

# Create development branch if it doesn't exist
if ! git branch -a | grep -q "develop"; then
    echo "🌿 Creating development branch..."
    git checkout -b develop
    git checkout main 2>/dev/null || git checkout master 2>/dev/null || echo "Staying on current branch"
    echo "✅ Development branch created"
fi

echo ""
echo "🎉 Version tracking initialized successfully!"
echo ""
echo "📋 Repository Status:"
echo "   Current branch: $(git branch --show-current)"
echo "   Latest commit: $(git log -1 --format='%h - %s (%cr)')"
echo "   Tags: $(git tag -l | tr '\n' ' ')"
echo ""
echo "🔗 Next steps:"
echo "   1. Restart the server to see Git information"
echo "   2. Use 'git tag v1.0.1' for new releases"
echo "   3. Use 'git checkout develop' for development"
echo ""
echo "=============================================="

#!/bin/bash

# HLenergy API Version Bump Script
# This script helps manage version releases

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to get current version from package.json
get_current_version() {
    node -p "require('./package.json').version"
}

# Function to update package.json version
update_package_version() {
    local new_version=$1
    npm version $new_version --no-git-tag-version
}

# Function to validate version format
validate_version() {
    local version=$1
    if [[ ! $version =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        print_error "Invalid version format. Use semantic versioning (e.g., 1.0.1)"
        exit 1
    fi
}

# Function to check if version already exists
check_version_exists() {
    local version=$1
    if git tag -l | grep -q "^v$version$"; then
        print_error "Version v$version already exists!"
        exit 1
    fi
}

# Function to show current status
show_status() {
    local current_version=$(get_current_version)
    local current_commit=$(git rev-parse --short HEAD)
    local current_branch=$(git rev-parse --abbrev-ref HEAD)
    
    echo ""
    echo "🏷️  Current Version Status"
    echo "=========================="
    echo "📦 Package Version: $current_version"
    echo "🌿 Git Branch: $current_branch"
    echo "📝 Git Commit: $current_commit"
    echo "🏷️  Git Tags: $(git tag -l | tr '\n' ' ')"
    echo ""
}

# Function to create release
create_release() {
    local version_type=$1
    local current_version=$(get_current_version)
    
    print_info "Current version: $current_version"
    
    # Calculate new version based on type
    local new_version
    case $version_type in
        "patch")
            new_version=$(echo $current_version | awk -F. '{$NF = $NF + 1;} 1' | sed 's/ /./g')
            ;;
        "minor")
            new_version=$(echo $current_version | awk -F. '{$(NF-1) = $(NF-1) + 1; $NF = 0;} 1' | sed 's/ /./g')
            ;;
        "major")
            new_version=$(echo $current_version | awk -F. '{$1 = $1 + 1; $2 = 0; $3 = 0;} 1' | sed 's/ /./g')
            ;;
        *)
            new_version=$version_type
            validate_version $new_version
            ;;
    esac
    
    print_info "New version will be: $new_version"
    check_version_exists $new_version
    
    # Confirm with user
    read -p "🤔 Create release v$new_version? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "Release cancelled"
        exit 0
    fi
    
    # Check for uncommitted changes
    if ! git diff-index --quiet HEAD --; then
        print_warning "You have uncommitted changes. Commit them first."
        git status --short
        exit 1
    fi
    
    # Update package.json
    print_info "Updating package.json version..."
    update_package_version $new_version
    
    # Commit version bump
    print_info "Committing version bump..."
    git add package.json package-lock.json
    git commit -m "chore: bump version to v$new_version"
    
    # Create tag
    print_info "Creating Git tag..."
    git tag -a "v$new_version" -m "Release v$new_version

$(generate_changelog $current_version $new_version)"
    
    print_status "Release v$new_version created successfully!"
    print_info "To push the release: git push origin main --tags"
}

# Function to generate changelog
generate_changelog() {
    local from_version=$1
    local to_version=$2
    
    echo "Changes since v$from_version:"
    echo ""
    
    # Get commits since last tag
    local last_tag="v$from_version"
    if git tag -l | grep -q "^$last_tag$"; then
        git log --oneline "$last_tag..HEAD" | sed 's/^/- /'
    else
        echo "- Initial release"
    fi
}

# Function to show help
show_help() {
    echo "🚀 HLenergy API Version Management"
    echo "=================================="
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  status                 Show current version status"
    echo "  patch                  Bump patch version (1.0.0 -> 1.0.1)"
    echo "  minor                  Bump minor version (1.0.0 -> 1.1.0)"
    echo "  major                  Bump major version (1.0.0 -> 2.0.0)"
    echo "  release <version>      Create specific version (e.g., 1.2.3)"
    echo "  changelog [from] [to]  Show changelog between versions"
    echo "  help                   Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 status              # Show current version info"
    echo "  $0 patch               # Create patch release"
    echo "  $0 release 1.2.0       # Create specific version"
    echo "  $0 changelog v1.0.0    # Show changes since v1.0.0"
    echo ""
}

# Main script logic
case "${1:-help}" in
    "status")
        show_status
        ;;
    "patch"|"minor"|"major")
        create_release $1
        ;;
    "release")
        if [ -z "$2" ]; then
            print_error "Please specify a version number"
            echo "Usage: $0 release <version>"
            exit 1
        fi
        create_release $2
        ;;
    "changelog")
        local from_version=${2:-$(git describe --tags --abbrev=0 2>/dev/null || echo "v1.0.0")}
        local to_version=${3:-"HEAD"}
        generate_changelog $from_version $to_version
        ;;
    "help"|*)
        show_help
        ;;
esac

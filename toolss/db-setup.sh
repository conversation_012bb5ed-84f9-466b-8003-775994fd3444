#!/bin/bash

# HLenergy API Database Setup Script
# This script sets up MySQL database and runs migrations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to show header
show_header() {
    echo ""
    echo "🗄️  HLenergy API Database Setup"
    echo "==============================="
    echo ""
}

# Function to check if MySQL is running
check_mysql() {
    print_info "Checking MySQL connection..."
    
    if command -v mysql &> /dev/null; then
        print_status "MySQL client is available"
    else
        print_error "MySQL client is not installed"
        print_info "Please install MySQL client first"
        exit 1
    fi
    
    # Test connection
    if mysql -h${DB_HOST:-localhost} -P${DB_PORT:-3306} -u${DB_USER:-root} -p${DB_PASSWORD} -e "SELECT 1;" &> /dev/null; then
        print_status "MySQL connection successful"
    else
        print_error "Cannot connect to MySQL"
        print_info "Please check your database credentials in .env file"
        exit 1
    fi
}

# Function to create databases
create_databases() {
    print_info "Creating databases..."
    
    local dev_db=${DB_NAME:-hlenergy_dev}
    local test_db=${DB_NAME_TEST:-hlenergy_test}
    
    # Create development database
    mysql -h${DB_HOST:-localhost} -P${DB_PORT:-3306} -u${DB_USER:-root} -p${DB_PASSWORD} -e "CREATE DATABASE IF NOT EXISTS \`$dev_db\`;" 2>/dev/null
    print_status "Development database '$dev_db' created/verified"

    # Create test database
    mysql -h${DB_HOST:-localhost} -P${DB_PORT:-3306} -u${DB_USER:-root} -p${DB_PASSWORD} -e "CREATE DATABASE IF NOT EXISTS \`$test_db\`;" 2>/dev/null
    print_status "Test database '$test_db' created/verified"
}

# Function to run migrations
run_migrations() {
    print_info "Running database migrations..."
    
    if npx sequelize-cli db:migrate; then
        print_status "Migrations completed successfully"
    else
        print_error "Migration failed"
        exit 1
    fi
}

# Function to run seeders
run_seeders() {
    print_info "Running database seeders..."
    
    if npx sequelize-cli db:seed:all; then
        print_status "Seeders completed successfully"
    else
        print_warning "Seeders failed (this might be expected if data already exists)"
    fi
}

# Function to show database status
show_status() {
    print_info "Database status:"
    echo ""
    
    local dev_db=${DB_NAME:-hlenergy_dev}
    
    # Show tables
    echo "📋 Tables in $dev_db:"
    mysql -h${DB_HOST:-localhost} -P${DB_PORT:-3306} -u${DB_USER:-root} -p${DB_PASSWORD} -e "USE \`$dev_db\`; SHOW TABLES;" 2>/dev/null || echo "   No tables found"

    echo ""

    # Show user count
    local user_count=$(mysql -h${DB_HOST:-localhost} -P${DB_PORT:-3306} -u${DB_USER:-root} -p${DB_PASSWORD} -e "USE \`$dev_db\`; SELECT COUNT(*) FROM users;" 2>/dev/null | tail -n 1 || echo "0")
    echo "👥 Users: $user_count"

    # Show contact count
    local contact_count=$(mysql -h${DB_HOST:-localhost} -P${DB_PORT:-3306} -u${DB_USER:-root} -p${DB_PASSWORD} -e "USE \`$dev_db\`; SELECT COUNT(*) FROM contact_submissions;" 2>/dev/null | tail -n 1 || echo "0")
    echo "📧 Contact submissions: $contact_count"
    
    echo ""
}

# Function to reset database
reset_database() {
    print_warning "This will delete all data in the database!"
    read -p "🤔 Are you sure you want to reset the database? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Database reset cancelled"
        return
    fi
    
    print_info "Resetting database..."
    
    # Undo all migrations
    npx sequelize-cli db:migrate:undo:all || true
    
    # Run migrations again
    run_migrations
    
    # Run seeders
    run_seeders
    
    print_status "Database reset completed"
}

# Function to backup database
backup_database() {
    local backup_file="backup-$(date +%Y%m%d-%H%M%S).sql"
    local dev_db=${DB_NAME:-hlenergy_dev}
    
    print_info "Creating database backup..."
    
    if mysqldump -h${DB_HOST:-localhost} -P${DB_PORT:-3306} -u${DB_USER:-root} -p${DB_PASSWORD} "$dev_db" > "$backup_file"; then
        print_status "Database backup created: $backup_file"
    else
        print_error "Backup failed"
        exit 1
    fi
}

# Function to show help
show_help() {
    echo "🗄️  HLenergy API Database Setup"
    echo "==============================="
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  setup                  Complete database setup (create, migrate, seed)"
    echo "  create                 Create databases only"
    echo "  migrate                Run migrations only"
    echo "  seed                   Run seeders only"
    echo "  status                 Show database status"
    echo "  reset                  Reset database (WARNING: deletes all data)"
    echo "  backup                 Create database backup"
    echo "  help                   Show this help message"
    echo ""
    echo "Environment variables (from .env file):"
    echo "  DB_HOST                Database host (default: localhost)"
    echo "  DB_PORT                Database port (default: 3306)"
    echo "  DB_USER                Database username (default: root)"
    echo "  DB_PASSWORD            Database password"
    echo "  DB_NAME                Development database name (default: hlenergy_dev)"
    echo "  DB_NAME_TEST           Test database name (default: hlenergy_test)"
    echo ""
}

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
    print_status "Environment variables loaded from .env"
else
    print_warning ".env file not found, using defaults"
fi

# Main script logic
show_header

case "${1:-setup}" in
    "setup")
        check_mysql
        create_databases
        run_migrations
        run_seeders
        show_status
        ;;
    "create")
        check_mysql
        create_databases
        ;;
    "migrate")
        check_mysql
        run_migrations
        ;;
    "seed")
        check_mysql
        run_seeders
        ;;
    "status")
        check_mysql
        show_status
        ;;
    "reset")
        check_mysql
        reset_database
        show_status
        ;;
    "backup")
        check_mysql
        backup_database
        ;;
    "help"|*)
        show_help
        ;;
esac

#!/usr/bin/env node

/**
 * Generate Postman Collection from API Endpoints
 * This script creates a Postman collection based on the endpoints from our landing page
 */

const fs = require('fs');
const path = require('path');

// All endpoints from the landing page
const endpoints = [
  // System Endpoints
  { method: 'GET', path: '/health', category: 'System Health', description: 'Health check endpoint' },
  { method: 'GET', path: '/version', category: 'System Health', description: 'Version information' },
  { method: 'GET', path: '/version/summary', category: 'System Health', description: 'Version summary' },
  { method: 'GET', path: '/api/test', category: 'System Health', description: 'API test endpoint' },

  // Authentication
  { method: 'POST', path: '/api/v1/auth/register', category: 'Authentication', description: 'User registration' },
  { method: 'POST', path: '/api/v1/auth/login', category: 'Authentication', description: 'User login' },
  { method: 'POST', path: '/api/v1/auth/refresh', category: 'Authentication', description: 'Refresh token' },

  // Contact
  { method: 'POST', path: '/api/v1/contact/submit', category: 'Contact', description: 'Submit contact form' },
  { method: 'GET', path: '/api/v1/contact', category: 'Contact', description: 'Get contact submissions' },
  { method: 'PATCH', path: '/api/v1/contact/:id/status', category: 'Contact', description: 'Update contact status' },

  // Profile
  { method: 'GET', path: '/api/v1/profile', category: 'Profile', description: 'Get user profile' },
  { method: 'PATCH', path: '/api/v1/profile', category: 'Profile', description: 'Update user profile' },
  { method: 'PATCH', path: '/api/v1/profile/password', category: 'Profile', description: 'Change password' },
  { method: 'GET', path: '/api/v1/profile/sessions', category: 'Profile', description: 'Get user sessions' },
  { method: 'GET', path: '/api/v1/profile/sessions/:id', category: 'Profile', description: 'Get session details' },

  // Admin
  { method: 'GET', path: '/api/v1/admin/dashboard', category: 'Admin', description: 'Admin dashboard' },
  { method: 'GET', path: '/api/v1/admin/users', category: 'Admin', description: 'Get all users' },
  { method: 'GET', path: '/api/v1/admin/users/:id', category: 'Admin', description: 'Get user details' },
  { method: 'PATCH', path: '/api/v1/admin/users/:id/status', category: 'Admin', description: 'Update user status' },
  { method: 'GET', path: '/api/v1/admin/analytics', category: 'Admin', description: 'Admin analytics' },
  { method: 'GET', path: '/api/v1/admin/contacts', category: 'Admin', description: 'Admin contacts' },
  { method: 'GET', path: '/api/v1/admin/contacts/:id', category: 'Admin', description: 'Get contact details' },
  { method: 'GET', path: '/api/v1/admin/system/health', category: 'Admin', description: 'System health check' },

  // Email
  { method: 'POST', path: '/api/v1/email/send-verification', category: 'Email', description: 'Send verification email' },
  { method: 'POST', path: '/api/v1/email/verify', category: 'Email', description: 'Verify email' },
  { method: 'POST', path: '/api/v1/email/forgot-password', category: 'Email', description: 'Forgot password' },
  { method: 'POST', path: '/api/v1/email/reset-password', category: 'Email', description: 'Reset password' },

  // Email Queue
  { method: 'GET', path: '/api/v1/email-queue/status', category: 'Email Queue', description: 'Get queue status' },
  { method: 'GET', path: '/api/v1/email-queue/emails', category: 'Email Queue', description: 'Get queued emails' },
  { method: 'GET', path: '/api/v1/email-queue/emails/:id', category: 'Email Queue', description: 'Get email details' },
  { method: 'POST', path: '/api/v1/email-queue/emails/:id/retry', category: 'Email Queue', description: 'Retry failed email' },
  { method: 'POST', path: '/api/v1/email-queue/emails/:id/cancel', category: 'Email Queue', description: 'Cancel email' },
  { method: 'POST', path: '/api/v1/email-queue/worker/start', category: 'Email Queue', description: 'Start email worker' },
  { method: 'POST', path: '/api/v1/email-queue/worker/stop', category: 'Email Queue', description: 'Stop email worker' },
  { method: 'DELETE', path: '/api/v1/email-queue/cleanup', category: 'Email Queue', description: 'Cleanup queue' },

  // Analytics
  { method: 'POST', path: '/api/v1/analytics/events', category: 'Analytics', description: 'Track analytics events' },
  { method: 'GET', path: '/api/v1/analytics/metrics', category: 'Analytics', description: 'Get analytics metrics' },
  { method: 'GET', path: '/api/v1/analytics/business-metrics', category: 'Analytics', description: 'Get business metrics' },
  { method: 'GET', path: '/api/v1/analytics/heatmap', category: 'Analytics', description: 'Get heatmap data' },

  // Socket.io
  { method: 'GET', path: '/api/v1/socket/health', category: 'Socket.io', description: 'Socket health check' },
  { method: 'GET', path: '/api/v1/socket/status', category: 'Socket.io', description: 'Socket status' },
  { method: 'GET', path: '/api/v1/socket/users/online', category: 'Socket.io', description: 'Get online users' },
  { method: 'POST', path: '/api/v1/socket/broadcast', category: 'Socket.io', description: 'Broadcast message' },
  { method: 'GET', path: '/api/v1/socket/rooms', category: 'Socket.io', description: 'Get active rooms' },
  { method: 'POST', path: '/api/v1/socket/analytics', category: 'Socket.io', description: 'Send analytics event' },
  { method: 'GET', path: '/api/v1/socket/logs', category: 'Socket.io', description: 'Get socket logs' },
  { method: 'GET', path: '/api/v1/socket/metrics', category: 'Socket.io', description: 'Get socket metrics' },
  { method: 'POST', path: '/api/v1/socket/notify', category: 'Socket.io', description: 'Send notification' },
  { method: 'GET', path: '/api/v1/socket/memory', category: 'Socket.io', description: 'Get memory usage' },
  { method: 'POST', path: '/api/v1/socket/cleanup', category: 'Socket.io', description: 'Cleanup connections' },
  { method: 'POST', path: '/api/v1/socket/temp-token', category: 'Socket.io', description: 'Get temporary token' },

  // Dependencies
  { method: 'GET', path: '/dependencies', category: 'Dependencies', description: 'Dependency status' },
  { method: 'GET', path: '/dependencies/outdated', category: 'Dependencies', description: 'Check outdated packages' },
  { method: 'GET', path: '/dependencies/security', category: 'Dependencies', description: 'Security vulnerabilities' },
  { method: 'GET', path: '/dependencies/key', category: 'Dependencies', description: 'Key dependencies' },

  // Database
  { method: 'GET', path: '/database/stats', category: 'Database', description: 'Database statistics' },

  // Logs
  { method: 'GET', path: '/logs/status', category: 'Logs', description: 'Logging system status' },
  { method: 'GET', path: '/logs/recent', category: 'Logs', description: 'Get recent logs' },
  { method: 'GET', path: '/logs/db', category: 'Logs', description: 'Get database logs' },
  { method: 'GET', path: '/logs/db/stats', category: 'Logs', description: 'Database log statistics' },
  { method: 'POST', path: '/logs/reconnect', category: 'Logs', description: 'Force database reconnection' },
  { method: 'DELETE', path: '/logs/cleanup', category: 'Logs', description: 'Cleanup old logs' },
  { method: 'DELETE', path: '/logs/fallback/cleanup', category: 'Logs', description: 'Cleanup fallback files' },

  // Security
  { method: 'GET', path: '/security/dashboard', category: 'Security', description: 'Security dashboard' },
  { method: 'GET', path: '/security/events', category: 'Security', description: 'Get security events' },
  { method: 'GET', path: '/security/threats', category: 'Security', description: 'Get threat analysis' },
  { method: 'POST', path: '/security/block-ip', category: 'Security', description: 'Block IP address' },
];

// Group endpoints by category
const groupedEndpoints = endpoints.reduce((acc, endpoint) => {
  if (!acc[endpoint.category]) {
    acc[endpoint.category] = [];
  }
  acc[endpoint.category].push(endpoint);
  return acc;
}, {});

// Generate Postman collection
const generateCollection = () => {
  const collection = {
    info: {
      name: "HLenergy API - Auto-Generated Collection",
      description: "Auto-generated collection from landing page endpoints",
      version: "2.0.0",
      schema: "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
    },
    auth: {
      type: "bearer",
      bearer: [
        {
          key: "token",
          value: "{{authToken}}",
          type: "string"
        }
      ]
    },
    variable: [
      {
        key: "baseUrl",
        value: "http://localhost:3001",
        type: "string"
      },
      {
        key: "authToken",
        value: "",
        type: "string"
      }
    ],
    item: []
  };

  // Add folders for each category
  Object.keys(groupedEndpoints).forEach(category => {
    const folder = {
      name: `📁 ${category}`,
      item: []
    };

    groupedEndpoints[category].forEach(endpoint => {
      const request = {
        name: endpoint.description,
        request: {
          method: endpoint.method,
          header: [],
          url: {
            raw: `{{baseUrl}}${endpoint.path}`,
            host: ["{{baseUrl}}"],
            path: endpoint.path.split('/').filter(p => p)
          }
        }
      };

      // Add auth header for protected endpoints
      if (endpoint.path.includes('/api/v1/') && 
          !endpoint.path.includes('/auth/') && 
          !endpoint.path.includes('/contact/submit')) {
        request.request.header.push({
          key: "Authorization",
          value: "Bearer {{authToken}}"
        });
      }

      // Add content-type for POST/PATCH requests
      if (['POST', 'PATCH'].includes(endpoint.method)) {
        request.request.header.push({
          key: "Content-Type",
          value: "application/json"
        });

        // Add sample body for specific endpoints
        if (endpoint.path.includes('/auth/register')) {
          request.request.body = {
            mode: "raw",
            raw: JSON.stringify({
              name: "Test User",
              email: "<EMAIL>",
              password: "TestPassword123!"
            }, null, 2)
          };
        } else if (endpoint.path.includes('/auth/login')) {
          request.request.body = {
            mode: "raw",
            raw: JSON.stringify({
              email: "<EMAIL>",
              password: "TestPassword123!"
            }, null, 2)
          };
        } else if (endpoint.path.includes('/contact/submit')) {
          request.request.body = {
            mode: "raw",
            raw: JSON.stringify({
              name: "Jane Doe",
              email: "<EMAIL>",
              message: "Interested in energy consultation"
            }, null, 2)
          };
        } else {
          request.request.body = {
            mode: "raw",
            raw: "{}"
          };
        }
      }

      folder.item.push(request);
    });

    collection.item.push(folder);
  });

  return collection;
};

// Generate and save collection
const collection = generateCollection();
const outputPath = path.join(__dirname, '..', 'postman', 'HLenergy-Auto-Generated-Collection.json');

// Ensure directory exists
const dir = path.dirname(outputPath);
if (!fs.existsSync(dir)) {
  fs.mkdirSync(dir, { recursive: true });
}

// Write collection file
fs.writeFileSync(outputPath, JSON.stringify(collection, null, 2));

console.log('🎉 Postman Collection Generated Successfully!');
console.log(`📁 Location: ${outputPath}`);
console.log(`📊 Total Endpoints: ${endpoints.length}`);
console.log(`📂 Categories: ${Object.keys(groupedEndpoints).length}`);
console.log('\n📋 Categories:');
Object.keys(groupedEndpoints).forEach(category => {
  console.log(`   📁 ${category}: ${groupedEndpoints[category].length} endpoints`);
});

console.log('\n🚀 To import in Postman:');
console.log('1. Open Postman');
console.log('2. Click "Import" → "Files"');
console.log(`3. Select: ${outputPath}`);
console.log('4. Click "Import"');
console.log('\n✅ Done! All endpoints ready for testing.');

#!/usr/bin/env node

/**
 * Check Admin User
 * Checks the admin user details and tests password verification
 */

require('dotenv').config();
const bcrypt = require('bcryptjs');
const { User } = require('./shared/models');

const checkAdminUser = async () => {
  try {
    console.log('🔍 Checking admin user...');
    
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const testPassword = process.env.ADMIN_PASSWORD || 'admin123456';
    
    // Find admin user
    const admin = await User.findOne({
      where: { email: adminEmail }
    });
    
    if (!admin) {
      console.log('❌ Admin user not found');
      return;
    }
    
    console.log('✅ Admin user found:');
    console.log('   - ID:', admin.id);
    console.log('   - Name:', admin.name);
    console.log('   - Email:', admin.email);
    console.log('   - Role:', admin.role);
    console.log('   - Active:', admin.is_active);
    console.log('   - <PERSON><PERSON> Verified:', admin.email_verified);
    console.log('   - Password Hash:', admin.password.substring(0, 20) + '...');
    
    // Test password verification
    console.log('\n🔐 Testing password verification...');
    console.log('   - Test Password:', testPassword);
    
    const isValidPassword = await bcrypt.compare(testPassword, admin.password);
    console.log('   - Password Valid:', isValidPassword);
    
    if (!isValidPassword) {
      console.log('\n⚠️  Password verification failed. Let\'s try to update it...');

      // Update the user's password (the beforeUpdate hook will hash it automatically)
      await admin.update({ password: testPassword });

      console.log('✅ Password updated successfully');

      // Reload the user to get the new password hash
      await admin.reload();

      // Test again
      const isValidNow = await bcrypt.compare(testPassword, admin.password);
      console.log('   - Password Valid Now:', isValidNow);
    }

    // Reset login attempts
    if (admin.login_attempts > 0) {
      console.log('\n🔄 Resetting login attempts...');
      await admin.update({
        login_attempts: 0,
        locked_until: null
      });
      console.log('✅ Login attempts reset');
    }
    
    return admin;
    
  } catch (error) {
    console.error('❌ Error checking admin user:', error.message);
    throw error;
  }
};

// Run if called directly
if (require.main === module) {
  checkAdminUser()
    .then(() => {
      console.log('\n🎉 Admin user check complete!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Check failed:', error.message);
      process.exit(1);
    });
}

module.exports = { checkAdminUser };

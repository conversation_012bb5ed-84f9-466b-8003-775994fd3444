{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./shared", "strict": false, "noEmit": false, "skipLibCheck": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "noImplicitThis": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["shared/*"]}, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["shared/**/*"], "exclude": ["node_modules", "dist"]}
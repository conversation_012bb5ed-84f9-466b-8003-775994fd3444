const request = require('supertest');
const app = require('../../src/index');

describe('Endpoint Availability Test Suite', () => {
  // Test all endpoints from the landing page to ensure they exist and return proper responses
  
  const publicEndpoints = [
    { method: 'GET', path: '/health', expectedStatus: 200 },
    { method: 'GET', path: '/version', expectedStatus: 200 },
    { method: 'GET', path: '/version/summary', expectedStatus: 200 },
    { method: 'GET', path: '/api/test', expectedStatus: 200 },
    { method: 'GET', path: '/dependencies', expectedStatus: 200 },
    { method: 'GET', path: '/dependencies/outdated', expectedStatus: 200 },
    { method: 'GET', path: '/dependencies/security', expectedStatus: 200 },
    { method: 'GET', path: '/dependencies/key', expectedStatus: 200 },
    { method: 'GET', path: '/database/stats', expectedStatus: 200 },
    { method: 'GET', path: '/logs/status', expectedStatus: 200 },
    { method: 'GET', path: '/logs/recent', expectedStatus: 200 },
    { method: 'GET', path: '/logs/db', expectedStatus: [200, 404, 500] },
    { method: 'GET', path: '/logs/db/stats', expectedStatus: [200, 500] },
    { method: 'POST', path: '/logs/reconnect', expectedStatus: 200 },
    { method: 'GET', path: '/api/v1/socket/health', expectedStatus: 200 },
    { method: 'GET', path: '/api/v1/socket/status', expectedStatus: 200 },
    { method: 'GET', path: '/api/v1/socket/users/online', expectedStatus: 200 },
    { method: 'GET', path: '/api/v1/socket/rooms', expectedStatus: 200 },
    { method: 'GET', path: '/api/v1/socket/logs', expectedStatus: 200 },
    { method: 'GET', path: '/api/v1/socket/metrics', expectedStatus: 200 },
    { method: 'GET', path: '/api/v1/socket/memory', expectedStatus: 200 },
    { method: 'GET', path: '/api/v1/analytics/metrics', expectedStatus: 200 },
    { method: 'GET', path: '/api/v1/analytics/business-metrics', expectedStatus: 200 },
  ];

  const authRequiredEndpoints = [
    { method: 'POST', path: '/api/v1/auth/register', expectedStatus: [201, 400] },
    { method: 'POST', path: '/api/v1/auth/login', expectedStatus: [200, 400, 401] },
    { method: 'POST', path: '/api/v1/auth/refresh', expectedStatus: [200, 401] },
    { method: 'POST', path: '/api/v1/contact/submit', expectedStatus: [201, 400] },
    { method: 'GET', path: '/api/v1/contact', expectedStatus: [200, 401, 403] },
    { method: 'GET', path: '/api/v1/profile', expectedStatus: [200, 401] },
    { method: 'PATCH', path: '/api/v1/profile', expectedStatus: [200, 400, 401] },
    { method: 'GET', path: '/api/v1/profile/sessions', expectedStatus: [200, 401] },
    { method: 'POST', path: '/api/v1/email/send-verification', expectedStatus: [200, 400, 401, 404] },
    { method: 'POST', path: '/api/v1/email/verify', expectedStatus: [200, 400, 404] },
    { method: 'POST', path: '/api/v1/email/forgot-password', expectedStatus: [200, 400, 404] },
    { method: 'POST', path: '/api/v1/email/reset-password', expectedStatus: [200, 400, 404] },
    { method: 'GET', path: '/api/v1/email-queue/status', expectedStatus: [200, 401, 404] },
    { method: 'GET', path: '/api/v1/email-queue/emails', expectedStatus: [200, 401, 404] },
    { method: 'POST', path: '/api/v1/socket/broadcast', expectedStatus: [200, 400, 401, 403] },
    { method: 'POST', path: '/api/v1/socket/test', expectedStatus: [200, 400] },
  ];

  const adminOnlyEndpoints = [
    { method: 'GET', path: '/api/v1/admin/dashboard', expectedStatus: [200, 401, 403, 404] },
    { method: 'GET', path: '/api/v1/admin/users', expectedStatus: [200, 401, 403, 404] },
    { method: 'GET', path: '/security/dashboard', expectedStatus: [200, 401, 403] },
    { method: 'GET', path: '/security/events', expectedStatus: [200, 401, 403] },
    { method: 'GET', path: '/security/threats', expectedStatus: [200, 401, 403] },
    { method: 'POST', path: '/security/block-ip', expectedStatus: [200, 400, 401, 403] },
  ];

  describe('Public Endpoints - Should be accessible without authentication', () => {
    publicEndpoints.forEach(({ method, path, expectedStatus }) => {
      test(`${method} ${path} - Should return ${expectedStatus}`, async () => {
        const response = await request(app)[method.toLowerCase()](path);
        
        expect(response.status).toBe(expectedStatus);
        
        // Basic response structure checks
        if (response.status === 200) {
          expect(response.body).toBeDefined();
          expect(typeof response.body).toBe('object');
        }
      });
    });
  });

  describe('Authentication Required Endpoints - Should handle auth properly', () => {
    authRequiredEndpoints.forEach(({ method, path, expectedStatus }) => {
      test(`${method} ${path} - Should handle authentication`, async () => {
        const response = await request(app)[method.toLowerCase()](path)
          .send({}); // Send empty body for POST requests
        
        const statusArray = Array.isArray(expectedStatus) ? expectedStatus : [expectedStatus];
        expect(statusArray).toContain(response.status);
        
        // Check response structure
        expect(response.body).toBeDefined();
        expect(typeof response.body).toBe('object');
      });
    });
  });

  describe('Admin Only Endpoints - Should require admin privileges', () => {
    adminOnlyEndpoints.forEach(({ method, path, expectedStatus }) => {
      test(`${method} ${path} - Should require admin access`, async () => {
        const response = await request(app)[method.toLowerCase()](path)
          .send({}); // Send empty body for POST requests
        
        const statusArray = Array.isArray(expectedStatus) ? expectedStatus : [expectedStatus];
        expect(statusArray).toContain(response.status);
        
        // Check response structure
        expect(response.body).toBeDefined();
        expect(typeof response.body).toBe('object');
      });
    });
  });

  describe('Special Endpoint Tests', () => {
    test('POST /api/v1/analytics/events - Should accept analytics data', async () => {
      const analyticsData = {
        events: [
          {
            event: 'test_event',
            category: 'test',
            properties: { test: true },
            sessionId: 'test-session',
            userId: 'test-user'
          }
        ]
      };

      const response = await request(app)
        .post('/api/v1/analytics/events')
        .send(analyticsData);

      expect([200, 400]).toContain(response.status);
    });

    test('GET /api/v1/analytics/heatmap - Should require page parameter', async () => {
      const response = await request(app)
        .get('/api/v1/analytics/heatmap')
        .query({ page: '/test' });

      expect([200, 400]).toContain(response.status);
    });

    test('DELETE /logs/cleanup - Should handle log cleanup', async () => {
      const response = await request(app)
        .delete('/logs/cleanup')
        .query({ days: 30 });

      expect([200, 500]).toContain(response.status);
    });

    test('DELETE /logs/fallback/cleanup - Should handle fallback cleanup', async () => {
      const response = await request(app)
        .delete('/logs/fallback/cleanup')
        .query({ days: 7 });

      expect([200, 500]).toContain(response.status);
    });
  });

  describe('API Documentation Endpoints', () => {
    test('GET /api-docs.json - Should return OpenAPI spec', async () => {
      const response = await request(app)
        .get('/api-docs.json')
        .expect(200);

      expect(response.body.openapi || response.body.swagger).toBeDefined();
      expect(response.body.info).toBeDefined();
      expect(response.body.paths).toBeDefined();
    });

    test('GET /api/versions - Should return versioning information', async () => {
      const response = await request(app)
        .get('/api/versions')
        .expect(200);

      expect(response.body.message).toContain('API Versioning');
      expect(response.body.versioning).toBeDefined();
    });

    test('GET /api - Should return API information', async () => {
      const response = await request(app)
        .get('/api')
        .expect(200);

      expect(response.body.message).toContain('Welcome to HLenergy API');
      expect(response.body.endpoints).toBeDefined();
      expect(response.body.documentation).toBeDefined();
    });

    test('GET /api/v1 - Should return v1 API information', async () => {
      const response = await request(app)
        .get('/api/v1')
        .expect(200);

      expect(response.body.message).toContain('HLenergy API v1');
      expect(response.body.endpoints).toBeDefined();
    });
  });

  describe('Landing Page', () => {
    test('GET / - Should return HTML landing page', async () => {
      const response = await request(app)
        .get('/')
        .expect(200);

      expect(response.text).toContain('HLenergy API');
      expect(response.text).toContain('Available Endpoints');
      expect(response.headers['content-type']).toContain('text/html');
    });
  });

  describe('Response Headers and Security', () => {
    test('Should include security headers', async () => {
      const response = await request(app)
        .get('/health');

      expect(response.headers['x-content-type-options']).toBeDefined();
      expect(response.headers['x-frame-options']).toBeDefined();
    });

    test('Should include API version headers for versioned endpoints', async () => {
      const response = await request(app)
        .get('/api/v1');

      expect(response.headers['api-version']).toBeDefined();
    });
  });
});

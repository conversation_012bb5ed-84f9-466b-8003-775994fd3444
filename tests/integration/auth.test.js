const request = require('supertest')
const app = require('../src/app')
const { pool } = require('../src/config/database')

describe('Authentication API', () => {
  let testUser = {
    name: 'Test User',
    email: '<EMAIL>',
    password: 'password123'
  }

  beforeEach(async () => {
    // Clean up test data
    await pool.execute('DELETE FROM users WHERE email = ?', [testUser.email])
  })

  afterAll(async () => {
    // Clean up and close connections
    await pool.execute('DELETE FROM users WHERE email = ?', [testUser.email])
    await pool.end()
  })

  describe('POST /api/v1/auth/register', () => {
    it('should register a new user successfully', async () => {
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(testUser)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.data.user.email).toBe(testUser.email)
      expect(response.body.data.user.name).toBe(testUser.name)
      expect(response.body.data.token).toBeDefined()
      expect(response.body.data.user.password).toBeUndefined()
    })

    it('should not register user with invalid email', async () => {
      const invalidUser = { ...testUser, email: 'invalid-email' }
      
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(invalidUser)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toContain('email')
    })

    it('should not register user with weak password', async () => {
      const weakPasswordUser = { ...testUser, password: '123' }
      
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(weakPasswordUser)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toContain('password')
    })

    it('should not register user with duplicate email', async () => {
      // Register user first time
      await request(app)
        .post('/api/v1/auth/register')
        .send(testUser)
        .expect(201)

      // Try to register again with same email
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(testUser)
        .expect(409)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toContain('already exists')
    })

    it('should handle missing required fields', async () => {
      const incompleteUser = { email: testUser.email }
      
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(incompleteUser)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toContain('required')
    })
  })

  describe('POST /api/v1/auth/login', () => {
    beforeEach(async () => {
      // Register user for login tests
      await request(app)
        .post('/api/v1/auth/register')
        .send(testUser)
    })

    it('should login user with correct credentials', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.user.email).toBe(testUser.email)
      expect(response.body.data.token).toBeDefined()
      expect(response.body.data.user.password).toBeUndefined()
    })

    it('should not login with incorrect password', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          email: testUser.email,
          password: 'wrongpassword'
        })
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toContain('Invalid')
    })

    it('should not login with non-existent email', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          email: '<EMAIL>',
          password: testUser.password
        })
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toContain('Invalid')
    })

    it('should handle missing credentials', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({})
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toContain('required')
    })
  })

  describe('GET /api/v1/auth/me', () => {
    let authToken

    beforeEach(async () => {
      // Register and login to get token
      const registerResponse = await request(app)
        .post('/api/v1/auth/register')
        .send(testUser)

      authToken = registerResponse.body.data.token
    })

    it('should get current user with valid token', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.user.email).toBe(testUser.email)
      expect(response.body.data.user.password).toBeUndefined()
    })

    it('should not get user without token', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toContain('token')
    })

    it('should not get user with invalid token', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toContain('token')
    })
  })

  describe('POST /api/v1/auth/logout', () => {
    let authToken

    beforeEach(async () => {
      const registerResponse = await request(app)
        .post('/api/v1/auth/register')
        .send(testUser)

      authToken = registerResponse.body.data.token
    })

    it('should logout user successfully', async () => {
      const response = await request(app)
        .post('/api/v1/auth/logout')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.message).toContain('logged out')
    })

    it('should handle logout without token', async () => {
      const response = await request(app)
        .post('/api/v1/auth/logout')
        .expect(401)

      expect(response.body.success).toBe(false)
    })
  })

  describe('Rate Limiting', () => {
    it('should rate limit login attempts', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      }

      // Make multiple failed login attempts
      for (let i = 0; i < 6; i++) {
        await request(app)
          .post('/api/v1/auth/login')
          .send(loginData)
      }

      // Next attempt should be rate limited
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(429)

      expect(response.body.message).toContain('rate limit')
    })
  })

  describe('Security Headers', () => {
    it('should include security headers', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .expect(401)

      expect(response.headers['x-content-type-options']).toBe('nosniff')
      expect(response.headers['x-frame-options']).toBe('DENY')
      expect(response.headers['x-xss-protection']).toBe('1; mode=block')
    })
  })
})

const request = require('supertest')
const app = require('../src/app')
const { pool } = require('../src/config/database')

describe('Contact API', () => {
  const validContactData = {
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567890',
    company: 'Test Company',
    message: 'This is a test message for energy consultation services.'
  }

  beforeEach(async () => {
    // Clean up test data
    await pool.execute('DELETE FROM contact_submissions WHERE email = ?', [validContactData.email])
  })

  afterAll(async () => {
    // Clean up and close connections
    await pool.execute('DELETE FROM contact_submissions WHERE email = ?', [validContactData.email])
    await pool.end()
  })

  describe('POST /api/v1/contact', () => {
    it('should submit contact form successfully', async () => {
      const response = await request(app)
        .post('/api/v1/contact')
        .send(validContactData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.message).toContain('submitted successfully')
      expect(response.body.data.id).toBeDefined()

      // Verify data was saved to database
      const [rows] = await pool.execute(
        'SELECT * FROM contact_submissions WHERE email = ?',
        [validContactData.email]
      )
      expect(rows.length).toBe(1)
      expect(rows[0].name).toBe(validContactData.name)
      expect(rows[0].email).toBe(validContactData.email)
    })

    it('should validate required fields', async () => {
      const incompleteData = {
        name: 'John Doe'
        // Missing email, message
      }

      const response = await request(app)
        .post('/api/v1/contact')
        .send(incompleteData)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toContain('required')
    })

    it('should validate email format', async () => {
      const invalidEmailData = {
        ...validContactData,
        email: 'invalid-email'
      }

      const response = await request(app)
        .post('/api/v1/contact')
        .send(invalidEmailData)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toContain('email')
    })

    it('should validate phone number format', async () => {
      const invalidPhoneData = {
        ...validContactData,
        phone: 'invalid-phone'
      }

      const response = await request(app)
        .post('/api/v1/contact')
        .send(invalidPhoneData)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toContain('phone')
    })

    it('should sanitize input data', async () => {
      const maliciousData = {
        ...validContactData,
        name: '<script>alert("xss")</script>John Doe',
        message: '<img src="x" onerror="alert(1)">Test message'
      }

      const response = await request(app)
        .post('/api/v1/contact')
        .send(maliciousData)
        .expect(201)

      expect(response.body.success).toBe(true)

      // Verify malicious content was sanitized
      const [rows] = await pool.execute(
        'SELECT * FROM contact_submissions WHERE email = ?',
        [validContactData.email]
      )
      expect(rows[0].name).not.toContain('<script>')
      expect(rows[0].message).not.toContain('<img')
    })

    it('should handle duplicate submissions', async () => {
      // Submit first time
      await request(app)
        .post('/api/v1/contact')
        .send(validContactData)
        .expect(201)

      // Submit again with same email
      const response = await request(app)
        .post('/api/v1/contact')
        .send(validContactData)
        .expect(201) // Should still succeed but might have different handling

      expect(response.body.success).toBe(true)
    })

    it('should queue email notification', async () => {
      const response = await request(app)
        .post('/api/v1/contact')
        .send(validContactData)
        .expect(201)

      expect(response.body.success).toBe(true)

      // Verify email was queued
      const [emailRows] = await pool.execute(
        'SELECT * FROM email_queue WHERE recipient_email = ? AND status = "pending"',
        ['<EMAIL>'] // Assuming admin email
      )
      expect(emailRows.length).toBeGreaterThan(0)
    })

    it('should handle database errors gracefully', async () => {
      // Mock database error by using invalid data that would cause constraint violation
      const invalidData = {
        ...validContactData,
        email: 'a'.repeat(300) // Assuming email field has length limit
      }

      const response = await request(app)
        .post('/api/v1/contact')
        .send(invalidData)
        .expect(500)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toContain('error')
    })

    it('should rate limit contact submissions', async () => {
      // Make multiple submissions quickly
      for (let i = 0; i < 6; i++) {
        await request(app)
          .post('/api/v1/contact')
          .send({
            ...validContactData,
            email: `test${i}@example.com`
          })
      }

      // Next submission should be rate limited
      const response = await request(app)
        .post('/api/v1/contact')
        .send({
          ...validContactData,
          email: '<EMAIL>'
        })
        .expect(429)

      expect(response.body.message).toContain('rate limit')
    })

    it('should log contact submission', async () => {
      const response = await request(app)
        .post('/api/v1/contact')
        .send(validContactData)
        .expect(201)

      expect(response.body.success).toBe(true)

      // Verify log entry was created
      const [logRows] = await pool.execute(
        'SELECT * FROM logs WHERE action = "contact_form_submission" AND user_email = ?',
        [validContactData.email]
      )
      expect(logRows.length).toBeGreaterThan(0)
    })
  })

  describe('GET /api/v1/contact (Admin only)', () => {
    let adminToken

    beforeEach(async () => {
      // Create admin user and get token
      const adminUser = {
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'adminpass123',
        role: 'admin'
      }

      const registerResponse = await request(app)
        .post('/api/v1/auth/register')
        .send(adminUser)

      adminToken = registerResponse.body.data.token

      // Create some test contact submissions
      await request(app)
        .post('/api/v1/contact')
        .send(validContactData)
    })

    afterEach(async () => {
      // Clean up admin user
      await pool.execute('DELETE FROM users WHERE email = ?', ['<EMAIL>'])
    })

    it('should get contact submissions for admin', async () => {
      const response = await request(app)
        .get('/api/v1/contact')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.submissions).toBeDefined()
      expect(Array.isArray(response.body.data.submissions)).toBe(true)
    })

    it('should not allow non-admin access', async () => {
      // Create regular user
      const regularUser = {
        name: 'Regular User',
        email: '<EMAIL>',
        password: 'userpass123'
      }

      const userResponse = await request(app)
        .post('/api/v1/auth/register')
        .send(regularUser)

      const userToken = userResponse.body.data.token

      const response = await request(app)
        .get('/api/v1/contact')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toContain('admin')

      // Clean up
      await pool.execute('DELETE FROM users WHERE email = ?', ['<EMAIL>'])
    })

    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/v1/contact?page=1&limit=10')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.pagination).toBeDefined()
      expect(response.body.data.pagination.page).toBe(1)
      expect(response.body.data.pagination.limit).toBe(10)
    })

    it('should support filtering by date', async () => {
      const today = new Date().toISOString().split('T')[0]
      
      const response = await request(app)
        .get(`/api/v1/contact?from=${today}&to=${today}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.submissions).toBeDefined()
    })
  })
})

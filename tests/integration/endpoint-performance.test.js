const request = require('supertest');
const app = require('../../src/index');

describe('Endpoint Performance Tests', () => {
  const PERFORMANCE_THRESHOLD = 2000; // 2 seconds max response time
  const FAST_THRESHOLD = 500; // 500ms for fast endpoints

  describe('Critical Endpoints Performance', () => {
    test('GET /health - Should respond quickly', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/health')
        .expect(200);
      
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(FAST_THRESHOLD);
      expect(response.body.status).toBeDefined();
      
      console.log(`Health check took ${duration}ms`);
    });

    test('GET /api/test - Should respond quickly', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/api/test')
        .expect(200);
      
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(FAST_THRESHOLD);
      expect(response.body.success).toBe(true);
      
      console.log(`API test took ${duration}ms`);
    });

    test('GET /version/summary - Should respond quickly', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/version/summary')
        .expect(200);
      
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(FAST_THRESHOLD);
      
      console.log(`Version summary took ${duration}ms`);
    });
  });

  describe('Database-dependent Endpoints Performance', () => {
    test('GET /database/stats - Should respond within threshold', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/database/stats')
        .expect(200);
      
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD);
      expect(response.body.data).toBeDefined();
      
      console.log(`Database stats took ${duration}ms`);
    });

    test('GET /logs/db/stats - Should respond within threshold', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/logs/db/stats')
        .expect(200);
      
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD);
      
      console.log(`Database log stats took ${duration}ms`);
    });

    test('GET /logs/recent - Should respond within threshold', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/logs/recent')
        .query({ limit: 10 })
        .expect(200);
      
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD);
      
      console.log(`Recent logs took ${duration}ms`);
    });
  });

  describe('Analytics Endpoints Performance', () => {
    test('POST /api/v1/analytics/events - Should handle events quickly', async () => {
      const eventsData = {
        events: [
          {
            event: 'performance_test',
            category: 'test',
            properties: { timestamp: Date.now() },
            sessionId: 'perf-test-session',
            userId: 'perf-test-user'
          }
        ]
      };

      const start = Date.now();
      
      const response = await request(app)
        .post('/api/v1/analytics/events')
        .send(eventsData)
        .expect(200);
      
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD);
      expect(response.body.success).toBe(true);
      
      console.log(`Analytics events took ${duration}ms`);
    });

    test('GET /api/v1/analytics/metrics - Should respond within threshold', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/api/v1/analytics/metrics')
        .query({ timeRange: '24h' })
        .expect(200);
      
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD);
      
      console.log(`Analytics metrics took ${duration}ms`);
    });
  });

  describe('Socket.io Endpoints Performance', () => {
    test('GET /api/v1/socket/health - Should respond quickly', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/api/v1/socket/health')
        .expect(200);
      
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(FAST_THRESHOLD);
      
      console.log(`Socket health took ${duration}ms`);
    });

    test('GET /api/v1/socket/users/online - Should respond quickly', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/api/v1/socket/users/online')
        .expect(200);
      
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(FAST_THRESHOLD);
      
      console.log(`Socket users online took ${duration}ms`);
    });
  });

  describe('Dependencies Endpoints Performance', () => {
    test('GET /dependencies - Should respond within threshold', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/dependencies')
        .expect(200);
      
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD);
      
      console.log(`Dependencies check took ${duration}ms`);
    });

    test('GET /dependencies/key - Should respond within threshold', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/dependencies/key')
        .expect(200);
      
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD);
      
      console.log(`Key dependencies took ${duration}ms`);
    });
  });

  describe('Concurrent Request Performance', () => {
    test('Should handle multiple concurrent requests', async () => {
      const concurrentRequests = 10;
      const start = Date.now();
      
      const promises = Array(concurrentRequests).fill().map(() =>
        request(app).get('/health')
      );
      
      const responses = await Promise.all(promises);
      const duration = Date.now() - start;
      
      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
      
      // Average response time should be reasonable
      const avgResponseTime = duration / concurrentRequests;
      expect(avgResponseTime).toBeLessThan(PERFORMANCE_THRESHOLD);
      
      console.log(`${concurrentRequests} concurrent requests took ${duration}ms (avg: ${avgResponseTime}ms)`);
    });

    test('Should handle mixed endpoint requests concurrently', async () => {
      const endpoints = [
        '/health',
        '/version/summary',
        '/api/test',
        '/api/v1/socket/health',
        '/dependencies/key'
      ];
      
      const start = Date.now();
      
      const promises = endpoints.map(endpoint =>
        request(app).get(endpoint)
      );
      
      const responses = await Promise.all(promises);
      const duration = Date.now() - start;
      
      // All requests should succeed
      responses.forEach((response, index) => {
        expect(response.status).toBe(200);
        console.log(`${endpoints[index]} responded in concurrent test`);
      });
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD * 2);
      
      console.log(`Mixed concurrent requests took ${duration}ms`);
    });
  });

  describe('Large Response Performance', () => {
    test('GET /logs/db - Should handle large log responses efficiently', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/logs/db')
        .query({ page: 1, limit: 100 })
        .expect(200);
      
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD);
      
      console.log(`Large log response (100 items) took ${duration}ms`);
    });

    test('GET /api-docs.json - Should serve OpenAPI spec efficiently', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/api-docs.json')
        .expect(200);
      
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD);
      expect(response.body.paths).toBeDefined();
      
      console.log(`OpenAPI spec took ${duration}ms`);
    });
  });

  describe('Memory Usage Monitoring', () => {
    test('Should not cause significant memory leaks during testing', async () => {
      const initialMemory = process.memoryUsage();
      
      // Make multiple requests to different endpoints
      for (let i = 0; i < 50; i++) {
        await request(app).get('/health');
        await request(app).get('/version/summary');
        await request(app).get('/api/test');
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      console.log(`Memory increase after 150 requests: ${Math.round(memoryIncrease / 1024 / 1024)}MB`);
      
      // Memory increase should be reasonable (less than 50MB for this test)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });
  });
});

const request = require('supertest');
const app = require('../../src/index');

describe('HLenergy API - All Endpoints Test Suite', () => {
  let userToken = null;
  let adminToken = null;
  let testUser = null;
  let testAdmin = null;

  // Setup: Create test users and get tokens
  beforeAll(async () => {
    // Create regular user
    testUser = global.testHelpers.getTestUser();
    const userResponse = await request(app)
      .post('/api/v1/auth/register')
      .send(testUser);
    
    if (userResponse.status === 201) {
      userToken = userResponse.body.data.token;
    }

    // Create admin user (simulate admin creation)
    testAdmin = global.testHelpers.getTestAdmin();
    const adminResponse = await request(app)
      .post('/api/v1/auth/register')
      .send(testAdmin);
    
    if (adminResponse.status === 201) {
      adminToken = adminResponse.body.data.token;
    }
  });

  describe('System Endpoints', () => {
    test('GET /health - Health check', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body.status).toBeDefined();
      expect(response.body.message).toContain('HLenergy API Server');
      expect(response.body.version).toBeDefined();
      expect(response.body.timestamp).toBeDefined();
    });

    test('GET /version - Version information', async () => {
      const response = await request(app)
        .get('/version')
        .expect(200);

      expect(response.body.server).toBeDefined();
      expect(response.body.api).toBeDefined();
      expect(response.body.git).toBeDefined();
    });

    test('GET /version/summary - Version summary', async () => {
      const response = await request(app)
        .get('/version/summary')
        .expect(200);

      expect(response.body.server_version).toBeDefined();
      expect(response.body.api_version).toBeDefined();
      expect(response.body.environment).toBeDefined();
    });

    test('GET /api/test - API test endpoint', async () => {
      const response = await request(app)
        .get('/api/test')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Backend API is working');
    });
  });

  describe('Authentication Endpoints (v1)', () => {
    test('POST /api/v1/auth/register - User registration', async () => {
      const newUser = global.testHelpers.getTestUser();
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(newUser)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(newUser.email);
      expect(response.body.data.token).toBeDefined();
    });

    test('POST /api/v1/auth/login - User login', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(testUser.email);
      expect(response.body.data.token).toBeDefined();
    });

    test('POST /api/v1/auth/refresh - Token refresh', async () => {
      if (!userToken) {
        console.log('Skipping refresh test - no user token available');
        return;
      }

      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.token).toBeDefined();
    });
  });

  describe('Contact Endpoints (v1)', () => {
    test('POST /api/v1/contact/submit - Submit contact form', async () => {
      const contactData = {
        name: 'Test Contact',
        email: '<EMAIL>',
        message: 'This is a test contact message',
        phone: '+1234567890'
      };

      const response = await request(app)
        .post('/api/v1/contact/submit')
        .send(contactData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.submission.name).toBe(contactData.name);
      expect(response.body.data.emailQueued).toBeDefined();
    });

    test('GET /api/v1/contact - Get contact submissions (requires auth)', async () => {
      if (!userToken) {
        console.log('Skipping contact list test - no user token available');
        return;
      }

      const response = await request(app)
        .get('/api/v1/contact')
        .set('Authorization', `Bearer ${userToken}`);

      // May return 200 or 403 depending on user role
      expect([200, 403]).toContain(response.status);
    });
  });

  describe('Dependencies Endpoints', () => {
    test('GET /dependencies - Dependency status', async () => {
      const response = await request(app)
        .get('/dependencies')
        .expect(200);

      expect(response.body.package).toBeDefined();
      expect(response.body.health).toBeDefined();
    });

    test('GET /dependencies/outdated - Check outdated packages', async () => {
      const response = await request(app)
        .get('/dependencies/outdated')
        .expect(200);

      expect(response.body.status).toBeDefined();
      expect(response.body.count).toBeDefined();
    });

    test('GET /dependencies/security - Security vulnerabilities', async () => {
      const response = await request(app)
        .get('/dependencies/security')
        .expect(200);

      expect(response.body.status).toBeDefined();
      expect(response.body.vulnerabilities).toBeDefined();
    });

    test('GET /dependencies/key - Key dependencies', async () => {
      const response = await request(app)
        .get('/dependencies/key')
        .expect(200);

      expect(response.body.message).toBeDefined();
      expect(response.body.dependencies).toBeDefined();
    });
  });

  describe('Database Endpoints', () => {
    test('GET /database/stats - Database statistics', async () => {
      const response = await request(app)
        .get('/database/stats')
        .expect(200);

      expect(response.body.message).toBeDefined();
      expect(response.body.data).toBeDefined();
    });
  });

  describe('Analytics Endpoints (v1)', () => {
    test('POST /api/v1/analytics/events - Track analytics events', async () => {
      const eventsData = {
        events: [
          {
            event: 'page_view',
            category: 'navigation',
            properties: {
              page: '/test',
              timestamp: Date.now()
            },
            sessionId: 'test-session-123',
            userId: 'test-user-456'
          }
        ]
      };

      const response = await request(app)
        .post('/api/v1/analytics/events')
        .send(eventsData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.eventsProcessed).toBe(1);
    });

    test('GET /api/v1/analytics/metrics - Get analytics metrics', async () => {
      const response = await request(app)
        .get('/api/v1/analytics/metrics')
        .query({ timeRange: '24h' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.timeRange).toBe('24h');
    });

    test('GET /api/v1/analytics/business-metrics - Get business metrics', async () => {
      const response = await request(app)
        .get('/api/v1/analytics/business-metrics')
        .query({ timeRange: '7d' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.revenue).toBeDefined();
    });

    test('GET /api/v1/analytics/heatmap - Get heatmap data', async () => {
      const response = await request(app)
        .get('/api/v1/analytics/heatmap')
        .query({ page: '/test' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.page).toBe('/test');
    });
  });

  describe('Socket.io Endpoints (v1)', () => {
    test('GET /api/v1/socket/health - Socket.io health check', async () => {
      const response = await request(app)
        .get('/api/v1/socket/health')
        .expect(200);

      expect(response.body.status).toBeDefined();
      expect(response.body.connectedUsers).toBeDefined();
    });

    test('GET /api/v1/socket/users/online - Get online users', async () => {
      const response = await request(app)
        .get('/api/v1/socket/users/online')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.users).toBeDefined();
      expect(response.body.count).toBeDefined();
    });

    test('GET /api/v1/socket/rooms - Get active rooms', async () => {
      const response = await request(app)
        .get('/api/v1/socket/rooms')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.rooms).toBeDefined();
      expect(response.body.totalRooms).toBeDefined();
    });

    test('POST /api/v1/socket/broadcast - Broadcast message (requires auth)', async () => {
      if (!adminToken) {
        console.log('Skipping socket broadcast test - no admin token available');
        return;
      }

      const broadcastData = {
        event: 'test_broadcast',
        message: 'Test broadcast message',
        data: { test: true }
      };

      const response = await request(app)
        .post('/api/v1/socket/broadcast')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(broadcastData);

      // May return 200 or 401/403 depending on auth
      expect([200, 401, 403]).toContain(response.status);
    });

    test('POST /api/v1/socket/test - Test Socket.io connection', async () => {
      const testData = {
        message: 'Test socket message'
      };

      const response = await request(app)
        .post('/api/v1/socket/test')
        .send(testData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('sent');
    });
  });

  describe('Logs Endpoints', () => {
    test('GET /logs/status - Logging system status', async () => {
      const response = await request(app)
        .get('/logs/status')
        .expect(200);

      expect(response.body.message).toContain('Logging system status');
      expect(response.body.status).toBeDefined();
    });

    test('GET /logs/recent - Get recent logs', async () => {
      const response = await request(app)
        .get('/logs/recent')
        .query({ limit: 10 })
        .expect(200);

      expect(response.body.message).toContain('Recent logs');
      expect(response.body.logs).toBeDefined();
      expect(response.body.count).toBeDefined();
    });

    test('GET /logs/db - Get database logs', async () => {
      const response = await request(app)
        .get('/logs/db')
        .query({ page: 1, limit: 10 })
        .expect(200);

      expect(response.body.message).toContain('Database logs');
      expect(response.body.logs).toBeDefined();
      expect(response.body.pagination).toBeDefined();
    });

    test('GET /logs/db/stats - Get database log statistics', async () => {
      const response = await request(app)
        .get('/logs/db/stats')
        .expect(200);

      expect(response.body.message).toContain('Database log statistics');
      expect(response.body.stats).toBeDefined();
    });

    test('POST /logs/reconnect - Force database reconnection', async () => {
      const response = await request(app)
        .post('/logs/reconnect')
        .expect(200);

      expect(response.body.message).toBeDefined();
      expect(response.body.success).toBeDefined();
    });
  });

  describe('Profile Endpoints (v1) - Authenticated', () => {
    test('GET /api/v1/profile - Get user profile (requires auth)', async () => {
      if (!userToken) {
        console.log('Skipping profile test - no user token available');
        return;
      }

      const response = await request(app)
        .get('/api/v1/profile')
        .set('Authorization', `Bearer ${userToken}`);

      // May return 200 or 401 depending on auth implementation
      expect([200, 401, 404]).toContain(response.status);
    });

    test('PATCH /api/v1/profile - Update user profile (requires auth)', async () => {
      if (!userToken) {
        console.log('Skipping profile update test - no user token available');
        return;
      }

      const updateData = {
        name: 'Updated Test User'
      };

      const response = await request(app)
        .patch('/api/v1/profile')
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData);

      // May return 200 or 401 depending on auth implementation
      expect([200, 401, 404]).toContain(response.status);
    });

    test('GET /api/v1/profile/sessions - Get user sessions (requires auth)', async () => {
      if (!userToken) {
        console.log('Skipping sessions test - no user token available');
        return;
      }

      const response = await request(app)
        .get('/api/v1/profile/sessions')
        .set('Authorization', `Bearer ${userToken}`);

      // May return 200 or 401 depending on auth implementation
      expect([200, 401, 404]).toContain(response.status);
    });
  });

  describe('Email Endpoints (v1)', () => {
    test('POST /api/v1/email/send-verification - Send verification email', async () => {
      const emailData = {
        email: global.testHelpers.randomEmail()
      };

      const response = await request(app)
        .post('/api/v1/email/send-verification')
        .send(emailData);

      // May return 200, 400, or 404 depending on implementation
      expect([200, 400, 404]).toContain(response.status);
    });

    test('POST /api/v1/email/forgot-password - Request password reset', async () => {
      const emailData = {
        email: testUser ? testUser.email : '<EMAIL>'
      };

      const response = await request(app)
        .post('/api/v1/email/forgot-password')
        .send(emailData);

      // May return 200, 400, or 404 depending on implementation
      expect([200, 400, 404]).toContain(response.status);
    });
  });

  describe('Email Queue Endpoints (v1)', () => {
    test('GET /api/v1/email-queue/status - Get email queue status', async () => {
      const response = await request(app)
        .get('/api/v1/email-queue/status');

      // May return 200 or 404 depending on implementation
      expect([200, 404]).toContain(response.status);
    });

    test('GET /api/v1/email-queue/emails - Get queued emails', async () => {
      const response = await request(app)
        .get('/api/v1/email-queue/emails')
        .query({ page: 1, limit: 10 });

      // May return 200, 401, or 404 depending on implementation
      expect([200, 401, 404]).toContain(response.status);
    });
  });

  describe('Admin Endpoints (v1) - Admin Only', () => {
    test('GET /api/v1/admin/dashboard - Admin dashboard (requires admin)', async () => {
      if (!adminToken) {
        console.log('Skipping admin dashboard test - no admin token available');
        return;
      }

      const response = await request(app)
        .get('/api/v1/admin/dashboard')
        .set('Authorization', `Bearer ${adminToken}`);

      // May return 200, 401, or 403 depending on auth implementation
      expect([200, 401, 403, 404]).toContain(response.status);
    });

    test('GET /api/v1/admin/users - Get all users (requires admin)', async () => {
      if (!adminToken) {
        console.log('Skipping admin users test - no admin token available');
        return;
      }

      const response = await request(app)
        .get('/api/v1/admin/users')
        .set('Authorization', `Bearer ${adminToken}`);

      // May return 200, 401, or 403 depending on auth implementation
      expect([200, 401, 403, 404]).toContain(response.status);
    });
  });

  describe('Security Endpoints - Admin Only', () => {
    test('GET /security/dashboard - Security dashboard (requires admin)', async () => {
      if (!adminToken) {
        console.log('Skipping security dashboard test - no admin token available');
        return;
      }

      const response = await request(app)
        .get('/security/dashboard')
        .set('Authorization', `Bearer ${adminToken}`);

      // May return 200, 401, or 403 depending on auth implementation
      expect([200, 401, 403]).toContain(response.status);
    });

    test('GET /security/events - Get security events (requires admin)', async () => {
      if (!adminToken) {
        console.log('Skipping security events test - no admin token available');
        return;
      }

      const response = await request(app)
        .get('/security/events')
        .set('Authorization', `Bearer ${adminToken}`)
        .query({ page: 1, limit: 10 });

      // May return 200, 401, or 403 depending on auth implementation
      expect([200, 401, 403]).toContain(response.status);
    });

    test('GET /security/threats - Get threat analysis (requires admin)', async () => {
      if (!adminToken) {
        console.log('Skipping security threats test - no admin token available');
        return;
      }

      const response = await request(app)
        .get('/security/threats')
        .set('Authorization', `Bearer ${adminToken}`);

      // May return 200, 401, or 403 depending on auth implementation
      expect([200, 401, 403]).toContain(response.status);
    });
  });

  describe('Error Handling', () => {
    test('GET /nonexistent-endpoint - 404 for non-existent endpoints', async () => {
      const response = await request(app)
        .get('/nonexistent-endpoint')
        .expect(404);

      expect(response.body.error).toContain('Route not found');
    });

    test('POST /api/v1/auth/login - 400 for invalid data', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({ invalid: 'data' })
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('Rate Limiting', () => {
    test('Multiple requests should respect rate limits', async () => {
      // Make multiple requests to test rate limiting
      const promises = Array(10).fill().map(() =>
        request(app).get('/health')
      );

      const responses = await Promise.all(promises);

      // Most should succeed, but rate limiting might kick in
      const successCount = responses.filter(r => r.status === 200).length;
      expect(successCount).toBeGreaterThan(0);
    });
  });

  // Cleanup
  afterAll(async () => {
    // Clean up test data if needed
    console.log('Test suite completed');
  });
});

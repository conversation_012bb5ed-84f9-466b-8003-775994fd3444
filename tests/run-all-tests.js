#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 HLenergy API - Comprehensive Test Suite Runner');
console.log('================================================\n');

// Test configuration
const testSuites = [
  {
    name: 'Endpoint Availability Tests',
    file: 'tests/integration/endpoint-availability.test.js',
    description: 'Tests that all endpoints from the landing page are accessible'
  },
  {
    name: 'Comprehensive Endpoint Tests',
    file: 'tests/integration/all-endpoints.test.js',
    description: 'Tests functionality of all API endpoints with proper data'
  },
  {
    name: 'Performance Tests',
    file: 'tests/integration/endpoint-performance.test.js',
    description: 'Tests response times and performance of critical endpoints'
  },
  {
    name: 'Authentication Tests',
    file: 'tests/integration/auth.test.js',
    description: 'Tests authentication and authorization functionality'
  },
  {
    name: 'Contact Tests',
    file: 'tests/integration/contact.test.js',
    description: 'Tests contact form submission and management'
  }
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function runTest(testFile) {
  return new Promise((resolve) => {
    console.log(colorize(`\n🔄 Running: ${testFile}`, 'cyan'));
    
    const startTime = Date.now();
    const jest = spawn('npx', ['jest', testFile, '--verbose'], {
      stdio: 'pipe',
      cwd: process.cwd()
    });

    let output = '';
    let errorOutput = '';

    jest.stdout.on('data', (data) => {
      output += data.toString();
    });

    jest.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    jest.on('close', (code) => {
      const duration = Date.now() - startTime;
      const result = {
        file: testFile,
        success: code === 0,
        duration,
        output,
        errorOutput
      };

      if (code === 0) {
        console.log(colorize(`✅ PASSED: ${testFile} (${duration}ms)`, 'green'));
      } else {
        console.log(colorize(`❌ FAILED: ${testFile} (${duration}ms)`, 'red'));
        if (errorOutput) {
          console.log(colorize('Error Output:', 'red'));
          console.log(errorOutput);
        }
      }

      resolve(result);
    });
  });
}

async function runAllTests() {
  console.log(colorize('Starting comprehensive test suite...', 'bright'));
  console.log(`Testing ${testSuites.length} test suites\n`);

  const results = [];
  const startTime = Date.now();

  // Check if test files exist
  for (const suite of testSuites) {
    if (!fs.existsSync(suite.file)) {
      console.log(colorize(`⚠️  Warning: ${suite.file} not found, skipping...`, 'yellow'));
      continue;
    }

    console.log(colorize(`📋 ${suite.name}`, 'blue'));
    console.log(`   ${suite.description}`);
    
    const result = await runTest(suite.file);
    results.push({
      ...suite,
      ...result
    });
  }

  const totalDuration = Date.now() - startTime;

  // Generate summary report
  console.log(colorize('\n📊 TEST SUMMARY REPORT', 'bright'));
  console.log('='.repeat(50));

  const passed = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  const total = results.length;

  console.log(`Total Tests: ${total}`);
  console.log(colorize(`Passed: ${passed}`, 'green'));
  console.log(colorize(`Failed: ${failed}`, failed > 0 ? 'red' : 'green'));
  console.log(`Total Duration: ${totalDuration}ms`);

  console.log(colorize('\n📋 Detailed Results:', 'bright'));
  results.forEach((result, index) => {
    const status = result.success ? colorize('✅ PASS', 'green') : colorize('❌ FAIL', 'red');
    console.log(`${index + 1}. ${status} ${result.name} (${result.duration}ms)`);
  });

  // Generate coverage report if available
  console.log(colorize('\n📈 Generating Coverage Report...', 'bright'));
  try {
    const coverage = spawn('npx', ['jest', '--coverage', '--silent'], {
      stdio: 'pipe',
      cwd: process.cwd()
    });

    coverage.on('close', (code) => {
      if (code === 0) {
        console.log(colorize('✅ Coverage report generated in ./coverage/', 'green'));
      } else {
        console.log(colorize('⚠️  Coverage report generation failed', 'yellow'));
      }
    });
  } catch (error) {
    console.log(colorize('⚠️  Could not generate coverage report', 'yellow'));
  }

  // Save results to file
  const reportData = {
    timestamp: new Date().toISOString(),
    summary: {
      total,
      passed,
      failed,
      duration: totalDuration
    },
    results: results.map(r => ({
      name: r.name,
      file: r.file,
      success: r.success,
      duration: r.duration
    }))
  };

  fs.writeFileSync('test-results.json', JSON.stringify(reportData, null, 2));
  console.log(colorize('\n💾 Test results saved to test-results.json', 'cyan'));

  // Endpoint coverage report
  console.log(colorize('\n🎯 ENDPOINT COVERAGE ANALYSIS', 'bright'));
  console.log('='.repeat(50));
  
  const endpointCategories = [
    'Authentication (v1)', 'Contact (v1)', 'Profile (v1)', 'Admin (v1)',
    'Email (v1)', 'Email Queue (v1)', 'Analytics (v1)', 'Socket.io (v1)',
    'System', 'Dependencies', 'Database', 'Logs', 'Security'
  ];

  console.log('Endpoint categories tested:');
  endpointCategories.forEach((category, index) => {
    console.log(`${index + 1}. ${colorize(category, 'cyan')}`);
  });

  console.log(colorize(`\n🏆 Total Endpoint Categories: ${endpointCategories.length}`, 'green'));

  // Final status
  if (failed === 0) {
    console.log(colorize('\n🎉 ALL TESTS PASSED! 🎉', 'green'));
    console.log(colorize('The HLenergy API is working correctly!', 'green'));
    process.exit(0);
  } else {
    console.log(colorize(`\n⚠️  ${failed} TEST SUITE(S) FAILED`, 'red'));
    console.log(colorize('Please check the error output above and fix the issues.', 'red'));
    process.exit(1);
  }
}

// Handle process interruption
process.on('SIGINT', () => {
  console.log(colorize('\n\n⚠️  Test execution interrupted by user', 'yellow'));
  process.exit(1);
});

// Run the tests
runAllTests().catch((error) => {
  console.error(colorize('\n❌ Test runner error:', 'red'), error);
  process.exit(1);
});

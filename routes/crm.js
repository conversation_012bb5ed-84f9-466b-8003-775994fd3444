const express = require('express');
const router = express.Router();
const db = require('../shared/config/database');
const { authenticateToken, requireAdmin } = require('../shared/middleware/auth');
const rateLimit = require('express-rate-limit');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;

// Enhanced rate limiting for CRM endpoints
const crmRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Higher limit for CRM operations
  message: {
    error: 'Too many CRM requests from this IP, please try again later.',
    type: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Specific rate limits for different operations
const readOperationsLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 200, // 200 requests per minute for read operations
  skip: (req) => req.method !== 'GET'
});

const writeOperationsLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 50, // 50 requests per minute for write operations
  skip: (req) => req.method === 'GET'
});

// Apply rate limiting
router.use(crmRateLimit);
router.use(readOperationsLimit);
router.use(writeOperationsLimit);

// Apply authentication to all CRM routes
router.use(authenticateToken);

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads/crm');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|xls|xlsx|txt/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Invalid file type'));
    }
  }
});

// Helper function for pagination
const getPagination = (page, limit) => {
  const pageNum = parseInt(page) || 1;
  const limitNum = parseInt(limit) || 10;
  const offset = (pageNum - 1) * limitNum;
  return { limit: limitNum, offset, page: pageNum };
};

// Helper function for building WHERE clauses
const buildWhereClause = (filters, baseWhere = '1=1') => {
  let whereClause = baseWhere;
  const values = [];
  let paramCount = 0;

  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      paramCount++;
      if (key === 'search') {
        whereClause += ` AND (name ILIKE $${paramCount} OR email ILIKE $${paramCount})`;
        values.push(`%${value}%`);
      } else {
        whereClause += ` AND ${key} = $${paramCount}`;
        values.push(value);
      }
    }
  });

  return { whereClause, values };
};

/**
 * @swagger
 * components:
 *   schemas:
 *     Customer:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         email:
 *           type: string
 *         phone:
 *           type: string
 *         industry:
 *           type: string
 *         priority:
 *           type: string
 *           enum: [low, medium, high]
 *         status:
 *           type: string
 *           enum: [prospect, active, inactive, converted]
 *         lead_source:
 *           type: string
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api/v1/crm/customers:
 *   get:
 *     summary: Get customers with pagination and filtering
 *     tags: [CRM - Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *       - in: query
 *         name: leadSource
 *         schema:
 *           type: string
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: List of customers
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Customer'
 *                     pagination:
 *                       type: object
 */
router.get('/customers', async (req, res) => {
  try {
    const { page, limit, status, priority, leadSource, search } = req.query;
    const { limit: limitNum, offset, page: pageNum } = getPagination(page, limit);

    // Build WHERE clause with filters
    const filters = {
      status,
      priority,
      lead_source: leadSource,
      search
    };

    const { whereClause, values } = buildWhereClause(filters);

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM customers WHERE ${whereClause}`;
    const countResult = await db.query(countQuery, values);
    const total = parseInt(countResult.rows[0].count);

    // Get customers with pagination
    const customersQuery = `
      SELECT 
        c.*,
        u.name as assigned_to_name
      FROM customers c
      LEFT JOIN users u ON c.assigned_to = u.id
      WHERE ${whereClause}
      ORDER BY c.created_at DESC
      LIMIT $${values.length + 1} OFFSET $${values.length + 2}
    `;

    const customersResult = await db.query(customersQuery, [...values, limitNum, offset]);

    const pagination = {
      page: pageNum,
      limit: limitNum,
      total,
      totalPages: Math.ceil(total / limitNum)
    };

    res.json({
      success: true,
      data: {
        items: customersResult.rows,
        pagination
      }
    });

  } catch (error) {
    console.error('Error fetching customers:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch customers',
        type: 'DATABASE_ERROR'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/crm/customers:
 *   post:
 *     summary: Create a new customer
 *     tags: [CRM - Customers]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *             properties:
 *               name:
 *                 type: string
 *               email:
 *                 type: string
 *               phone:
 *                 type: string
 *               industry:
 *                 type: string
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high]
 *               status:
 *                 type: string
 *                 enum: [prospect, active, inactive, converted]
 *               lead_source:
 *                 type: string
 *     responses:
 *       201:
 *         description: Customer created successfully
 *       400:
 *         description: Invalid input data
 *       409:
 *         description: Customer with email already exists
 */
router.post('/customers', async (req, res) => {
  try {
    const {
      name,
      email,
      phone,
      industry,
      priority = 'medium',
      status = 'prospect',
      lead_source,
      company_size,
      annual_revenue,
      website,
      address,
      city,
      country = 'Portugal',
      postal_code,
      notes
    } = req.body;

    // Validate required fields
    if (!name || !email) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Name and email are required',
          type: 'VALIDATION_ERROR'
        }
      });
    }

    // Check if customer with email already exists
    const existingCustomer = await db.query(
      'SELECT id FROM customers WHERE email = $1',
      [email]
    );

    if (existingCustomer.rows.length > 0) {
      return res.status(409).json({
        success: false,
        error: {
          message: 'Customer with this email already exists',
          type: 'DUPLICATE_EMAIL'
        }
      });
    }

    // Insert new customer
    const insertQuery = `
      INSERT INTO customers (
        name, email, phone, industry, priority, status, lead_source,
        company_size, annual_revenue, website, address, city, country,
        postal_code, notes, assigned_to
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
      RETURNING *
    `;

    const values = [
      name, email, phone, industry, priority, status, lead_source,
      company_size, annual_revenue, website, address, city, country,
      postal_code, notes, req.user.id
    ];

    const result = await db.query(insertQuery, values);
    const customer = result.rows[0];

    res.status(201).json({
      success: true,
      data: customer,
      message: 'Customer created successfully'
    });

  } catch (error) {
    console.error('Error creating customer:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to create customer',
        type: 'DATABASE_ERROR'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/crm/customers/{id}:
 *   get:
 *     summary: Get customer by ID
 *     tags: [CRM - Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Customer details
 *       404:
 *         description: Customer not found
 */
router.get('/customers/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT
        c.*,
        u.name as assigned_to_name,
        COUNT(p.id) as project_count,
        COUNT(comm.id) as communication_count
      FROM customers c
      LEFT JOIN users u ON c.assigned_to = u.id
      LEFT JOIN projects p ON c.id = p.customer_id
      LEFT JOIN communications comm ON c.id = comm.customer_id
      WHERE c.id = $1
      GROUP BY c.id, u.name
    `;

    const result = await db.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Customer not found',
          type: 'NOT_FOUND'
        }
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Error fetching customer:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch customer',
        type: 'DATABASE_ERROR'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/crm/customers/{id}:
 *   put:
 *     summary: Update customer
 *     tags: [CRM - Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               email:
 *                 type: string
 *               phone:
 *                 type: string
 *               industry:
 *                 type: string
 *               priority:
 *                 type: string
 *               status:
 *                 type: string
 *     responses:
 *       200:
 *         description: Customer updated successfully
 *       404:
 *         description: Customer not found
 */
router.put('/customers/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateFields = req.body;

    // Check if customer exists
    const existingCustomer = await db.query('SELECT id FROM customers WHERE id = $1', [id]);
    if (existingCustomer.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Customer not found',
          type: 'NOT_FOUND'
        }
      });
    }

    // Build dynamic update query
    const setClause = [];
    const values = [];
    let paramCount = 0;

    Object.entries(updateFields).forEach(([key, value]) => {
      if (value !== undefined) {
        paramCount++;
        setClause.push(`${key} = $${paramCount}`);
        values.push(value);
      }
    });

    if (setClause.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'No fields to update',
          type: 'VALIDATION_ERROR'
        }
      });
    }

    values.push(id);
    const updateQuery = `
      UPDATE customers
      SET ${setClause.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramCount + 1}
      RETURNING *
    `;

    const result = await db.query(updateQuery, values);

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Customer updated successfully'
    });

  } catch (error) {
    console.error('Error updating customer:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to update customer',
        type: 'DATABASE_ERROR'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/crm/customers/{id}:
 *   delete:
 *     summary: Delete customer
 *     tags: [CRM - Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Customer deleted successfully
 *       404:
 *         description: Customer not found
 */
router.delete('/customers/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query('DELETE FROM customers WHERE id = $1 RETURNING id', [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Customer not found',
          type: 'NOT_FOUND'
        }
      });
    }

    res.json({
      success: true,
      message: 'Customer deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting customer:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to delete customer',
        type: 'DATABASE_ERROR'
      }
    });
  }
});

// ==================== PROJECTS ENDPOINTS ====================

/**
 * @swagger
 * /api/v1/crm/projects:
 *   get:
 *     summary: Get projects with pagination and filtering
 *     tags: [CRM - Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *       - in: query
 *         name: customerId
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: List of projects
 */
router.get('/projects', async (req, res) => {
  try {
    const { page, limit, status, priority, customerId, search } = req.query;
    const { limit: limitNum, offset, page: pageNum } = getPagination(page, limit);

    // Build WHERE clause with filters
    const filters = {
      status,
      priority,
      customer_id: customerId,
      search
    };

    const { whereClause, values } = buildWhereClause(filters);

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM projects WHERE ${whereClause}`;
    const countResult = await db.query(countQuery, values);
    const total = parseInt(countResult.rows[0].count);

    // Get projects with pagination
    const projectsQuery = `
      SELECT
        p.*,
        c.name as customer_name,
        c.email as customer_email,
        u.name as assigned_to_name
      FROM projects p
      LEFT JOIN customers c ON p.customer_id = c.id
      LEFT JOIN users u ON p.assigned_to = u.id
      WHERE ${whereClause}
      ORDER BY p.created_at DESC
      LIMIT $${values.length + 1} OFFSET $${values.length + 2}
    `;

    const projectsResult = await db.query(projectsQuery, [...values, limitNum, offset]);

    const pagination = {
      page: pageNum,
      limit: limitNum,
      total,
      totalPages: Math.ceil(total / limitNum)
    };

    res.json({
      success: true,
      data: {
        items: projectsResult.rows,
        pagination
      }
    });

  } catch (error) {
    console.error('Error fetching projects:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch projects',
        type: 'DATABASE_ERROR'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/crm/projects:
 *   post:
 *     summary: Create a new project
 *     tags: [CRM - Projects]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - customer_id
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               customer_id:
 *                 type: integer
 *               status:
 *                 type: string
 *               priority:
 *                 type: string
 *               start_date:
 *                 type: string
 *                 format: date
 *               end_date:
 *                 type: string
 *                 format: date
 *               estimated_budget:
 *                 type: number
 *     responses:
 *       201:
 *         description: Project created successfully
 */
router.post('/projects', async (req, res) => {
  try {
    const {
      name,
      description,
      customer_id,
      status = 'planning',
      priority = 'medium',
      start_date,
      end_date,
      estimated_budget,
      project_type
    } = req.body;

    // Validate required fields
    if (!name || !customer_id) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Name and customer_id are required',
          type: 'VALIDATION_ERROR'
        }
      });
    }

    // Check if customer exists
    const customerExists = await db.query('SELECT id FROM customers WHERE id = $1', [customer_id]);
    if (customerExists.rows.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Customer not found',
          type: 'INVALID_CUSTOMER'
        }
      });
    }

    // Insert new project
    const insertQuery = `
      INSERT INTO projects (
        name, description, customer_id, status, priority, start_date,
        end_date, estimated_budget, project_type, assigned_to, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `;

    const values = [
      name, description, customer_id, status, priority, start_date,
      end_date, estimated_budget, project_type, req.user.id, req.user.id
    ];

    const result = await db.query(insertQuery, values);
    const project = result.rows[0];

    res.status(201).json({
      success: true,
      data: project,
      message: 'Project created successfully'
    });

  } catch (error) {
    console.error('Error creating project:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to create project',
        type: 'DATABASE_ERROR'
      }
    });
  }
});

// ==================== PROJECTS ENDPOINTS ====================

/**
 * @swagger
 * /api/v1/crm/projects:
 *   get:
 *     summary: Get projects with pagination and filtering
 *     tags: [CRM - Projects]
 *     security:
 *       - bearerAuth: []
 */
router.get('/projects', async (req, res) => {
  try {
    const { page, limit, status, priority, customerId, search } = req.query;
    const { limit: limitNum, offset, page: pageNum } = getPagination(page, limit);

    // Build WHERE clause with filters
    let whereClause = '1=1';
    const values = [];
    let paramCount = 0;

    if (status) {
      paramCount++;
      whereClause += ` AND status = $${paramCount}`;
      values.push(status);
    }
    if (priority) {
      paramCount++;
      whereClause += ` AND priority = $${paramCount}`;
      values.push(priority);
    }
    if (customerId) {
      paramCount++;
      whereClause += ` AND customer_id = $${paramCount}`;
      values.push(customerId);
    }
    if (search) {
      paramCount++;
      whereClause += ` AND (name ILIKE $${paramCount} OR description ILIKE $${paramCount})`;
      values.push(`%${search}%`);
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM projects WHERE ${whereClause}`;
    const countResult = await db.query(countQuery, values);
    const total = parseInt(countResult.rows[0].count);

    // Get projects with pagination
    const projectsQuery = `
      SELECT
        p.*,
        c.name as customer_name,
        c.email as customer_email,
        u.name as assigned_to_name
      FROM projects p
      LEFT JOIN customers c ON p.customer_id = c.id
      LEFT JOIN users u ON p.assigned_to = u.id
      WHERE ${whereClause}
      ORDER BY p.created_at DESC
      LIMIT $${values.length + 1} OFFSET $${values.length + 2}
    `;

    const projectsResult = await db.query(projectsQuery, [...values, limitNum, offset]);

    const pagination = {
      page: pageNum,
      limit: limitNum,
      total,
      totalPages: Math.ceil(total / limitNum)
    };

    res.json({
      success: true,
      data: {
        items: projectsResult.rows,
        pagination
      }
    });

  } catch (error) {
    console.error('Error fetching projects:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch projects',
        type: 'DATABASE_ERROR'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/crm/projects:
 *   post:
 *     summary: Create a new project
 *     tags: [CRM - Projects]
 *     security:
 *       - bearerAuth: []
 */
router.post('/projects', async (req, res) => {
  try {
    const {
      name,
      description,
      customer_id,
      status = 'planning',
      priority = 'medium',
      start_date,
      end_date,
      estimated_budget,
      project_type
    } = req.body;

    // Validate required fields
    if (!name || !customer_id) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Name and customer_id are required',
          type: 'VALIDATION_ERROR'
        }
      });
    }

    // Check if customer exists
    const customerExists = await db.query('SELECT id FROM customers WHERE id = $1', [customer_id]);
    if (customerExists.rows.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Customer not found',
          type: 'INVALID_CUSTOMER'
        }
      });
    }

    // Insert new project
    const insertQuery = `
      INSERT INTO projects (
        name, description, customer_id, status, priority, start_date,
        end_date, estimated_budget, project_type, assigned_to, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `;

    const values = [
      name, description, customer_id, status, priority, start_date,
      end_date, estimated_budget, project_type, req.user.id, req.user.id
    ];

    const result = await db.query(insertQuery, values);
    const project = result.rows[0];

    res.status(201).json({
      success: true,
      data: project,
      message: 'Project created successfully'
    });

  } catch (error) {
    console.error('Error creating project:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to create project',
        type: 'DATABASE_ERROR'
      }
    });
  }
});

// ==================== DASHBOARD ENDPOINTS ====================

/**
 * @swagger
 * /api/v1/crm/dashboard:
 *   get:
 *     summary: Get CRM dashboard data
 *     tags: [CRM - Dashboard]
 *     security:
 *       - bearerAuth: []
 */
router.get('/dashboard', async (req, res) => {
  try {
    // Get dashboard statistics
    const statsQuery = `
      SELECT
        (SELECT COUNT(*) FROM customers) as total_customers,
        (SELECT COUNT(*) FROM customers WHERE status = 'active') as active_customers,
        (SELECT COUNT(*) FROM projects WHERE status = 'active') as active_projects,
        (SELECT COUNT(*) FROM projects WHERE status = 'completed') as completed_projects,
        (SELECT COUNT(*) FROM projects WHERE end_date < CURRENT_DATE AND status != 'completed') as overdue_projects,
        (SELECT COUNT(*) FROM customers WHERE priority = 'high') as high_priority_leads,
        (SELECT COUNT(*) FROM communications WHERE status = 'new') as unread_communications
    `;

    const statsResult = await db.query(statsQuery);
    const stats = statsResult.rows[0];

    // Get recent activity
    const recentActivityQuery = `
      SELECT
        'customer' as type,
        'Customer created: ' || name as description,
        created_at as timestamp,
        'System' as user
      FROM customers
      WHERE created_at > CURRENT_DATE - INTERVAL '7 days'

      UNION ALL

      SELECT
        'project' as type,
        'Project updated: ' || name as description,
        updated_at as timestamp,
        'System' as user
      FROM projects
      WHERE updated_at > CURRENT_DATE - INTERVAL '7 days'

      UNION ALL

      SELECT
        'communication' as type,
        'New communication: ' || COALESCE(subject, 'No subject') as description,
        created_at as timestamp,
        'System' as user
      FROM communications
      WHERE created_at > CURRENT_DATE - INTERVAL '7 days'

      ORDER BY timestamp DESC
      LIMIT 10
    `;

    const activityResult = await db.query(recentActivityQuery);

    // Get lead sources
    const leadSourcesQuery = `
      SELECT
        COALESCE(lead_source, 'Unknown') as source,
        COUNT(*) as count,
        ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM customers)), 1) as percentage
      FROM customers
      GROUP BY lead_source
      ORDER BY count DESC
    `;

    const leadSourcesResult = await db.query(leadSourcesQuery);

    const dashboardData = {
      stats: {
        totalCustomers: parseInt(stats.total_customers),
        activeCustomers: parseInt(stats.active_customers),
        activeProjects: parseInt(stats.active_projects),
        completedProjects: parseInt(stats.completed_projects),
        overdueProjects: parseInt(stats.overdue_projects),
        highPriorityLeads: parseInt(stats.high_priority_leads),
        unreadCommunications: parseInt(stats.unread_communications)
      },
      recentActivity: activityResult.rows,
      leadSources: leadSourcesResult.rows
    };

    res.json({
      success: true,
      data: dashboardData
    });

  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch dashboard data',
        type: 'DATABASE_ERROR'
      }
    });
  }
});

// ==================== COMMUNICATIONS ENDPOINTS ====================

/**
 * @swagger
 * /api/v1/crm/communications:
 *   get:
 *     summary: Get communications with pagination and filtering
 *     tags: [CRM - Communications]
 *     security:
 *       - bearerAuth: []
 */
router.get('/communications', async (req, res) => {
  try {
    const { page, limit, status, type, customerId, projectId } = req.query;
    const { limit: limitNum, offset, page: pageNum } = getPagination(page, limit);

    // Build WHERE clause with filters
    let whereClause = '1=1';
    const values = [];
    let paramCount = 0;

    if (status) {
      paramCount++;
      whereClause += ` AND status = $${paramCount}`;
      values.push(status);
    }
    if (type) {
      paramCount++;
      whereClause += ` AND type = $${paramCount}`;
      values.push(type);
    }
    if (customerId) {
      paramCount++;
      whereClause += ` AND customer_id = $${paramCount}`;
      values.push(customerId);
    }
    if (projectId) {
      paramCount++;
      whereClause += ` AND project_id = $${paramCount}`;
      values.push(projectId);
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM communications WHERE ${whereClause}`;
    const countResult = await db.query(countQuery, values);
    const total = parseInt(countResult.rows[0].count);

    // Get communications with pagination
    const communicationsQuery = `
      SELECT
        comm.*,
        c.name as customer_name,
        p.name as project_name,
        u.name as created_by_name
      FROM communications comm
      LEFT JOIN customers c ON comm.customer_id = c.id
      LEFT JOIN projects p ON comm.project_id = p.id
      LEFT JOIN users u ON comm.created_by = u.id
      WHERE ${whereClause}
      ORDER BY comm.created_at DESC
      LIMIT $${values.length + 1} OFFSET $${values.length + 2}
    `;

    const communicationsResult = await db.query(communicationsQuery, [...values, limitNum, offset]);

    const pagination = {
      page: pageNum,
      limit: limitNum,
      total,
      totalPages: Math.ceil(total / limitNum)
    };

    res.json({
      success: true,
      data: {
        items: communicationsResult.rows,
        pagination
      }
    });

  } catch (error) {
    console.error('Error fetching communications:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch communications',
        type: 'DATABASE_ERROR'
      }
    });
  }
});

module.exports = router;

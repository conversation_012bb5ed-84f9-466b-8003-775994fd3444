#!/usr/bin/env node

/**
 * Run Push Subscriptions Migration
 * 
 * This script runs the push subscriptions migration to create the table
 * if it doesn't exist.
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });

const { Sequelize } = require('sequelize');

// Database configuration
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'mysql',
    logging: console.log,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  }
);

async function runMigration() {
  try {
    console.log('🔌 Connecting to database...');
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Import the migration
    const migration = require('./shared/migrations/20241210000002-create-push-subscriptions-clean.js');
    
    console.log('🚀 Running push subscriptions migration...');
    await migration.up(sequelize.getQueryInterface(), Sequelize);
    
    console.log('✅ Push subscriptions migration completed successfully!');
    
    // Test the table
    console.log('🧪 Testing table creation...');
    const [results] = await sequelize.query('DESCRIBE push_subscriptions');
    console.log('📋 Table structure:');
    console.table(results);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
    console.log('🔌 Database connection closed');
  }
}

// Run the migration
runMigration();

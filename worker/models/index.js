/**
 * Worker Models
 * Standalone models for the worker process
 */

const { sequelize } = require('../config/database');
const { DataTypes } = require('sequelize');

// Import model definitions from shared directory but use worker's sequelize instance
const BackgroundJobModel = require('../../shared/models/BackgroundJob');
const SystemStatsModel = require('../../shared/models/SystemStats');
const HealthCheckModel = require('../../shared/models/HealthCheck');
const EmailQueueModel = require('../../shared/models/EmailQueue');

// Create models with worker's sequelize instance
const BackgroundJob = sequelize.define('BackgroundJob', BackgroundJobModel.rawAttributes, {
  tableName: 'background_jobs',
  timestamps: false,
});

const SystemStats = sequelize.define('SystemStats', SystemStatsModel.rawAttributes, {
  tableName: 'system_stats',
  timestamps: false,
});

const HealthCheck = sequelize.define('HealthCheck', HealthCheckModel.rawAttributes, {
  tableName: 'health_checks',
  timestamps: false,
});

const EmailQueue = sequelize.define('EmailQueue', EmailQueueModel.rawAttributes, {
  tableName: 'email_queue',
  timestamps: false,
});

// Add class methods manually since they don't transfer with rawAttributes
// BackgroundJob methods
BackgroundJob.enqueue = BackgroundJobModel.enqueue.bind(BackgroundJob);
BackgroundJob.dequeue = BackgroundJobModel.dequeue.bind(BackgroundJob);
BackgroundJob.markAsProcessing = BackgroundJobModel.markAsProcessing.bind(BackgroundJob);
BackgroundJob.markAsCompleted = BackgroundJobModel.markAsCompleted.bind(BackgroundJob);
BackgroundJob.markAsFailed = BackgroundJobModel.markAsFailed.bind(BackgroundJob);
BackgroundJob.getStats = BackgroundJobModel.getStats.bind(BackgroundJob);
BackgroundJob.cleanupOldJobs = BackgroundJobModel.cleanupOldJobs.bind(BackgroundJob);

// SystemStats methods
SystemStats.getCachedStats = SystemStatsModel.getCachedStats.bind(SystemStats);
SystemStats.setCachedStats = SystemStatsModel.setCachedStats.bind(SystemStats);
SystemStats.calculateUserStats = SystemStatsModel.calculateUserStats.bind(SystemStats);
SystemStats.calculateLogStats = SystemStatsModel.calculateLogStats.bind(SystemStats);
SystemStats.calculateConnectionStats = SystemStatsModel.calculateConnectionStats.bind(SystemStats);
SystemStats.cleanupExpiredStats = SystemStatsModel.cleanupExpiredStats.bind(SystemStats);

// HealthCheck methods
HealthCheck.recordCheck = HealthCheckModel.recordCheck.bind(HealthCheck);
HealthCheck.getLatestChecks = HealthCheckModel.getLatestChecks.bind(HealthCheck);
HealthCheck.getOverallHealth = HealthCheckModel.getOverallHealth.bind(HealthCheck);
HealthCheck.checkDatabaseHealth = HealthCheckModel.checkDatabaseHealth.bind(HealthCheck);
HealthCheck.checkMemoryUsage = HealthCheckModel.checkMemoryUsage.bind(HealthCheck);
HealthCheck.checkSocketServer = HealthCheckModel.checkSocketServer.bind(HealthCheck);
HealthCheck.cleanupOldChecks = HealthCheckModel.cleanupOldChecks.bind(HealthCheck);

// Create basic models for stats calculations (read-only)
const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  role: {
    type: DataTypes.ENUM('user', 'admin'),
    defaultValue: 'user',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
}, {
  tableName: 'users',
  timestamps: false,
});

const UserSession = sequelize.define('UserSession', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  last_activity: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
}, {
  tableName: 'user_sessions',
  timestamps: false,
});

const Log = sequelize.define('Log', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  level: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  message: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
  },
  type: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
}, {
  tableName: 'logs',
  timestamps: false,
});

// Add cleanup methods to UserSession
UserSession.cleanupExpiredSessions = async function() {
  const { Op } = require('sequelize');
  const result = await this.destroy({
    where: {
      [Op.or]: [
        { expires_at: { [Op.lt]: new Date() } },
        { is_active: false }
      ]
    }
  });
  return result;
};

// Initialize database connection
async function initializeDatabase() {
  try {
    await sequelize.authenticate();
    console.log('✅ Worker database connection established');
    return true;
  } catch (error) {
    console.error('❌ Worker database connection failed:', error);
    throw error;
  }
}

module.exports = {
  sequelize,
  BackgroundJob,
  SystemStats,
  HealthCheck,
  EmailQueue,
  User,
  UserSession,
  Log,
  initializeDatabase,
};

/**
 * Worker Database Configuration
 * Uses the same database as the main application but with worker-specific settings
 */

require('dotenv').config({ path: require('path').join(__dirname, '../../.env') });

const { Sequelize } = require('sequelize');

// Database configuration
const config = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  database: process.env.DB_NAME || 'hlenergy',
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  dialect: 'mysql',
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000
  },
  define: {
    timestamps: false,
    freezeTableName: true
  }
};

// Create Sequelize instance
const sequelize = new Sequelize(config.database, config.username, config.password, config);

// Test connection
async function testConnection() {
  try {
    await sequelize.authenticate();
    console.log('✅ Worker database connection established successfully');
    return true;
  } catch (error) {
    console.error('❌ Worker unable to connect to database:', error.message);
    return false;
  }
}

module.exports = {
  sequelize,
  testConnection,
  config
};

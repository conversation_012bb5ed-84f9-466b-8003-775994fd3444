const { BackgroundJob } = require('./models');
const { Logger } = require('./utils/logger');
const JobHandlers = require('./handlers');

/**
 * Main Worker Class
 * Processes background jobs from the queue
 */
class Worker {
  constructor(config = {}) {
    this.config = {
      maxConcurrentJobs: config.maxConcurrentJobs || 5,
      pollInterval: config.pollInterval || 5000, // 5 seconds
      maxRetries: config.maxRetries || 3,
      retryDelay: config.retryDelay || 30000, // 30 seconds
      ...config,
    };

    this.isRunning = false;
    this.activeJobs = new Map();
    this.pollTimer = null;
    this.stats = {
      processed: 0,
      failed: 0,
      active: 0,
      startedAt: null,
    };

    // Bind methods
    this.processJobs = this.processJobs.bind(this);
    this.handleJob = this.handleJob.bind(this);
  }

  /**
   * Start the worker process
   */
  async start() {
    if (this.isRunning) {
      throw new Error('Worker is already running');
    }

    console.log('⚙️ Starting worker with config:', {
      maxConcurrentJobs: this.config.maxConcurrentJobs,
      pollInterval: this.config.pollInterval,
    });

    this.isRunning = true;
    this.stats.startedAt = new Date();

    // Start job polling
    this.scheduleNextPoll();

    Logger.info('Worker started successfully', this.config);
  }

  /**
   * Stop the worker process gracefully
   */
  async stop() {
    if (!this.isRunning) {
      return;
    }

    console.log('🛑 Stopping worker...');
    this.isRunning = false;

    // Clear polling timer
    if (this.pollTimer) {
      clearTimeout(this.pollTimer);
      this.pollTimer = null;
    }

    // Wait for active jobs to complete
    if (this.activeJobs.size > 0) {
      console.log(`⏳ Waiting for ${this.activeJobs.size} active jobs to complete...`);
      
      const timeout = 30000; // 30 seconds timeout
      const startTime = Date.now();
      
      while (this.activeJobs.size > 0 && (Date.now() - startTime) < timeout) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      if (this.activeJobs.size > 0) {
        console.log(`⚠️ Force stopping with ${this.activeJobs.size} jobs still active`);
        Logger.warn('Worker stopped with active jobs', { activeJobs: this.activeJobs.size });
      }
    }

    console.log('✅ Worker stopped successfully');
    Logger.info('Worker stopped successfully', this.getStats());
  }

  /**
   * Schedule the next job polling cycle
   */
  scheduleNextPoll() {
    if (!this.isRunning) return;

    this.pollTimer = setTimeout(async () => {
      try {
        await this.processJobs();
      } catch (error) {
        console.error('❌ Error in job processing cycle:', error);
        Logger.error('Error in job processing cycle', { error: error.message });
      } finally {
        this.scheduleNextPoll();
      }
    }, this.config.pollInterval);
  }

  /**
   * Process available jobs from the queue
   */
  async processJobs() {
    if (!this.isRunning) return;

    // Check if we can take more jobs
    const availableSlots = this.config.maxConcurrentJobs - this.activeJobs.size;
    if (availableSlots <= 0) {
      return; // All slots occupied
    }

    try {
      // Fetch available jobs
      const jobs = await BackgroundJob.dequeue(availableSlots);
      
      if (jobs.length === 0) {
        return; // No jobs available
      }

      console.log(`📋 Found ${jobs.length} job(s) to process`);

      // Process each job concurrently
      for (const job of jobs) {
        this.handleJob(job);
      }

    } catch (error) {
      console.error('❌ Error fetching jobs from queue:', error);
      Logger.error('Error fetching jobs from queue', { error: error.message });
    }
  }

  /**
   * Handle a single job
   */
  async handleJob(job) {
    const jobId = job.id;
    
    try {
      // Mark job as processing
      await BackgroundJob.markAsProcessing(jobId);
      this.activeJobs.set(jobId, {
        job,
        startedAt: new Date(),
      });
      this.stats.active = this.activeJobs.size;

      console.log(`🔄 Processing job ${jobId}: ${job.job_type}`);
      Logger.info('Processing job', {
        jobId,
        jobType: job.job_type,
        attempts: job.attempts + 1,
      });

      // Get job handler
      const handler = JobHandlers.getHandler(job.job_type);
      if (!handler) {
        throw new Error(`No handler found for job type: ${job.job_type}`);
      }

      // Execute job
      const startTime = Date.now();
      const result = await handler.execute(job.job_data, job);
      const duration = Date.now() - startTime;

      // Mark job as completed
      await BackgroundJob.markAsCompleted(jobId);
      
      console.log(`✅ Job ${jobId} completed successfully in ${duration}ms`);
      Logger.info('Job completed successfully', {
        jobId,
        jobType: job.job_type,
        duration,
        result: typeof result === 'object' ? JSON.stringify(result) : result,
      });

      this.stats.processed++;

    } catch (error) {
      console.error(`❌ Job ${jobId} failed:`, error);
      Logger.error('Job failed', {
        jobId,
        jobType: job.job_type,
        error: error.message,
        attempts: job.attempts + 1,
      });

      // Mark job as failed (with retry logic)
      await BackgroundJob.markAsFailed(jobId, error.message);
      this.stats.failed++;

    } finally {
      // Remove from active jobs
      this.activeJobs.delete(jobId);
      this.stats.active = this.activeJobs.size;
    }
  }

  /**
   * Get worker statistics
   */
  getStats() {
    return {
      ...this.stats,
      uptime: this.stats.startedAt ? Date.now() - this.stats.startedAt.getTime() : 0,
      isRunning: this.isRunning,
      config: this.config,
    };
  }

  /**
   * Get active job information
   */
  getActiveJobs() {
    const activeJobs = [];
    for (const [jobId, jobInfo] of this.activeJobs.entries()) {
      activeJobs.push({
        id: jobId,
        type: jobInfo.job.job_type,
        startedAt: jobInfo.startedAt,
        duration: Date.now() - jobInfo.startedAt.getTime(),
      });
    }
    return activeJobs;
  }

  /**
   * Health check for the worker
   */
  async healthCheck() {
    return {
      status: this.isRunning ? 'healthy' : 'stopped',
      stats: this.getStats(),
      activeJobs: this.getActiveJobs(),
      lastPoll: this.pollTimer ? 'scheduled' : 'not_scheduled',
    };
  }
}

module.exports = { Worker };

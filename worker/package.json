{"name": "hlenergy-worker", "version": "1.0.0", "description": "HLenergy Background Worker Process", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "echo 'Worker build completed'", "test": "echo 'Worker tests not implemented yet'"}, "keywords": ["background-jobs", "worker", "scheduler", "health-monitoring", "statistics"], "author": "HLenergy Team", "license": "MIT", "dependencies": {"node-cron": "^3.0.3", "dotenv": "^16.3.1", "sequelize": "^6.37.7", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}
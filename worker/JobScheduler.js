const cron = require('node-cron');
const { BackgroundJob } = require('./models');
const { Logger } = require('./utils/logger');

/**
 * Job Scheduler Class
 * Manages recurring jobs using cron expressions
 */
class JobScheduler {
  constructor() {
    this.scheduledJobs = new Map();
    this.isRunning = false;
  }

  /**
   * Start the job scheduler
   */
  async start() {
    if (this.isRunning) {
      throw new Error('Job scheduler is already running');
    }

    console.log('📅 Starting job scheduler...');
    this.isRunning = true;

    // Schedule recurring jobs
    await this.scheduleRecurringJobs();

    Logger.info('Job scheduler started successfully');
  }

  /**
   * Stop the job scheduler
   */
  async stop() {
    if (!this.isRunning) {
      return;
    }

    console.log('🛑 Stopping job scheduler...');
    this.isRunning = false;

    // Stop all scheduled jobs
    for (const [name, task] of this.scheduledJobs.entries()) {
      task.stop();
      console.log(`📅 Stopped scheduled job: ${name}`);
    }

    this.scheduledJobs.clear();
    console.log('✅ Job scheduler stopped successfully');
    Logger.info('Job scheduler stopped successfully');
  }

  /**
   * Schedule all recurring jobs
   */
  async scheduleRecurringJobs() {
    const jobs = [
      // Stats calculation jobs
      {
        name: 'calculate-stats',
        schedule: '*/15 * * * *', // Every 15 minutes
        jobType: 'calculate-daily-stats',
        jobData: {},
        priority: 5,
      },
      {
        name: 'calculate-weekly-stats',
        schedule: '0 1 * * 1', // Every Monday at 1 AM
        jobType: 'calculate-weekly-stats',
        jobData: {},
        priority: 3,
      },
      {
        name: 'calculate-monthly-stats',
        schedule: '0 2 1 * *', // First day of month at 2 AM
        jobType: 'calculate-monthly-stats',
        jobData: {},
        priority: 3,
      },

      // Health check jobs
      {
        name: 'health-checks',
        schedule: '*/5 * * * *', // Every 5 minutes
        jobType: 'check-database-health',
        jobData: {},
        priority: 7,
      },
      {
        name: 'comprehensive-health-check',
        schedule: '*/10 * * * *', // Every 10 minutes
        jobType: 'comprehensive-health-check',
        jobData: {},
        priority: 8,
      },
      {
        name: 'memory-check',
        schedule: '*/10 * * * *', // Every 10 minutes
        jobType: 'check-memory-usage',
        jobData: {},
        priority: 6,
      },

      // Cleanup jobs
      {
        name: 'cleanup-old-logs',
        schedule: '0 3 * * *', // Daily at 3 AM
        jobType: 'cleanup-old-logs',
        jobData: { daysToKeep: 30 },
        priority: 2,
      },
      {
        name: 'cleanup-expired-sessions',
        schedule: '0 */6 * * *', // Every 6 hours
        jobType: 'cleanup-expired-sessions',
        jobData: {},
        priority: 4,
      },
      {
        name: 'cleanup-old-jobs',
        schedule: '0 4 * * *', // Daily at 4 AM
        jobType: 'cleanup-old-jobs',
        jobData: { daysToKeep: 7 },
        priority: 2,
      },
      {
        name: 'cleanup-old-stats',
        schedule: '0 5 * * *', // Daily at 5 AM
        jobType: 'cleanup-old-stats',
        jobData: {},
        priority: 2,
      },

      // Email digest jobs
      {
        name: 'daily-digest',
        schedule: '0 8 * * *', // Daily at 8 AM
        jobType: 'send-daily-digest',
        jobData: {},
        priority: 4,
      },
      {
        name: 'weekly-report',
        schedule: '0 9 * * 1', // Every Monday at 9 AM
        jobType: 'send-weekly-report',
        jobData: {},
        priority: 3,
      },
    ];

    for (const jobConfig of jobs) {
      await this.scheduleJob(jobConfig);
    }

    console.log(`📅 Scheduled ${jobs.length} recurring jobs`);
  }

  /**
   * Schedule a single job
   */
  async scheduleJob(config) {
    const { name, schedule, jobType, jobData, priority } = config;

    try {
      // Validate cron expression
      if (!cron.validate(schedule)) {
        throw new Error(`Invalid cron expression: ${schedule}`);
      }

      // Create scheduled task
      const task = cron.schedule(schedule, async () => {
        try {
          console.log(`📅 Executing scheduled job: ${name} (${jobType})`);
          
          // Enqueue the job
          await BackgroundJob.enqueue(jobType, jobData, { priority });
          
          Logger.info('Scheduled job enqueued', {
            name,
            jobType,
            schedule,
            priority,
          });

        } catch (error) {
          console.error(`❌ Failed to enqueue scheduled job ${name}:`, error);
          Logger.error('Failed to enqueue scheduled job', {
            name,
            jobType,
            error: error.message,
          });
        }
      }, {
        scheduled: false, // Don't start immediately
        timezone: process.env.TZ || 'UTC',
      });

      // Store the task
      this.scheduledJobs.set(name, task);

      // Start the task
      task.start();

      console.log(`📅 Scheduled job: ${name} (${schedule})`);

    } catch (error) {
      console.error(`❌ Failed to schedule job ${name}:`, error);
      Logger.error('Failed to schedule job', {
        name,
        schedule,
        error: error.message,
      });
    }
  }

  /**
   * Add a new scheduled job at runtime
   */
  async addScheduledJob(name, schedule, jobType, jobData = {}, priority = 0) {
    if (this.scheduledJobs.has(name)) {
      throw new Error(`Scheduled job with name '${name}' already exists`);
    }

    await this.scheduleJob({
      name,
      schedule,
      jobType,
      jobData,
      priority,
    });
  }

  /**
   * Remove a scheduled job
   */
  removeScheduledJob(name) {
    const task = this.scheduledJobs.get(name);
    if (task) {
      task.stop();
      this.scheduledJobs.delete(name);
      console.log(`📅 Removed scheduled job: ${name}`);
      Logger.info('Scheduled job removed', { name });
      return true;
    }
    return false;
  }

  /**
   * Get information about all scheduled jobs
   */
  getScheduledJobs() {
    const jobs = [];
    for (const [name, task] of this.scheduledJobs.entries()) {
      jobs.push({
        name,
        running: task.running,
        // Note: node-cron doesn't expose schedule info easily
        // You might want to store this separately if needed
      });
    }
    return jobs;
  }

  /**
   * Health check for the scheduler
   */
  async healthCheck() {
    return {
      status: this.isRunning ? 'healthy' : 'stopped',
      scheduledJobsCount: this.scheduledJobs.size,
      runningJobs: Array.from(this.scheduledJobs.entries())
        .filter(([name, task]) => task.running)
        .map(([name]) => name),
    };
  }
}

module.exports = { JobScheduler };

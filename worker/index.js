#!/usr/bin/env node

/**
 * HLenergy Standalone Background Worker
 * 
 * This worker process runs independently from the main server and handles:
 * - Background job processing
 * - System statistics calculation
 * - Health monitoring
 * - Database cleanup tasks
 * - Scheduled maintenance
 */

const path = require('path');
const { Worker } = require('./Worker');
const { JobScheduler } = require('./JobScheduler');
const { Logger } = require('./utils/logger');

// Set up environment
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Global error handlers
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  Logger.error('Uncaught exception in worker process', { error: error.message, stack: error.stack });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  Logger.error('Unhandled rejection in worker process', { reason, promise });
  process.exit(1);
});

// Graceful shutdown handling
let worker = null;
let scheduler = null;
let isShuttingDown = false;

const gracefulShutdown = async (signal) => {
  if (isShuttingDown) return;
  isShuttingDown = true;

  console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);
  Logger.info(`Worker received ${signal}, starting graceful shutdown`);

  try {
    // Stop accepting new jobs
    if (scheduler) {
      console.log('📅 Stopping job scheduler...');
      await scheduler.stop();
    }

    // Stop worker and finish current jobs
    if (worker) {
      console.log('⚙️ Stopping worker process...');
      await worker.stop();
    }

    console.log('✅ Worker shutdown completed successfully');
    Logger.info('Worker shutdown completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error);
    Logger.error('Error during worker shutdown', { error: error.message });
    process.exit(1);
  }
};

// Register shutdown handlers
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // For nodemon

// Main worker startup
async function startWorker() {
  try {
    console.log('🚀 Starting HLenergy Background Worker...');
    console.log(`📍 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`📍 Process ID: ${process.pid}`);
    
    Logger.info('Starting HLenergy Background Worker', {
      environment: process.env.NODE_ENV || 'development',
      pid: process.pid,
      version: require('./package.json').version,
    });

    // Initialize database connection
    console.log('🔌 Connecting to database...');
    const { initializeDatabase } = require('./models');
    await initializeDatabase();
    console.log('✅ Database connected successfully');

    // Create and start worker
    console.log('⚙️ Initializing worker process...');
    worker = new Worker({
      maxConcurrentJobs: parseInt(process.env.WORKER_MAX_CONCURRENT_JOBS) || 5,
      pollInterval: parseInt(process.env.WORKER_POLL_INTERVAL) || 5000,
      maxRetries: parseInt(process.env.WORKER_MAX_RETRIES) || 3,
      retryDelay: parseInt(process.env.WORKER_RETRY_DELAY) || 30000,
    });

    await worker.start();
    console.log('✅ Worker process started successfully');

    // Create and start job scheduler
    console.log('📅 Initializing job scheduler...');
    scheduler = new JobScheduler();
    await scheduler.start();
    console.log('✅ Job scheduler started successfully');

    // Log startup completion
    console.log('🎉 HLenergy Background Worker is now running!');
    console.log('📊 Worker will process jobs and maintain system health');
    console.log('🔄 Press Ctrl+C to stop gracefully\n');

    Logger.info('HLenergy Background Worker started successfully', {
      maxConcurrentJobs: worker.config.maxConcurrentJobs,
      pollInterval: worker.config.pollInterval,
    });

    // Keep process alive and log periodic status
    setInterval(() => {
      if (!isShuttingDown) {
        const stats = worker.getStats();
        console.log(`📊 Worker Status - Processed: ${stats.processed}, Failed: ${stats.failed}, Active: ${stats.active}`);
      }
    }, 60000); // Log every minute

  } catch (error) {
    console.error('❌ Failed to start worker:', error);
    Logger.error('Failed to start worker process', { error: error.message, stack: error.stack });
    process.exit(1);
  }
}

// Start the worker
if (require.main === module) {
  startWorker().catch((error) => {
    console.error('❌ Worker startup failed:', error);
    process.exit(1);
  });
}

module.exports = { startWorker, gracefulShutdown };

# HLenergy Background Worker

A standalone background worker process for HLenergy that handles:
- System statistics calculation
- Health monitoring
- Database cleanup tasks
- Email notifications
- Scheduled maintenance

## Features

### 🔄 Job Processing
- **Concurrent Processing**: Handles multiple jobs simultaneously
- **Retry Logic**: Automatic retry with exponential backoff
- **Priority Queue**: High-priority jobs processed first
- **Graceful Shutdown**: Completes active jobs before stopping

### 📊 Statistics Calculation
- **Daily Stats**: User activity, log counts, connection metrics
- **Weekly Reports**: Aggregated weekly summaries
- **Monthly Reports**: Long-term trend analysis
- **Performance Metrics**: Memory usage, response times

### 🏥 Health Monitoring
- **Database Health**: Connection and query performance
- **API Health**: Endpoint availability and response times
- **Memory Monitoring**: Heap usage and memory leaks
- **External Services**: Third-party service availability

### 🧹 Maintenance Tasks
- **Log Cleanup**: Remove old log entries
- **Session Cleanup**: Clean expired user sessions
- **Database Optimization**: Optimize tables and indexes
- **Data Backup**: Backup critical system data

### 📧 Email Notifications
- **Daily Digests**: System summary emails
- **Weekly Reports**: Comprehensive weekly analysis
- **Alert Notifications**: Critical issue alerts

## Installation

### Prerequisites
- Node.js 16+
- MySQL database (shared with main application)
- Access to main application's `.env` file

### Setup
```bash
# Navigate to worker directory
cd backend/worker

# Install dependencies
npm install

# Make startup script executable
chmod +x start-worker.sh
```

## Configuration

The worker uses the same `.env` file as the main application, plus these additional variables:

```env
# Worker Configuration
WORKER_MAX_CONCURRENT_JOBS=5
WORKER_POLL_INTERVAL=5000
WORKER_MAX_RETRIES=3
WORKER_RETRY_DELAY=30000

# Debug Settings
SOCKET_DEBUG=false
DEBUG=false

# Timezone for scheduled jobs
TZ=UTC
```

## Usage

### Development Mode
```bash
# Start with auto-restart
npm run dev

# Or use the startup script
./start-worker.sh
```

### Production Mode
```bash
# Start worker
npm start

# Or use PM2 (recommended)
pm2 start index.js --name hlenergy-worker
pm2 save
pm2 startup
```

### Manual Job Management
```bash
# Enqueue a job via API
curl -X POST http://localhost:3001/api/v1/worker/jobs \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jobType": "calculate-daily-stats",
    "priority": 5
  }'

# Check job status
curl http://localhost:3001/api/v1/worker/jobs \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## Job Types

### Statistics Jobs
- `calculate-daily-stats` - Calculate daily system statistics
- `calculate-weekly-stats` - Calculate weekly summaries
- `calculate-monthly-stats` - Calculate monthly reports
- `cleanup-old-stats` - Remove expired cached statistics

### Health Check Jobs
- `check-database-health` - Test database connectivity and performance
- `check-api-health` - Test API endpoint availability
- `check-memory-usage` - Monitor memory usage and performance
- `check-external-services` - Test external service availability

### Cleanup Jobs
- `cleanup-old-logs` - Remove old log entries
- `cleanup-expired-sessions` - Clean expired user sessions
- `cleanup-old-jobs` - Remove completed background jobs
- `optimize-database` - Optimize database tables
- `backup-critical-data` - Backup important system data

### Email Jobs
- `send-daily-digest` - Send daily system summary
- `send-weekly-report` - Send weekly analysis report
- `send-alert-notification` - Send critical alerts

## Scheduling

Jobs are automatically scheduled using cron expressions:

| Job | Schedule | Description |
|-----|----------|-------------|
| Daily Stats | `*/15 * * * *` | Every 15 minutes |
| Health Checks | `*/5 * * * *` | Every 5 minutes |
| Log Cleanup | `0 3 * * *` | Daily at 3 AM |
| Weekly Report | `0 9 * * 1` | Mondays at 9 AM |
| Monthly Report | `0 2 1 * *` | 1st of month at 2 AM |

## Monitoring

### Worker Status
```bash
# Check worker health
curl http://localhost:3001/api/v1/worker/health \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# View job queue
curl http://localhost:3001/api/v1/worker/jobs \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### Logs
The worker logs all activities with structured logging:
- **INFO**: Normal operations and job completions
- **WARN**: Non-critical issues and retries
- **ERROR**: Job failures and system errors
- **DEBUG**: Detailed debugging information (dev mode only)

### Process Management
```bash
# With PM2
pm2 status hlenergy-worker
pm2 logs hlenergy-worker
pm2 restart hlenergy-worker
pm2 stop hlenergy-worker

# Direct process
ps aux | grep "node.*worker"
kill -TERM <worker-pid>  # Graceful shutdown
```

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Main Server   │    │ Standalone      │    │    Database     │
│                 │    │ Worker Process  │    │                 │
│ - API Routes    │    │                 │    │ - Jobs Queue    │
│ - Socket.io     │◄──►│ - Job Processor │◄──►│ - Stats Cache   │
│ - Job Enqueuer  │    │ - Health Checks │    │ - Health Data   │
│                 │    │ - Stats Calc    │    │ - Shared Models │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Troubleshooting

### Common Issues

**Worker won't start**
- Check Node.js version (16+ required)
- Verify database connection
- Check `.env` file exists and is readable

**Jobs not processing**
- Check worker is running: `pm2 status`
- Verify database connectivity
- Check job queue: `curl .../api/v1/worker/jobs`

**High memory usage**
- Monitor with: `curl .../api/v1/worker/health`
- Reduce `WORKER_MAX_CONCURRENT_JOBS`
- Check for memory leaks in job handlers

**Jobs failing repeatedly**
- Check worker logs: `pm2 logs hlenergy-worker`
- Verify job data format
- Check database permissions

### Debug Mode
```bash
# Enable debug logging
export DEBUG=true
export SOCKET_DEBUG=true
npm run dev
```

## Development

### Adding New Job Types
1. Create handler in `handlers/` directory
2. Register in `handlers/index.js`
3. Add to job type validation in `BackgroundJob.js`
4. Update documentation

### Testing Jobs
```javascript
// Enqueue test job
const { BackgroundJob } = require('../shared/models');
await BackgroundJob.enqueue('your-job-type', { test: 'data' });
```

## License

MIT License - see main application license.

const { Log, User<PERSON><PERSON>ion, BackgroundJob, <PERSON>Check } = require('../models');
const { Logger } = require('../utils/logger');

/**
 * Cleanup Task Handlers
 * Handles background jobs for database cleanup and maintenance
 */

/**
 * Cleanup old log entries
 */
async function cleanupOldLogs(jobData, job) {
  const { daysToKeep = 30 } = jobData;
  
  console.log(`🧹 Cleaning up logs older than ${daysToKeep} days...`);
  
  try {
    const { Op } = require('sequelize');
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const deletedCount = await Log.destroy({
      where: {
        created_at: {
          [Op.lt]: cutoffDate,
        },
      },
    });

    console.log(`✅ Cleaned up ${deletedCount} old log entries`);
    Logger.info('Old logs cleaned up', { deletedCount, daysToKeep });

    return {
      success: true,
      deletedCount,
      daysToKeep,
      message: `Cleaned up ${deletedCount} log entries older than ${daysToKeep} days`,
    };

  } catch (error) {
    console.error('❌ Failed to cleanup old logs:', error);
    Logger.error('Failed to cleanup old logs', { error: error.message, daysToKeep });
    throw error;
  }
}

/**
 * Cleanup expired user sessions
 */
async function cleanupExpiredSessions(jobData, job) {
  console.log('🧹 Cleaning up expired user sessions...');
  
  try {
    const deletedCount = await UserSession.cleanupExpiredSessions();

    console.log(`✅ Cleaned up ${deletedCount} expired sessions`);
    Logger.info('Expired sessions cleaned up', { deletedCount });

    return {
      success: true,
      deletedCount,
      message: `Cleaned up ${deletedCount} expired sessions`,
    };

  } catch (error) {
    console.error('❌ Failed to cleanup expired sessions:', error);
    Logger.error('Failed to cleanup expired sessions', { error: error.message });
    throw error;
  }
}

/**
 * Cleanup old background jobs
 */
async function cleanupOldJobs(jobData, job) {
  const { daysToKeep = 7 } = jobData;
  
  console.log(`🧹 Cleaning up completed jobs older than ${daysToKeep} days...`);
  
  try {
    const deletedCount = await BackgroundJob.cleanupOldJobs(daysToKeep);

    console.log(`✅ Cleaned up ${deletedCount} old job entries`);
    Logger.info('Old jobs cleaned up', { deletedCount, daysToKeep });

    return {
      success: true,
      deletedCount,
      daysToKeep,
      message: `Cleaned up ${deletedCount} job entries older than ${daysToKeep} days`,
    };

  } catch (error) {
    console.error('❌ Failed to cleanup old jobs:', error);
    Logger.error('Failed to cleanup old jobs', { error: error.message, daysToKeep });
    throw error;
  }
}

/**
 * Cleanup old health check records
 */
async function cleanupOldHealthChecks(jobData, job) {
  const { daysToKeep = 7 } = jobData;
  
  console.log(`🧹 Cleaning up health checks older than ${daysToKeep} days...`);
  
  try {
    const deletedCount = await HealthCheck.cleanupOldChecks(daysToKeep);

    console.log(`✅ Cleaned up ${deletedCount} old health check records`);
    Logger.info('Old health checks cleaned up', { deletedCount, daysToKeep });

    return {
      success: true,
      deletedCount,
      daysToKeep,
      message: `Cleaned up ${deletedCount} health check records older than ${daysToKeep} days`,
    };

  } catch (error) {
    console.error('❌ Failed to cleanup old health checks:', error);
    Logger.error('Failed to cleanup old health checks', { error: error.message, daysToKeep });
    throw error;
  }
}

/**
 * Optimize database tables
 */
async function optimizeDatabase(jobData, job) {
  console.log('🔧 Optimizing database tables...');
  
  try {
    const { sequelize } = require('../config/database');
    
    // Get list of tables to optimize
    const tables = [
      'logs',
      'user_sessions', 
      'background_jobs',
      'health_checks',
      'system_stats',
    ];

    const results = [];
    
    for (const table of tables) {
      try {
        console.log(`🔧 Optimizing table: ${table}`);
        
        // Run OPTIMIZE TABLE (MySQL specific)
        await sequelize.query(`OPTIMIZE TABLE ${table}`);
        
        results.push({
          table,
          status: 'optimized',
        });
        
        console.log(`✅ Optimized table: ${table}`);
        
      } catch (error) {
        console.error(`❌ Failed to optimize table ${table}:`, error);
        results.push({
          table,
          status: 'failed',
          error: error.message,
        });
      }
    }

    const successCount = results.filter(r => r.status === 'optimized').length;
    
    console.log(`✅ Database optimization completed: ${successCount}/${tables.length} tables optimized`);
    Logger.info('Database optimization completed', { results, successCount, totalTables: tables.length });

    return {
      success: true,
      results,
      successCount,
      totalTables: tables.length,
      message: `Optimized ${successCount}/${tables.length} database tables`,
    };

  } catch (error) {
    console.error('❌ Database optimization failed:', error);
    Logger.error('Database optimization failed', { error: error.message });
    throw error;
  }
}

/**
 * Backup critical data
 */
async function backupCriticalData(jobData, job) {
  console.log('💾 Creating backup of critical data...');
  
  try {
    const fs = require('fs').promises;
    const path = require('path');
    
    // Create backup directory
    const backupDir = path.join(process.cwd(), 'backups');
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(backupDir, `backup-${timestamp}.json`);
    
    try {
      await fs.mkdir(backupDir, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }

    // Gather critical data
    const { User, SystemStats } = require('../models');
    
    const criticalData = {
      timestamp: new Date().toISOString(),
      users: {
        total: await User.count(),
        admins: await User.count({ where: { role: 'admin' } }),
      },
      stats: await SystemStats.findAll({
        order: [['calculated_at', 'DESC']],
        limit: 10,
      }),
      systemInfo: {
        nodeVersion: process.version,
        platform: process.platform,
        uptime: process.uptime(),
      },
    };

    // Write backup file
    await fs.writeFile(backupPath, JSON.stringify(criticalData, null, 2));
    
    // Get file size
    const stats = await fs.stat(backupPath);
    const fileSizeKB = Math.round(stats.size / 1024);

    console.log(`✅ Critical data backup created: ${backupPath} (${fileSizeKB} KB)`);
    Logger.info('Critical data backup created', { 
      backupPath, 
      fileSizeKB,
      userCount: criticalData.users.total,
    });

    return {
      success: true,
      backupPath,
      fileSizeKB,
      timestamp,
      message: `Critical data backup created (${fileSizeKB} KB)`,
    };

  } catch (error) {
    console.error('❌ Failed to create backup:', error);
    Logger.error('Failed to create backup', { error: error.message });
    throw error;
  }
}

module.exports = {
  cleanupOldLogs,
  cleanupExpiredSessions,
  cleanupOldJobs,
  cleanupOldHealthChecks,
  optimizeDatabase,
  backupCriticalData,
};

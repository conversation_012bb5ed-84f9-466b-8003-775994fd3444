const { SystemStats, HealthCheck } = require('../models');
const { Logger } = require('../utils/logger');

/**
 * Record health check results in the database
 */
async function recordHealthChecks(healthResults) {
  try {
    const healthChecks = [];

    // Record database health
    if (healthResults.database) {
      healthChecks.push({
        check_name: 'database-connectivity',
        status: healthResults.database.status,
        response_time_ms: healthResults.database.responseTime,
        details: healthResults.database.details
      });
    }

    // Record API health
    if (healthResults.api) {
      healthChecks.push({
        check_name: 'api-health',
        status: healthResults.api.status,
        response_time_ms: healthResults.api.responseTime,
        details: healthResults.api.details
      });
    }

    // Record frontend health
    if (healthResults.frontend) {
      healthChecks.push({
        check_name: 'frontend-health',
        status: healthResults.frontend.status,
        response_time_ms: healthResults.frontend.responseTime,
        details: healthResults.frontend.details
      });
    }

    // Bulk create health check records
    if (healthChecks.length > 0) {
      await HealthCheck.bulkCreate(healthChecks);
      console.log(`✅ Recorded ${healthChecks.length} health check results`);
    }

  } catch (error) {
    console.error('❌ Failed to record health checks:', error);
    Logger.error('Failed to record health checks', { error: error.message });
  }
}

/**
 * Email Notification Handlers
 * Handles background jobs for sending email reports and notifications
 */

/**
 * Send daily digest email
 */
async function sendDailyDigest(jobData, job) {
  console.log('📧 Preparing daily digest email...');
  
  try {
    // Get daily stats
    const dailyStats = await SystemStats.getCachedStats('daily-summary');
    if (!dailyStats) {
      throw new Error('Daily stats not available');
    }

    // Get health overview
    const healthOverview = await HealthCheck.getOverallHealth();

    // Get email recipient from environment variables
    const recipientEmail = process.env.SYSTEM_ALERT_EMAIL || process.env.CONTACT_NOTIFICATION_EMAIL;

    if (!recipientEmail) {
      console.warn('⚠️ No email recipient configured for daily digest');
      return {
        success: true,
        message: 'No email recipient configured for daily digest',
      };
    }

    // Prepare email content
    const subject = `HLenergy Daily Report - ${dailyStats.date}`;
    const htmlContent = generateDailyDigestHTML(dailyStats, healthOverview);
    const textContent = generateDailyDigestText(dailyStats, healthOverview);

    // Queue email through the email worker
    const { EmailQueue } = require('../models');

    const emailEntry = await EmailQueue.create({
      to_email: recipientEmail,
      to_name: 'System Administrator',
      subject: subject,
      html_content: htmlContent,
      text_content: textContent,
      email_type: 'daily_digest',
      priority: 3,
      metadata: {
        reportDate: dailyStats.date,
        timestamp: new Date().toISOString(),
        source: 'worker_daily_digest'
      }
    });

    console.log(`✅ Daily digest email queued successfully: ID ${emailEntry.id}`);
    const successCount = 1;
    
    console.log(`✅ Daily digest email queued for: ${recipientEmail}`);
    Logger.info('Daily digest queued', {
      emailId: emailEntry.id,
      recipient: recipientEmail,
      reportDate: dailyStats.date
    });

    return {
      success: true,
      emailResults: [{
        email: recipientEmail,
        status: 'queued',
        emailId: emailEntry.id
      }],
      successCount,
      totalRecipients: 1,
      message: `Daily digest email queued for ${recipientEmail}`,
    };

  } catch (error) {
    console.error('❌ Failed to send daily digest:', error);
    Logger.error('Failed to send daily digest', { error: error.message });
    throw error;
  }
}

/**
 * Send weekly report email
 */
async function sendWeeklyReport(jobData, job) {
  console.log('📧 Preparing weekly report email...');
  
  try {
    // Get weekly stats
    const weeklyStats = await SystemStats.getCachedStats('weekly-summary');
    if (!weeklyStats) {
      throw new Error('Weekly stats not available');
    }

    // Get email recipient from environment variables
    const recipientEmail = process.env.SYSTEM_ALERT_EMAIL || process.env.CONTACT_NOTIFICATION_EMAIL;

    if (!recipientEmail) {
      console.warn('⚠️ No email recipient configured for weekly report');
      return {
        success: true,
        message: 'No email recipient configured for weekly report',
      };
    }

    // Prepare email content
    const subject = `HLenergy Weekly Report - Week of ${weeklyStats.weekStart}`;
    const htmlContent = generateWeeklyReportHTML(weeklyStats);
    const textContent = generateWeeklyReportText(weeklyStats);

    // Queue email through the email worker
    const { EmailQueue } = require('../models');

    const emailEntry = await EmailQueue.create({
      to_email: recipientEmail,
      to_name: 'System Administrator',
      subject: subject,
      html_content: htmlContent,
      text_content: textContent,
      email_type: 'weekly_report',
      priority: 3,
      metadata: {
        weekStart: weeklyStats.weekStart,
        timestamp: new Date().toISOString(),
        source: 'worker_weekly_report'
      }
    });

    console.log(`✅ Weekly report email queued successfully: ID ${emailEntry.id}`);
    const successCount = 1;

    console.log(`✅ Weekly report email queued for: ${recipientEmail}`);
    Logger.info('Weekly report queued', {
      emailId: emailEntry.id,
      recipient: recipientEmail,
      weekStart: weeklyStats.weekStart
    });

    return {
      success: true,
      emailResults: [{
        email: recipientEmail,
        status: 'queued',
        emailId: emailEntry.id
      }],
      successCount,
      totalRecipients: 1,
      message: `Weekly report email queued for ${recipientEmail}`,
    };

  } catch (error) {
    console.error('❌ Failed to send weekly report:', error);
    Logger.error('Failed to send weekly report', { error: error.message });
    throw error;
  }
}

/**
 * Send alert notification email
 */
async function sendAlertNotification(jobData, job) {
  const { alertType, message, severity = 'medium', details = {} } = jobData;

  console.log(`📧 Queuing alert notification: ${alertType}`);

  try {
    // Get email recipients from environment variables
    const healthAlertEmail = process.env.HEALTH_ALERT_EMAIL || process.env.SYSTEM_ALERT_EMAIL || process.env.CONTACT_NOTIFICATION_EMAIL;
    const systemAlertEmail = process.env.SYSTEM_ALERT_EMAIL || process.env.CONTACT_NOTIFICATION_EMAIL;

    // Determine recipient based on alert type
    let recipientEmail;
    if (alertType === 'system_health_alert') {
      recipientEmail = healthAlertEmail;
    } else {
      recipientEmail = systemAlertEmail;
    }

    if (!recipientEmail) {
      console.warn('⚠️ No email recipient configured for alert notifications');
      return {
        success: true,
        emailResults: [],
        successCount: 0,
        totalRecipients: 0,
        message: 'No email recipient configured'
      };
    }

    // Record health checks if this is a health alert
    if (alertType === 'system_health_alert' && details.healthResults) {
      await recordHealthChecks(details.healthResults);
    }

    // Prepare email content
    const subject = `🚨 HLenergy Alert: ${alertType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}`;
    const htmlContent = generateAlertHTML(alertType, message, severity, details);
    const textContent = generateAlertText(alertType, message, severity, details);

    // Queue email through the email worker
    const { EmailQueue } = require('../models');

    const emailEntry = await EmailQueue.create({
      to_email: recipientEmail,
      to_name: 'System Administrator',
      subject: subject,
      html_content: htmlContent,
      text_content: textContent,
      email_type: 'system_alert',
      priority: severity === 'critical' ? 10 : severity === 'high' ? 8 : 5,
      metadata: {
        alertType,
        severity,
        timestamp: new Date().toISOString(),
        source: 'worker_health_check'
      }
    });

    console.log(`✅ Alert email queued successfully: ID ${emailEntry.id}`);
    Logger.info('Alert email queued', {
      emailId: emailEntry.id,
      alertType,
      severity,
      recipient: recipientEmail
    });

    return {
      success: true,
      emailResults: [{
        email: recipientEmail,
        status: 'queued',
        emailId: emailEntry.id
      }],
      successCount: 1,
      totalRecipients: 1,
      message: `Alert email queued for ${recipientEmail}`
    };

  } catch (error) {
    console.error('❌ Failed to queue alert notification:', error);
    Logger.error('Failed to queue alert notification', { error: error.message, alertType });
    throw error;
  }
}

// Helper functions for generating email content
function generateDailyDigestHTML(dailyStats, healthOverview) {
  return `
    <h2>HLenergy Daily Report - ${dailyStats.date}</h2>
    <h3>📊 Statistics</h3>
    <ul>
      <li>Total Users: ${dailyStats.users.total}</li>
      <li>Active Users (24h): ${dailyStats.users.active24h}</li>
      <li>New Users (24h): ${dailyStats.users.new24h}</li>
      <li>Total Logs: ${dailyStats.logs.total}</li>
      <li>New Logs (24h): ${dailyStats.logs.new24h}</li>
      <li>Current Connections: ${dailyStats.connections.current}</li>
    </ul>
    <h3>🏥 System Health</h3>
    <p>Overall Status: <strong>${healthOverview.status}</strong></p>
    <p>${healthOverview.message}</p>
  `;
}

function generateDailyDigestText(dailyStats, healthOverview) {
  return `
HLenergy Daily Report - ${dailyStats.date}

Statistics:
- Total Users: ${dailyStats.users.total}
- Active Users (24h): ${dailyStats.users.active24h}
- New Users (24h): ${dailyStats.users.new24h}
- Total Logs: ${dailyStats.logs.total}
- New Logs (24h): ${dailyStats.logs.new24h}
- Current Connections: ${dailyStats.connections.current}

System Health:
Overall Status: ${healthOverview.status}
${healthOverview.message}
  `;
}

function generateWeeklyReportHTML(weeklyStats) {
  return `
    <h2>HLenergy Weekly Report</h2>
    <p>Week: ${weeklyStats.weekStart} to ${weeklyStats.weekEnd}</p>
    <h3>📊 Weekly Statistics</h3>
    <ul>
      <li>Total Users: ${weeklyStats.users.total}</li>
      <li>Active Users (7d): ${weeklyStats.users.active7d}</li>
      <li>New Users (7d): ${weeklyStats.users.new7d}</li>
    </ul>
  `;
}

function generateWeeklyReportText(weeklyStats) {
  return `
HLenergy Weekly Report
Week: ${weeklyStats.weekStart} to ${weeklyStats.weekEnd}

Weekly Statistics:
- Total Users: ${weeklyStats.users.total}
- Active Users (7d): ${weeklyStats.users.active7d}
- New Users (7d): ${weeklyStats.users.new7d}
  `;
}

function generateAlertHTML(alertType, message, severity, details) {
  const severityColor = severity === 'critical' ? 'red' :
                       severity === 'high' ? 'orange' :
                       severity === 'medium' ? 'orange' : 'yellow';

  const severityIcon = severity === 'critical' ? '🔴' :
                      severity === 'high' ? '🟠' :
                      severity === 'medium' ? '🟡' : '🟢';

  // Special handling for system health alerts
  if (alertType === 'system_health_alert' && details.issues) {
    return generateHealthAlertHTML(message, severity, details);
  }

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background: ${severityColor}; color: white; padding: 20px; text-align: center;">
        <h1 style="margin: 0;">${severityIcon} HLenergy System Alert</h1>
      </div>

      <div style="padding: 20px; background: #f9f9f9;">
        <h2 style="color: ${severityColor}; margin-top: 0;">Alert Details</h2>

        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
          <tr style="background: #e9e9e9;">
            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Alert Type</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${alertType}</td>
          </tr>
          <tr>
            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Severity</td>
            <td style="padding: 10px; border: 1px solid #ddd; color: ${severityColor}; font-weight: bold;">${severity.toUpperCase()}</td>
          </tr>
          <tr style="background: #e9e9e9;">
            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Message</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${message}</td>
          </tr>
          <tr>
            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Time</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${new Date().toLocaleString()}</td>
          </tr>
        </table>

        ${Object.keys(details).length > 0 ? `
          <h3>Additional Details:</h3>
          <div style="background: white; padding: 15px; border-left: 4px solid ${severityColor}; margin: 10px 0;">
            <pre style="white-space: pre-wrap; font-size: 12px; overflow-x: auto;">${JSON.stringify(details, null, 2)}</pre>
          </div>
        ` : ''}

        <div style="margin-top: 30px; padding: 15px; background: #e7f3ff; border-left: 4px solid #2196F3;">
          <p style="margin: 0;"><strong>Next Steps:</strong></p>
          <ul style="margin: 10px 0;">
            <li>Check the admin dashboard for more details</li>
            <li>Review system logs for additional information</li>
            <li>Contact the development team if issues persist</li>
          </ul>
        </div>
      </div>

      <div style="background: #333; color: white; padding: 15px; text-align: center; font-size: 12px;">
        <p style="margin: 0;">HLenergy Monitoring System | Generated at ${new Date().toISOString()}</p>
      </div>
    </div>
  `;
}

function generateHealthAlertHTML(message, severity, details) {
  const severityColor = severity === 'critical' ? '#dc3545' :
                       severity === 'high' ? '#fd7e14' :
                       severity === 'medium' ? '#ffc107' : '#28a745';

  const { overallStatus, issues, healthResults, timestamp, checkDuration } = details;

  return `
    <div style="font-family: Arial, sans-serif; max-width: 700px; margin: 0 auto;">
      <div style="background: ${severityColor}; color: white; padding: 20px; text-align: center;">
        <h1 style="margin: 0;">🏥 HLenergy Health Alert</h1>
        <p style="margin: 10px 0 0 0; font-size: 18px;">${message}</p>
      </div>

      <div style="padding: 20px; background: #f8f9fa;">
        <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h2 style="color: #333; margin-top: 0;">System Overview</h2>

          <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr style="background: #e9ecef;">
              <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Overall Status</td>
              <td style="padding: 12px; border: 1px solid #dee2e6; color: ${severityColor}; font-weight: bold;">${overallStatus.toUpperCase()}</td>
            </tr>
            <tr>
              <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Check Duration</td>
              <td style="padding: 12px; border: 1px solid #dee2e6;">${checkDuration}ms</td>
            </tr>
            <tr style="background: #e9ecef;">
              <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Issues Found</td>
              <td style="padding: 12px; border: 1px solid #dee2e6;">${issues.length}</td>
            </tr>
            <tr>
              <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Timestamp</td>
              <td style="padding: 12px; border: 1px solid #dee2e6;">${new Date(timestamp).toLocaleString()}</td>
            </tr>
          </table>
        </div>

        ${issues.length > 0 ? `
          <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h3 style="color: #dc3545; margin-top: 0;">🚨 Issues Detected</h3>

            ${issues.map(issue => `
              <div style="border-left: 4px solid ${issue.severity === 'critical' ? '#dc3545' : issue.severity === 'high' ? '#fd7e14' : '#ffc107'}; padding: 15px; margin: 15px 0; background: #f8f9fa;">
                <h4 style="margin: 0 0 10px 0; color: #333;">
                  ${issue.severity === 'critical' ? '🔴' : issue.severity === 'high' ? '🟠' : '🟡'}
                  ${issue.component} - ${issue.status.toUpperCase()}
                </h4>
                <p style="margin: 5px 0; color: #666;"><strong>Severity:</strong> ${issue.severity}</p>
                ${issue.details && Object.keys(issue.details).length > 0 ? `
                  <details style="margin: 10px 0;">
                    <summary style="cursor: pointer; color: #007bff;">View Details</summary>
                    <pre style="background: #f1f3f4; padding: 10px; margin: 10px 0; border-radius: 4px; font-size: 11px; overflow-x: auto;">${JSON.stringify(issue.details, null, 2)}</pre>
                  </details>
                ` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}

        <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h3 style="color: #333; margin-top: 0;">📋 Component Status</h3>

          <div style="display: grid; gap: 15px;">
            ${Object.entries(healthResults).map(([component, result]) => `
              <div style="border: 1px solid #dee2e6; border-radius: 6px; padding: 15px;">
                <h4 style="margin: 0 0 10px 0; color: #333; text-transform: capitalize;">
                  ${result.status === 'healthy' ? '✅' : result.status === 'degraded' ? '⚠️' : '❌'}
                  ${component}
                </h4>
                <p style="margin: 5px 0; color: ${result.status === 'healthy' ? '#28a745' : result.status === 'degraded' ? '#ffc107' : '#dc3545'};">
                  <strong>Status:</strong> ${result.status.toUpperCase()}
                </p>
                ${result.responseTime ? `<p style="margin: 5px 0; color: #666;"><strong>Response Time:</strong> ${result.responseTime}ms</p>` : ''}
              </div>
            `).join('')}
          </div>
        </div>

        <div style="background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3;">
          <h3 style="margin: 0 0 15px 0; color: #1976D2;">🔧 Recommended Actions</h3>
          <ul style="margin: 0; color: #333;">
            <li>Access the admin dashboard to view detailed system status</li>
            <li>Check the Worker Management section for job queue status</li>
            <li>Review system logs for additional error information</li>
            <li>Monitor the situation and escalate if issues persist</li>
            ${issues.some(i => i.component === 'Database') ? '<li><strong>Database Issue:</strong> Check database connectivity and performance</li>' : ''}
            ${issues.some(i => i.component === 'API') ? '<li><strong>API Issue:</strong> Verify API server status and endpoint availability</li>' : ''}
            ${issues.some(i => i.component === 'Frontend') ? '<li><strong>Frontend Issue:</strong> Check frontend deployment and accessibility</li>' : ''}
          </ul>
        </div>
      </div>

      <div style="background: #333; color: white; padding: 15px; text-align: center; font-size: 12px;">
        <p style="margin: 0;">HLenergy Health Monitoring System | Auto-generated alert</p>
        <p style="margin: 5px 0 0 0;">Dashboard: <a href="https://hlenergy.pt/admin" style="color: #4CAF50;">https://hlenergy.pt/admin</a></p>
      </div>
    </div>
  `;
}

function generateAlertText(alertType, message, severity, details) {
  // Special handling for system health alerts
  if (alertType === 'system_health_alert' && details.issues) {
    return generateHealthAlertText(message, severity, details);
  }

  return `
🚨 HLenergy System Alert

Alert Type: ${alertType}
Severity: ${severity.toUpperCase()}
Message: ${message}
Time: ${new Date().toLocaleString()}

${Object.keys(details).length > 0 ? `
Additional Details:
${JSON.stringify(details, null, 2)}
` : ''}

Next Steps:
- Check the admin dashboard for more details
- Review system logs for additional information
- Contact the development team if issues persist

---
HLenergy Monitoring System
Generated at: ${new Date().toISOString()}
  `;
}

function generateHealthAlertText(message, severity, details) {
  const { overallStatus, issues, healthResults, timestamp, checkDuration } = details;

  return `
🏥 HLenergy Health Alert

${message}

SYSTEM OVERVIEW:
- Overall Status: ${overallStatus.toUpperCase()}
- Check Duration: ${checkDuration}ms
- Issues Found: ${issues.length}
- Timestamp: ${new Date(timestamp).toLocaleString()}

${issues.length > 0 ? `
🚨 ISSUES DETECTED:

${issues.map(issue => `
${issue.severity === 'critical' ? '🔴' : issue.severity === 'high' ? '🟠' : '🟡'} ${issue.component} - ${issue.status.toUpperCase()}
   Severity: ${issue.severity}
   ${issue.details && Object.keys(issue.details).length > 0 ? `Details: ${JSON.stringify(issue.details, null, 2)}` : ''}
`).join('\n')}
` : ''}

📋 COMPONENT STATUS:

${Object.entries(healthResults).map(([component, result]) => `
${result.status === 'healthy' ? '✅' : result.status === 'degraded' ? '⚠️' : '❌'} ${component.toUpperCase()}
   Status: ${result.status.toUpperCase()}
   ${result.responseTime ? `Response Time: ${result.responseTime}ms` : ''}
`).join('\n')}

🔧 RECOMMENDED ACTIONS:
- Access the admin dashboard to view detailed system status
- Check the Worker Management section for job queue status
- Review system logs for additional error information
- Monitor the situation and escalate if issues persist
${issues.some(i => i.component === 'Database') ? '- DATABASE ISSUE: Check database connectivity and performance' : ''}
${issues.some(i => i.component === 'API') ? '- API ISSUE: Verify API server status and endpoint availability' : ''}
${issues.some(i => i.component === 'Frontend') ? '- FRONTEND ISSUE: Check frontend deployment and accessibility' : ''}

---
HLenergy Health Monitoring System
Dashboard: https://hlenergy.pt/admin
Auto-generated alert at: ${new Date().toISOString()}
  `;
}

module.exports = {
  sendDailyDigest,
  sendWeeklyReport,
  sendAlertNotification,
};

const { SystemStats } = require('../models');
const { Logger } = require('../utils/logger');

/**
 * Statistics Calculation Handlers
 * Handles background jobs for calculating and caching system statistics
 */

/**
 * Calculate daily statistics
 */
async function calculateDailyStats(jobData, job) {
  console.log('📊 Calculating daily statistics...');
  
  try {
    // Calculate all daily stats
    const [userStats, logStats, connectionStats] = await Promise.all([
      SystemStats.calculateUserStats(),
      SystemStats.calculateLogStats(),
      SystemStats.calculateConnectionStats(),
    ]);

    // Create daily summary
    const dailySummary = {
      date: new Date().toISOString().split('T')[0],
      users: {
        total: userStats.total,
        active24h: userStats.active.last24h,
        new24h: userStats.new.last24h,
        activeSessions: userStats.sessions.active,
      },
      logs: {
        total: logStats.total,
        new24h: logStats.recent.last24h,
        errors24h: logStats.errors.last24h,
        warnings24h: logStats.warnings.last24h,
      },
      connections: {
        current: connectionStats.current,
        peak24h: connectionStats.peak24h,
        userConnections: connectionStats.userConnections,
        adminConnections: connectionStats.adminConnections,
      },
      calculatedAt: new Date().toISOString(),
    };

    // Cache the daily summary
    await SystemStats.setCachedStats('daily-summary', dailySummary, 24 * 60); // Cache for 24 hours

    console.log('✅ Daily statistics calculated successfully');
    Logger.info('Daily statistics calculated', {
      userTotal: userStats.total,
      logsTotal: logStats.total,
      currentConnections: connectionStats.current,
    });

    return {
      success: true,
      summary: dailySummary,
      message: 'Daily statistics calculated successfully',
    };

  } catch (error) {
    console.error('❌ Failed to calculate daily statistics:', error);
    Logger.error('Failed to calculate daily statistics', { error: error.message });
    throw error;
  }
}

/**
 * Calculate weekly statistics
 */
async function calculateWeeklyStats(jobData, job) {
  console.log('📊 Calculating weekly statistics...');
  
  try {
    // Get current week's data
    const now = new Date();
    const weekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Calculate weekly stats (this would need more complex queries)
    const userStats = await SystemStats.calculateUserStats();
    const logStats = await SystemStats.calculateLogStats();

    const weeklySummary = {
      weekStart: weekStart.toISOString().split('T')[0],
      weekEnd: now.toISOString().split('T')[0],
      users: {
        total: userStats.total,
        active7d: userStats.active.last7d,
        new7d: userStats.new.last7d,
      },
      logs: {
        total: logStats.total,
        new7d: logStats.recent.last7d,
      },
      calculatedAt: new Date().toISOString(),
    };

    // Cache the weekly summary
    await SystemStats.setCachedStats('weekly-summary', weeklySummary, 7 * 24 * 60); // Cache for 7 days

    console.log('✅ Weekly statistics calculated successfully');
    Logger.info('Weekly statistics calculated', weeklySummary);

    return {
      success: true,
      summary: weeklySummary,
      message: 'Weekly statistics calculated successfully',
    };

  } catch (error) {
    console.error('❌ Failed to calculate weekly statistics:', error);
    Logger.error('Failed to calculate weekly statistics', { error: error.message });
    throw error;
  }
}

/**
 * Calculate monthly statistics
 */
async function calculateMonthlyStats(jobData, job) {
  console.log('📊 Calculating monthly statistics...');
  
  try {
    // Get current month's data
    const now = new Date();
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    // Calculate monthly stats
    const userStats = await SystemStats.calculateUserStats();
    const logStats = await SystemStats.calculateLogStats();

    const monthlySummary = {
      month: now.toISOString().substring(0, 7), // YYYY-MM format
      monthStart: monthStart.toISOString().split('T')[0],
      users: {
        total: userStats.total,
        active30d: userStats.active.last30d,
        new30d: userStats.new.last30d,
      },
      logs: {
        total: logStats.total,
      },
      calculatedAt: new Date().toISOString(),
    };

    // Cache the monthly summary
    await SystemStats.setCachedStats('monthly-summary', monthlySummary, 30 * 24 * 60); // Cache for 30 days

    console.log('✅ Monthly statistics calculated successfully');
    Logger.info('Monthly statistics calculated', monthlySummary);

    return {
      success: true,
      summary: monthlySummary,
      message: 'Monthly statistics calculated successfully',
    };

  } catch (error) {
    console.error('❌ Failed to calculate monthly statistics:', error);
    Logger.error('Failed to calculate monthly statistics', { error: error.message });
    throw error;
  }
}

/**
 * Cleanup old cached statistics
 */
async function cleanupOldStats(jobData, job) {
  console.log('🧹 Cleaning up old cached statistics...');
  
  try {
    const deletedCount = await SystemStats.cleanupExpiredStats();

    console.log(`✅ Cleaned up ${deletedCount} expired stat entries`);
    Logger.info('Old stats cleaned up', { deletedCount });

    return {
      success: true,
      deletedCount,
      message: `Cleaned up ${deletedCount} expired stat entries`,
    };

  } catch (error) {
    console.error('❌ Failed to cleanup old stats:', error);
    Logger.error('Failed to cleanup old stats', { error: error.message });
    throw error;
  }
}

/**
 * Calculate performance statistics
 */
async function calculatePerformanceStats(jobData, job) {
  console.log('📊 Calculating performance statistics...');
  
  try {
    // Get memory usage
    const memUsage = process.memoryUsage();
    const memUsageMB = {
      rss: Math.round(memUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024),
    };

    // Get process info
    const processInfo = {
      uptime: Math.round(process.uptime()),
      pid: process.pid,
      version: process.version,
      platform: process.platform,
      arch: process.arch,
    };

    const performanceStats = {
      memory: memUsageMB,
      process: processInfo,
      heapUsagePercent: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100 * 100) / 100,
      calculatedAt: new Date().toISOString(),
    };

    // Cache performance stats
    await SystemStats.setCachedStats('performance-stats', performanceStats, 10); // Cache for 10 minutes

    console.log('✅ Performance statistics calculated successfully');
    Logger.info('Performance statistics calculated', performanceStats);

    return {
      success: true,
      stats: performanceStats,
      message: 'Performance statistics calculated successfully',
    };

  } catch (error) {
    console.error('❌ Failed to calculate performance statistics:', error);
    Logger.error('Failed to calculate performance statistics', { error: error.message });
    throw error;
  }
}

module.exports = {
  calculateDailyStats,
  calculateWeeklyStats,
  calculateMonthlyStats,
  cleanupOldStats,
  calculatePerformanceStats,
};

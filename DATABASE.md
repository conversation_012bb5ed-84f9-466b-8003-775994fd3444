# HLenergy API Database Setup

This document provides comprehensive instructions for setting up and managing the MySQL database for the HLenergy API.

## 🗄️ Database Overview

The HLenergy API uses MySQL with Sequelize ORM for data persistence. The database includes:

- **Users**: Authentication and user management
- **Contact Submissions**: Customer inquiries and contact forms
- **Refresh Tokens**: JWT token management for secure authentication

## 📋 Prerequisites

### 1. MySQL Installation

**macOS (using Homebrew):**
```bash
brew install mysql
brew services start mysql
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install mysql-server
sudo systemctl start mysql
sudo systemctl enable mysql
```

**Windows:**
Download and install from [MySQL Official Website](https://dev.mysql.com/downloads/mysql/)

### 2. MySQL Configuration

1. **Secure Installation:**
```bash
sudo mysql_secure_installation
```

2. **Create Database User (Optional):**
```sql
CREATE USER 'hlenergy'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON hlenergy_dev.* TO 'hlenergy'@'localhost';
GRANT ALL PRIVILEGES ON hlenergy_test.* TO 'hlenergy'@'localhost';
FLUSH PRIVILEGES;
```

## ⚙️ Environment Configuration

1. **Copy Environment File:**
```bash
cp .env.example .env
```

2. **Update Database Configuration:**
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_mysql_password
DB_NAME=hlenergy_dev
DB_NAME_TEST=hlenergy_test
DB_SSL=false
```

## 🚀 Database Setup

### Automated Setup (Recommended)

```bash
# Complete database setup (create, migrate, seed)
npm run db:setup

# Or step by step:
npm run db:create    # Create databases
npm run db:migrate   # Run migrations
npm run db:seed      # Insert initial data
```

### Manual Setup

1. **Create Databases:**
```sql
CREATE DATABASE hlenergy_dev;
CREATE DATABASE hlenergy_test;
```

2. **Run Migrations:**
```bash
npx sequelize-cli db:migrate
```

3. **Run Seeders:**
```bash
npx sequelize-cli db:seed:all
```

## 📊 Database Schema

### Users Table
```sql
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  role ENUM('admin', 'staff', 'client') DEFAULT 'client',
  email_verified BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Contact Submissions Table
```sql
CREATE TABLE contact_submissions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  message TEXT NOT NULL,
  status ENUM('new', 'in_progress', 'resolved', 'closed') DEFAULT 'new',
  priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Refresh Tokens Table
```sql
CREATE TABLE refresh_tokens (
  id INT PRIMARY KEY AUTO_INCREMENT,
  token TEXT NOT NULL,
  user_id INT NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  is_revoked BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## 🛠️ Database Management Commands

### NPM Scripts
```bash
# Database setup and management
npm run db:setup          # Complete setup
npm run db:create          # Create databases
npm run db:migrate         # Run migrations
npm run db:seed           # Run seeders
npm run db:status         # Check database status
npm run db:reset          # Reset database (WARNING: deletes all data)
npm run db:backup         # Create database backup

# Development commands
npm run dev               # Start server with auto-restart
npm run deps:check        # Check dependencies
npm run version:status    # Check version status
```

### Direct Sequelize Commands
```bash
# Migrations
npx sequelize-cli migration:generate --name migration-name
npx sequelize-cli db:migrate
npx sequelize-cli db:migrate:undo
npx sequelize-cli db:migrate:undo:all

# Seeders
npx sequelize-cli seed:generate --name seeder-name
npx sequelize-cli db:seed:all
npx sequelize-cli db:seed:undo
npx sequelize-cli db:seed:undo:all
```

## 📈 Monitoring and Health Checks

### API Endpoints
- `GET /health` - Overall system health including database
- `GET /database/stats` - Database statistics and metrics
- `GET /dependencies` - Dependency status

### Database Health Check
```bash
curl http://localhost:3001/health
curl http://localhost:3001/database/stats
```

## 🔧 Troubleshooting

### Common Issues

1. **Connection Refused:**
   - Ensure MySQL is running: `brew services start mysql`
   - Check credentials in `.env` file
   - Verify database exists

2. **Authentication Failed:**
   - Update MySQL password in `.env`
   - Check user permissions
   - Try connecting with MySQL client

3. **Migration Errors:**
   - Check database connection
   - Ensure proper permissions
   - Review migration files for syntax errors

4. **Seeder Failures:**
   - Usually expected if data already exists
   - Check for unique constraint violations
   - Review seeder files

### Debug Commands
```bash
# Test database connection
mysql -h localhost -u root -p

# Check database status
npm run db:status

# View server logs
npm run dev

# Check dependencies
npm run deps:check
```

## 🔒 Security Considerations

1. **Production Environment:**
   - Use strong passwords
   - Enable SSL/TLS connections
   - Restrict database access
   - Regular backups

2. **Development Environment:**
   - Don't commit `.env` file
   - Use different credentials than production
   - Regular dependency updates

## 📚 Additional Resources

- [Sequelize Documentation](https://sequelize.org/docs/v6/)
- [MySQL Documentation](https://dev.mysql.com/doc/)
- [Node.js Best Practices](https://github.com/goldbergyoni/nodebestpractices)

## 🆘 Support

If you encounter issues:
1. Check this documentation
2. Review server logs
3. Test database connection manually
4. Check environment configuration
5. Verify MySQL service status

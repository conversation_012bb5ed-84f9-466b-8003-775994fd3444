#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to manually trigger a comprehensive health check
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });

async function triggerHealthCheck() {
  try {
    // Initialize database connection
    const { initializeDatabase } = require('./shared/models');
    await initializeDatabase();
    
    const { BackgroundJob } = require('./shared/models');
    
    console.log('🏥 Manually triggering comprehensive health check...');
    
    const job = await BackgroundJob.enqueue('comprehensive-health-check', {}, { priority: 8 });
    
    console.log(`✅ Health check job enqueued with ID: ${job.id}`);
    console.log('📋 Job will be processed by the worker shortly...');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Failed to trigger health check:', error);
    process.exit(1);
  }
}

triggerHealthCheck();

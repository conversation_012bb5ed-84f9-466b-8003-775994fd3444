#!/usr/bin/env node

/**
 * Fix Lock Reason Column Migration
 * Fixes the lock_reason column in user_sessions table to support all enum values
 */

require('dotenv').config();
const { Sequelize } = require('sequelize');

const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    dialect: 'mysql',
    logging: console.log,
  }
);

const fixLockReasonColumn = async () => {
  try {
    console.log('🔧 Fixing lock_reason column in user_sessions table...');
    
    // Check current column definition
    const [results] = await sequelize.query(`
      DESCRIBE user_sessions lock_reason
    `);
    
    console.log('Current lock_reason column definition:', results[0]);
    
    // Drop and recreate the column with correct ENUM values
    await sequelize.query(`
      ALTER TABLE user_sessions 
      MODIFY COLUMN lock_reason ENUM('inactivity', 'admin_action', 'security_breach', 'manual') 
      DEFAULT NULL 
      COMMENT 'Reason for session lock'
    `);
    
    console.log('✅ lock_reason column updated successfully');
    
    // Verify the change
    const [newResults] = await sequelize.query(`
      DESCRIBE user_sessions lock_reason
    `);
    
    console.log('Updated lock_reason column definition:', newResults[0]);
    
  } catch (error) {
    console.error('❌ Failed to fix lock_reason column:', error.message);
    throw error;
  }
};

// Run if called directly
if (require.main === module) {
  fixLockReasonColumn()
    .then(() => {
      console.log('🎉 Migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration failed:', error.message);
      process.exit(1);
    });
}

module.exports = { fixLockReasonColumn };

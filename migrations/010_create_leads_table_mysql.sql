-- Leads Database Schema (MySQL Compatible)
-- Migration: 010_create_leads_table_mysql.sql
-- Creates a dedicated leads table for managing potential customers

-- Create leads table
CREATE TABLE IF NOT EXISTS leads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Basic Information
    first_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(50),
    company_name VA<PERSON>HAR(255),
    job_title VARCHAR(100),
    
    -- Lead Details
    lead_source VARCHAR(50) NOT NULL DEFAULT 'website',
    lead_score INT DEFAULT 50 CHECK (lead_score >= 0 AND lead_score <= 100),
    status ENUM('new', 'contacted', 'qualified', 'proposal', 'negotiation', 'won', 'lost', 'nurturing') DEFAULT 'new',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    
    -- Contact Information
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'Portugal',
    website VARCHAR(255),
    
    -- Business Information
    industry VARCHAR(100),
    company_size ENUM('1-10', '11-50', '51-200', '201-1000', '1000+'),
    annual_revenue DECIMAL(15,2),
    monthly_energy_budget DECIMAL(10,2),
    current_energy_provider VARCHAR(255),
    
    -- Energy Needs (JSON field for flexible data)
    energy_needs JSON,
    
    -- Lead Management
    assigned_to INT,
    created_by INT,
    notes TEXT,
    tags JSON, -- Store tags as JSON array
    
    -- Dates
    first_contact_date TIMESTAMP NULL,
    last_contact_date TIMESTAMP NULL,
    next_follow_up_date TIMESTAMP NULL,
    qualified_date TIMESTAMP NULL,
    converted_date TIMESTAMP NULL,
    
    -- Financial
    estimated_value DECIMAL(15,2),
    actual_value DECIMAL(15,2),
    
    -- Source Tracking
    contact_submission_id INT,
    utm_source VARCHAR(100),
    utm_medium VARCHAR(100),
    utm_campaign VARCHAR(100),
    referrer_url TEXT,
    
    -- Metadata
    metadata JSON DEFAULT (JSON_OBJECT()),
    ip_address VARCHAR(45),
    user_agent TEXT,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Key Constraints
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (contact_submission_id) REFERENCES contact_submissions(id) ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX idx_leads_email ON leads(email);
CREATE INDEX idx_leads_status ON leads(status);
CREATE INDEX idx_leads_priority ON leads(priority);
CREATE INDEX idx_leads_lead_source ON leads(lead_source);
CREATE INDEX idx_leads_lead_score ON leads(lead_score);
CREATE INDEX idx_leads_assigned_to ON leads(assigned_to);
CREATE INDEX idx_leads_created_by ON leads(created_by);
CREATE INDEX idx_leads_created_at ON leads(created_at);
CREATE INDEX idx_leads_first_contact_date ON leads(first_contact_date);
CREATE INDEX idx_leads_next_follow_up_date ON leads(next_follow_up_date);
CREATE INDEX idx_leads_contact_submission_id ON leads(contact_submission_id);
CREATE INDEX idx_leads_company_name ON leads(company_name);
CREATE INDEX idx_leads_industry ON leads(industry);

-- Create lead activities table for tracking interactions
CREATE TABLE IF NOT EXISTS lead_activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    lead_id INT NOT NULL,
    activity_type ENUM('call', 'email', 'meeting', 'note', 'task', 'proposal', 'quote', 'demo', 'follow_up') NOT NULL,
    subject VARCHAR(255),
    description TEXT,
    outcome VARCHAR(100),
    scheduled_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    duration_minutes INT,
    created_by INT,
    assigned_to INT,
    metadata JSON DEFAULT (JSON_OBJECT()),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Key Constraints
    FOREIGN KEY (lead_id) REFERENCES leads(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL
);

-- Create indexes for lead activities
CREATE INDEX idx_lead_activities_lead_id ON lead_activities(lead_id);
CREATE INDEX idx_lead_activities_type ON lead_activities(activity_type);
CREATE INDEX idx_lead_activities_created_by ON lead_activities(created_by);
CREATE INDEX idx_lead_activities_assigned_to ON lead_activities(assigned_to);
CREATE INDEX idx_lead_activities_created_at ON lead_activities(created_at);
CREATE INDEX idx_lead_activities_scheduled_at ON lead_activities(scheduled_at);

-- Create lead scoring rules table
CREATE TABLE IF NOT EXISTS lead_scoring_rules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    rule_name VARCHAR(100) NOT NULL,
    rule_type ENUM('demographic', 'behavioral', 'engagement', 'firmographic') NOT NULL,
    condition_field VARCHAR(100) NOT NULL,
    condition_operator ENUM('equals', 'contains', 'greater_than', 'less_than', 'in_range') NOT NULL,
    condition_value TEXT NOT NULL,
    score_change INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default lead scoring rules
INSERT INTO lead_scoring_rules (rule_name, rule_type, condition_field, condition_operator, condition_value, score_change, description) VALUES
('High Value Industry', 'firmographic', 'industry', 'contains', 'Manufacturing,Energy,Technology', 15, 'Bonus points for high-value industries'),
('Large Company Size', 'firmographic', 'company_size', 'contains', '201-1000,1000+', 10, 'Larger companies have higher potential'),
('High Energy Budget', 'demographic', 'monthly_energy_budget', 'greater_than', '5000', 20, 'High energy budget indicates serious interest'),
('Website Visit', 'behavioral', 'lead_source', 'equals', 'website', 5, 'Direct website visits show interest'),
('Referral Source', 'behavioral', 'lead_source', 'equals', 'referral', 15, 'Referrals are high-quality leads'),
('Contact Form Submission', 'engagement', 'contact_submission_id', 'greater_than', '0', 10, 'Active engagement through contact form');

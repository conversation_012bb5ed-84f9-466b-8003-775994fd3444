-- Migration: Create contact_actions table
-- Description: Track user interactions with contact methods (email, phone, WhatsApp, address)
-- Date: 2024-01-15

CREATE TABLE IF NOT EXISTS contact_actions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  action ENUM('email', 'phone', 'whatsapp', 'address') NOT NULL COMMENT 'Type of contact action taken by user',
  page VARCHAR(100) NOT NULL DEFAULT 'contact' COMMENT 'Page where the action was taken',
  user_agent TEXT COMMENT 'User agent string for device/browser identification',
  referrer VARCHAR(500) COMMENT 'Referrer URL if available',
  ip_address VARCHAR(45) COMMENT 'IP address of the user (IPv4 or IPv6)',
  session_id VARCHAR(255) COMMENT 'Session identifier if available',
  timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'When the action was taken',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Indexes for performance
  INDEX idx_contact_actions_action (action),
  INDEX idx_contact_actions_page (page),
  INDEX idx_contact_actions_timestamp (timestamp),
  INDEX idx_contact_actions_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Tracks user interactions with contact methods (email, phone, WhatsApp, address)';

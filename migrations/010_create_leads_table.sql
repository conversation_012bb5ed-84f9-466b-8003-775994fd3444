-- Leads Database Schema
-- Migration: 010_create_leads_table.sql
-- Creates a dedicated leads table for managing potential customers

-- Create leads table
CREATE TABLE IF NOT EXISTS leads (
    id SERIAL PRIMARY KEY,
    
    -- Basic Information
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(50),
    company_name VA<PERSON><PERSON><PERSON>(255),
    job_title VARCHAR(100),
    
    -- Lead Details
    lead_source VARCHAR(50) NOT NULL DEFAULT 'website',
    lead_score INTEGER DEFAULT 50 CHECK (lead_score >= 0 AND lead_score <= 100),
    status VARCHAR(20) DEFAULT 'new' CHECK (status IN ('new', 'contacted', 'qualified', 'proposal', 'negotiation', 'won', 'lost', 'nurturing')),
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    
    -- Contact Information
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'Portugal',
    website VARCHAR(255),
    
    -- Business Information
    industry VARCHAR(100),
    company_size VARCHAR(20) CHECK (company_size IN ('1-10', '11-50', '51-200', '201-1000', '1000+')),
    annual_revenue DECIMAL(15,2),
    monthly_energy_budget DECIMAL(10,2),
    current_energy_provider VARCHAR(255),
    
    -- Energy Needs (JSON field for flexible data)
    energy_needs JSON,
    
    -- Lead Management
    assigned_to INTEGER REFERENCES users(id),
    created_by INTEGER REFERENCES users(id),
    notes TEXT,
    tags TEXT[], -- PostgreSQL array for tags
    
    -- Dates
    first_contact_date TIMESTAMP,
    last_contact_date TIMESTAMP,
    next_follow_up_date TIMESTAMP,
    qualified_date TIMESTAMP,
    converted_date TIMESTAMP,
    
    -- Financial
    estimated_value DECIMAL(15,2),
    actual_value DECIMAL(15,2),
    
    -- Source Tracking
    contact_submission_id INTEGER REFERENCES contact_submissions(id),
    utm_source VARCHAR(100),
    utm_medium VARCHAR(100),
    utm_campaign VARCHAR(100),
    referrer_url TEXT,
    
    -- Metadata
    metadata JSON DEFAULT '{}',
    ip_address VARCHAR(45),
    user_agent TEXT,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_leads_email ON leads(email);
CREATE INDEX IF NOT EXISTS idx_leads_status ON leads(status);
CREATE INDEX IF NOT EXISTS idx_leads_priority ON leads(priority);
CREATE INDEX IF NOT EXISTS idx_leads_lead_source ON leads(lead_source);
CREATE INDEX IF NOT EXISTS idx_leads_lead_score ON leads(lead_score);
CREATE INDEX IF NOT EXISTS idx_leads_assigned_to ON leads(assigned_to);
CREATE INDEX IF NOT EXISTS idx_leads_created_by ON leads(created_by);
CREATE INDEX IF NOT EXISTS idx_leads_created_at ON leads(created_at);
CREATE INDEX IF NOT EXISTS idx_leads_first_contact_date ON leads(first_contact_date);
CREATE INDEX IF NOT EXISTS idx_leads_next_follow_up_date ON leads(next_follow_up_date);
CREATE INDEX IF NOT EXISTS idx_leads_contact_submission_id ON leads(contact_submission_id);
CREATE INDEX IF NOT EXISTS idx_leads_company_name ON leads(company_name);
CREATE INDEX IF NOT EXISTS idx_leads_industry ON leads(industry);

-- Create GIN index for tags array
CREATE INDEX IF NOT EXISTS idx_leads_tags ON leads USING GIN(tags);

-- Create GIN index for JSON fields
CREATE INDEX IF NOT EXISTS idx_leads_energy_needs ON leads USING GIN(energy_needs);
CREATE INDEX IF NOT EXISTS idx_leads_metadata ON leads USING GIN(metadata);

-- Create trigger for updated_at timestamp
CREATE TRIGGER update_leads_updated_at 
    BEFORE UPDATE ON leads 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create lead activities table for tracking interactions
CREATE TABLE IF NOT EXISTS lead_activities (
    id SERIAL PRIMARY KEY,
    lead_id INTEGER NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
    activity_type VARCHAR(50) NOT NULL CHECK (activity_type IN ('call', 'email', 'meeting', 'note', 'task', 'proposal', 'quote', 'demo', 'follow_up')),
    subject VARCHAR(255),
    description TEXT,
    outcome VARCHAR(100),
    scheduled_at TIMESTAMP,
    completed_at TIMESTAMP,
    duration_minutes INTEGER,
    created_by INTEGER REFERENCES users(id),
    assigned_to INTEGER REFERENCES users(id),
    metadata JSON DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for lead activities
CREATE INDEX IF NOT EXISTS idx_lead_activities_lead_id ON lead_activities(lead_id);
CREATE INDEX IF NOT EXISTS idx_lead_activities_type ON lead_activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_lead_activities_created_by ON lead_activities(created_by);
CREATE INDEX IF NOT EXISTS idx_lead_activities_assigned_to ON lead_activities(assigned_to);
CREATE INDEX IF NOT EXISTS idx_lead_activities_created_at ON lead_activities(created_at);
CREATE INDEX IF NOT EXISTS idx_lead_activities_scheduled_at ON lead_activities(scheduled_at);

-- Create trigger for lead activities updated_at
CREATE TRIGGER update_lead_activities_updated_at 
    BEFORE UPDATE ON lead_activities 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create lead scoring rules table
CREATE TABLE IF NOT EXISTS lead_scoring_rules (
    id SERIAL PRIMARY KEY,
    rule_name VARCHAR(100) NOT NULL,
    rule_type VARCHAR(50) NOT NULL CHECK (rule_type IN ('demographic', 'behavioral', 'engagement', 'firmographic')),
    condition_field VARCHAR(100) NOT NULL,
    condition_operator VARCHAR(20) NOT NULL CHECK (condition_operator IN ('equals', 'contains', 'greater_than', 'less_than', 'in_range')),
    condition_value TEXT NOT NULL,
    score_change INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create trigger for lead scoring rules updated_at
CREATE TRIGGER update_lead_scoring_rules_updated_at 
    BEFORE UPDATE ON lead_scoring_rules 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default lead scoring rules
INSERT INTO lead_scoring_rules (rule_name, rule_type, condition_field, condition_operator, condition_value, score_change, description) VALUES
('High Value Industry', 'firmographic', 'industry', 'in', 'Manufacturing,Energy,Technology', 15, 'Bonus points for high-value industries'),
('Large Company Size', 'firmographic', 'company_size', 'in', '201-1000,1000+', 10, 'Larger companies have higher potential'),
('High Energy Budget', 'demographic', 'monthly_energy_budget', 'greater_than', '5000', 20, 'High energy budget indicates serious interest'),
('Website Visit', 'behavioral', 'lead_source', 'equals', 'website', 5, 'Direct website visits show interest'),
('Referral Source', 'behavioral', 'lead_source', 'equals', 'referral', 15, 'Referrals are high-quality leads'),
('Contact Form Submission', 'engagement', 'contact_submission_id', 'greater_than', '0', 10, 'Active engagement through contact form');

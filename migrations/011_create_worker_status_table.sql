-- Create worker_status table to track email worker status
CREATE TABLE IF NOT EXISTS `worker_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `worker_type` varchar(50) NOT NULL DEFAULT 'email',
  `status` enum('starting', 'running', 'stopping', 'stopped', 'error') NOT NULL DEFAULT 'stopped',
  `pid` int(11) DEFAULT NULL,
  `started_at` datetime DEFAULT NULL,
  `stopped_at` datetime DEFAULT NULL,
  `last_heartbeat` datetime DEFAULT NULL,
  `uptime_seconds` int(11) DEFAULT 0,
  `processed_count` int(11) DEFAULT 0,
  `error_count` int(11) DEFAULT 0,
  `last_error` text DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_worker_type` (`worker_type`),
  KEY `idx_status` (`status`),
  <PERSON>EY `idx_last_heartbeat` (`last_heartbeat`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert initial record for email worker
INSERT INTO `worker_status` (`worker_type`, `status`, `metadata`) 
VALUES ('email', 'stopped', JSON_OBJECT('version', '1.0.0', 'location', 'backend/email-worker/'))
ON DUPLICATE KEY UPDATE 
  `status` = 'stopped',
  `stopped_at` = CURRENT_TIMESTAMP,
  `updated_at` = CURRENT_TIMESTAMP;

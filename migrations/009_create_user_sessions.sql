-- Migration: Create user_sessions table for server-side session management
-- This replaces the vulnerable localStorage-based session lock system

CREATE TABLE IF NOT EXISTS user_sessions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL UNIQUE COMMENT 'Unique session identifier (JWT jti claim or generated UUID)',
  user_id INT NOT NULL COMMENT 'Reference to the user who owns this session',
  is_locked BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether the session is currently locked due to inactivity',
  locked_at TIMESTAMP NULL COMMENT 'When the session was locked',
  last_activity TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Last recorded user activity timestamp',
  expires_at TIMESTAMP NOT NULL COMMENT 'When the session expires (based on JWT expiration)',
  ip_address VARCHAR(45) NULL COMMENT 'IP address of the session (IPv6 support)',
  user_agent TEXT NULL COMMENT 'User agent string for device identification',
  device_fingerprint VARCHAR(255) NULL COMMENT 'Device fingerprint for additional security',
  is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether the session is active (not terminated)',
  terminated_at TIMESTAMP NULL COMMENT 'When the session was terminated',
  terminated_by INT NULL COMMENT 'User ID who terminated the session (for admin actions)',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Foreign key constraints
  CONSTRAINT fk_user_sessions_user_id 
    FOREIGN KEY (user_id) REFERENCES users(id) 
    ON DELETE CASCADE ON UPDATE CASCADE,
    
  CONSTRAINT fk_user_sessions_terminated_by 
    FOREIGN KEY (terminated_by) REFERENCES users(id) 
    ON DELETE SET NULL ON UPDATE CASCADE,
    
  -- Indexes for performance
  INDEX idx_user_sessions_session_id (session_id),
  INDEX idx_user_sessions_user_id (user_id),
  INDEX idx_user_sessions_is_locked (is_locked),
  INDEX idx_user_sessions_is_active (is_active),
  INDEX idx_user_sessions_last_activity (last_activity),
  INDEX idx_user_sessions_expires_at (expires_at),
  INDEX idx_user_sessions_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add comments to the table
ALTER TABLE user_sessions COMMENT = 'Server-side user session management with lock state tracking';

-- Create a view for active sessions (optional, for easier querying)
CREATE OR REPLACE VIEW active_user_sessions AS
SELECT 
  us.*,
  u.name as user_name,
  u.email as user_email,
  u.role as user_role,
  CASE 
    WHEN us.expires_at < NOW() THEN 'expired'
    WHEN us.is_locked = TRUE THEN 'locked'
    WHEN us.is_active = FALSE THEN 'terminated'
    ELSE 'active'
  END as session_status,
  TIMESTAMPDIFF(MINUTE, us.last_activity, NOW()) as minutes_since_activity
FROM user_sessions us
JOIN users u ON us.user_id = u.id
WHERE us.is_active = TRUE
ORDER BY us.last_activity DESC;

-- Insert initial comment for migration tracking
INSERT INTO logs (level, message, meta, source, created_at) 
VALUES (
  'info', 
  'Database Migration: Created user_sessions table', 
  JSON_OBJECT(
    'migration', '009_create_user_sessions',
    'description', 'Server-side session management with lock state tracking',
    'tables_created', JSON_ARRAY('user_sessions'),
    'views_created', JSON_ARRAY('active_user_sessions')
  ),
  'migration_script',
  NOW()
) ON DUPLICATE KEY UPDATE id=id;

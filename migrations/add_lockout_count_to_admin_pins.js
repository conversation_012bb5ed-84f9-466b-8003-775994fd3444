#!/usr/bin/env node

/**
 * Migration: Add lockout_count field to admin_pins table
 * Adds progressive lockout tracking to PIN authentication
 */

const { sequelize } = require('../shared/config/database');

async function addLockoutCountField() {
  try {
    console.log('🔄 Starting migration: Add lockout_count to admin_pins...');

    // Check if the column already exists
    const [results] = await sequelize.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'admin_pins' 
      AND COLUMN_NAME = 'lockout_count'
    `);

    if (results.length > 0) {
      console.log('✅ Column lockout_count already exists in admin_pins table');
      return;
    }

    // Add the lockout_count column
    await sequelize.query(`
      ALTER TABLE admin_pins 
      ADD COLUMN lockout_count INT NOT NULL DEFAULT 0 
      COMMENT 'Number of times PIN has been locked (for progressive lockout)'
      AFTER locked_until
    `);

    console.log('✅ Successfully added lockout_count column to admin_pins table');

    // Update any existing locked PINs to have lockout_count = 1
    const [updateResult] = await sequelize.query(`
      UPDATE admin_pins 
      SET lockout_count = 1 
      WHERE locked_until IS NOT NULL 
      AND locked_until > NOW()
      AND lockout_count = 0
    `);

    if (updateResult.affectedRows > 0) {
      console.log(`✅ Updated ${updateResult.affectedRows} existing locked PINs with lockout_count = 1`);
    }

    console.log('🎉 Migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

async function rollbackLockoutCountField() {
  try {
    console.log('🔄 Rolling back migration: Remove lockout_count from admin_pins...');

    // Check if the column exists
    const [results] = await sequelize.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'admin_pins' 
      AND COLUMN_NAME = 'lockout_count'
    `);

    if (results.length === 0) {
      console.log('✅ Column lockout_count does not exist in admin_pins table');
      return;
    }

    // Remove the lockout_count column
    await sequelize.query(`
      ALTER TABLE admin_pins 
      DROP COLUMN lockout_count
    `);

    console.log('✅ Successfully removed lockout_count column from admin_pins table');
    console.log('🎉 Rollback completed successfully!');

  } catch (error) {
    console.error('❌ Rollback failed:', error);
    throw error;
  }
}

// Main execution
async function main() {
  const action = process.argv[2];

  try {
    if (action === 'rollback') {
      await rollbackLockoutCountField();
    } else {
      await addLockoutCountField();
    }
  } catch (error) {
    console.error('Migration script failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  addLockoutCountField,
  rollbackLockoutCountField,
};

# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_HOST=logs.hlenergy.pt
DB_PORT=3306
DB_USER=hlenes_admin
DB_PASSWORD=hlenergyadmin1
DB_NAME=api_v2
DB_NAME_TEST=hlenergy_test
DB_SSL=false




# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your_refresh_token_secret_here
JWT_REFRESH_EXPIRES_IN=30d

# CORS Configuration
FRONTEND_URL=http://localhost:5173

# Push Notifications Configuration
VAPID_PUBLIC_KEY=BDZb2zuovpIdk2ji3585xQvFKYGoqk8eZQPX-1n2s9jlSPsDKwHkp7HaEOgctB0QadD1BjR6e4Ml_MG2vkoN-CY
VAPID_PRIVATE_KEY=uaqd0ot1mR2UVJBroVf6VUqMQKpa4Xi1klyapqSi2JM
VAPID_SUBJECT=mailto:<EMAIL>


# Contact Form Notifications
CONTACT_NOTIFICATION_EMAIL=<EMAIL>

# System Alert Notifications
SYSTEM_ALERT_EMAIL=<EMAIL>
HEALTH_ALERT_EMAIL=<EMAIL>

# Note: Email worker is now standalone
# Location: backend/email-worker/
# Configure email worker separately in backend/email-worker/.env

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Admin User (for seeding)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123456
STAFF_EMAIL=<EMAIL>
STAFF_PASSWORD=staff123456

# Logging
LOG_LEVEL=info

# Deployment (Production)
DEPLOY_TIME=
DEPLOY_USER=
DEPLOYMENT_ID=
SERVER_INSTANCE=
DEPLOY_REGION=

# Google reCAPTCHA v3
RECAPTCHA_SECRET_KEY=6LcEPn4rAAAAAIBtjWWF-bJJg5-Vk_0p3GzXKRwy

# Frontend Configuration (for health checks)
FRONTEND_URL=http://localhost:5174

# Worker Configuration
WORKER_MAX_CONCURRENT_JOBS=5
WORKER_POLL_INTERVAL=5000
WORKER_MAX_RETRIES=3
WORKER_RETRY_DELAY=30000

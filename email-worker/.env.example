# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=hlenergy_db
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
# SMTP_PORT options:
# 587 - STARTTLS (recommended, most compatible)
# 465 - SSL/TLS (legacy but still supported)
# 25  - Plain SMTP (not recommended, often blocked)
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_NAME=HLenergy
SMTP_FROM_EMAIL=<EMAIL>

# Worker Configuration
EMAIL_BATCH_SIZE=10
EMAIL_PROCESSING_INTERVAL=30000
EMAIL_MAX_RETRIES=3
EMAIL_RETRY_DELAYS=5,15,60

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_MAX_SIZE=20m
LOG_FILE_MAX_FILES=14d
LOG_TO_DATABASE=true
LOG_TO_FILES=true

# Process Configuration
WORKER_NAME=hlenergy-email-worker
WORKER_PID_FILE=./worker.pid
WORKER_LOG_FILE=./logs/email-worker.log

# Health Check Configuration
HEALTH_CHECK_PORT=3002
HEALTH_CHECK_ENABLED=true

# Cleanup Configuration
CLEANUP_OLD_EMAILS_DAYS=30
CLEANUP_INTERVAL_HOURS=24

# Environment
NODE_ENV=production

# HLenergy Standalone Email Worker Dockerfile

FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    bash \
    curl \
    tzdata

# Create app user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S emailworker -u 1001

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && \
    npm cache clean --force

# Copy application code
COPY . .

# Create logs directory
RUN mkdir -p logs && \
    chown -R emailworker:nodejs /app

# Switch to non-root user
USER emailworker

# Expose health check port (if enabled)
EXPOSE 3002

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node scripts/health-check.js || exit 1

# Default command
CMD ["npm", "start"]

# Labels
LABEL maintainer="HLenergy Team"
LABEL description="Standalone email worker for HLenergy backend"
LABEL version="1.0.0"

#!/bin/bash

# HLenergy Email Worker Monitor Script
# This script monitors the email worker and provides real-time status

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
WORKER_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="${WORKER_DIR}/worker.pid"
LOG_FILE="${WORKER_DIR}/logs/email-worker.log"
REFRESH_INTERVAL=5

# Functions
print_header() {
    clear
    echo -e "${BLUE}📧 HLenergy Email Worker Monitor${NC}"
    echo -e "${BLUE}=================================${NC}"
    echo -e "${CYAN}Press Ctrl+C to exit${NC}"
    echo ""
}

get_worker_status() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE" 2>/dev/null || echo "")
        if [ -n "$PID" ] && kill -0 "$PID" 2>/dev/null; then
            echo -e "${GREEN}✅ RUNNING${NC} (PID: $PID)"
            return 0
        else
            echo -e "${RED}❌ STOPPED${NC} (stale PID file)"
            return 1
        fi
    else
        echo -e "${RED}❌ STOPPED${NC} (no PID file)"
        return 1
    fi
}

get_uptime() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE" 2>/dev/null || echo "")
        if [ -n "$PID" ] && kill -0 "$PID" 2>/dev/null; then
            # Get process start time (this is system-dependent)
            if command -v ps &> /dev/null; then
                # Try to get process start time
                START_TIME=$(ps -o lstart= -p "$PID" 2>/dev/null || echo "Unknown")
                echo "$START_TIME"
            else
                echo "Unknown"
            fi
        else
            echo "Not running"
        fi
    else
        echo "Not running"
    fi
}

get_memory_usage() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE" 2>/dev/null || echo "")
        if [ -n "$PID" ] && kill -0 "$PID" 2>/dev/null; then
            if command -v ps &> /dev/null; then
                # Get memory usage in KB
                MEM_KB=$(ps -o rss= -p "$PID" 2>/dev/null || echo "0")
                MEM_MB=$((MEM_KB / 1024))
                echo "${MEM_MB}MB"
            else
                echo "Unknown"
            fi
        else
            echo "N/A"
        fi
    else
        echo "N/A"
    fi
}

get_cpu_usage() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE" 2>/dev/null || echo "")
        if [ -n "$PID" ] && kill -0 "$PID" 2>/dev/null; then
            if command -v ps &> /dev/null; then
                # Get CPU usage percentage
                CPU=$(ps -o pcpu= -p "$PID" 2>/dev/null || echo "0.0")
                echo "${CPU}%"
            else
                echo "Unknown"
            fi
        else
            echo "N/A"
        fi
    else
        echo "N/A"
    fi
}

get_log_tail() {
    if [ -f "$LOG_FILE" ]; then
        tail -n 10 "$LOG_FILE" 2>/dev/null || echo "No recent logs"
    else
        echo "Log file not found"
    fi
}

get_log_stats() {
    if [ -f "$LOG_FILE" ]; then
        local total_lines=$(wc -l < "$LOG_FILE" 2>/dev/null || echo "0")
        local file_size=$(du -h "$LOG_FILE" 2>/dev/null | cut -f1 || echo "0")
        echo "Lines: $total_lines, Size: $file_size"
    else
        echo "Log file not found"
    fi
}

check_health() {
    cd "$WORKER_DIR"
    if npm run health --silent 2>/dev/null; then
        echo -e "${GREEN}✅ HEALTHY${NC}"
    else
        echo -e "${RED}❌ UNHEALTHY${NC}"
    fi
}

show_queue_stats() {
    # This would require connecting to the database
    # For now, we'll show a placeholder
    echo "Queue stats require database connection"
    echo "Run 'npm run health' for detailed status"
}

monitor_loop() {
    while true; do
        print_header
        
        # Worker Status
        echo -e "${YELLOW}📊 Worker Status:${NC}"
        echo "   Status: $(get_worker_status)"
        echo "   Uptime: $(get_uptime)"
        echo "   Memory: $(get_memory_usage)"
        echo "   CPU: $(get_cpu_usage)"
        echo ""
        
        # Health Check
        echo -e "${YELLOW}🏥 Health Status:${NC}"
        echo "   Health: $(check_health)"
        echo ""
        
        # Log Information
        echo -e "${YELLOW}📋 Log Information:${NC}"
        echo "   Stats: $(get_log_stats)"
        echo "   File: $LOG_FILE"
        echo ""
        
        # Recent Logs
        echo -e "${YELLOW}📝 Recent Logs (last 10 lines):${NC}"
        echo "$(get_log_tail)" | sed 's/^/   /'
        echo ""
        
        # Queue Stats
        echo -e "${YELLOW}📬 Queue Status:${NC}"
        echo "   $(show_queue_stats)" | sed 's/^/   /'
        echo ""
        
        # Commands
        echo -e "${CYAN}💡 Available Commands:${NC}"
        echo "   npm run status    - Detailed status"
        echo "   npm run health    - Health check"
        echo "   npm run logs      - View logs"
        echo "   npm run stop      - Stop worker"
        echo ""
        
        echo -e "${CYAN}🕐 Last updated: $(date)${NC}"
        echo -e "${CYAN}📊 Refreshing every ${REFRESH_INTERVAL} seconds...${NC}"
        
        sleep $REFRESH_INTERVAL
    done
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --interval, -i SECONDS  Set refresh interval (default: 5)"
        echo "  --once, -o              Show status once and exit"
        echo "  --help, -h              Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0                      # Start monitoring with 5s refresh"
        echo "  $0 --interval 10        # Monitor with 10s refresh"
        echo "  $0 --once               # Show status once"
        exit 0
        ;;
    --interval|-i)
        if [ -n "${2:-}" ] && [ "$2" -gt 0 ] 2>/dev/null; then
            REFRESH_INTERVAL="$2"
            shift 2
        else
            echo -e "${RED}❌ Invalid interval: ${2:-}${NC}"
            exit 1
        fi
        ;;
    --once|-o)
        print_header
        get_worker_status > /dev/null
        npm run status 2>/dev/null || echo "Status check failed"
        exit 0
        ;;
    "")
        # No arguments, start monitoring
        ;;
    *)
        echo -e "${RED}❌ Unknown option: $1${NC}"
        echo "Use --help for usage information"
        exit 1
        ;;
esac

# Trap Ctrl+C to exit gracefully
trap 'echo -e "\n${YELLOW}👋 Monitoring stopped${NC}"; exit 0' INT

# Start monitoring
monitor_loop

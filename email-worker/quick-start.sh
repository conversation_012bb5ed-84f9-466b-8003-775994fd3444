#!/bin/bash

# HLenergy Email Worker Quick Start Script
# This script helps you get the email worker running quickly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
WORKER_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

print_header() {
    echo -e "${BLUE}🚀 HLenergy Email Worker Quick Start${NC}"
    echo -e "${BLUE}====================================${NC}"
    echo ""
}

print_step() {
    echo -e "${CYAN}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

check_prerequisites() {
    print_step "Step 1: Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        echo "Please install Node.js 18+ from https://nodejs.org/"
        exit 1
    fi
    
    local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 18 ]; then
        print_error "Node.js version 18+ required (found: $(node --version))"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
    echo "   Node.js: $(node --version)"
    echo "   npm: $(npm --version)"
    echo ""
}

install_dependencies() {
    print_step "Step 2: Installing dependencies..."
    
    cd "$WORKER_DIR"
    
    if [ ! -f "package.json" ]; then
        print_error "package.json not found"
        exit 1
    fi
    
    if npm install; then
        print_success "Dependencies installed successfully"
    else
        print_error "Failed to install dependencies"
        exit 1
    fi
    echo ""
}

setup_configuration() {
    print_step "Step 3: Setting up configuration..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_success "Created .env file from .env.example"
            print_warning "Please edit .env file with your configuration:"
            echo ""
            echo "   Required settings:"
            echo "   - Database connection (DB_HOST, DB_USER, DB_PASSWORD, DB_NAME)"
            echo "   - SMTP settings (SMTP_HOST, SMTP_USER, SMTP_PASSWORD)"
            echo ""
            echo "   Edit command: nano .env"
            echo ""
            read -p "Press Enter after configuring .env file..."
        else
            print_error ".env.example file not found"
            exit 1
        fi
    else
        print_success ".env file already exists"
    fi
    echo ""
}

test_configuration() {
    print_step "Step 4: Testing configuration..."

    if npm test; then
        print_success "Configuration test passed"
    else
        print_error "Configuration test failed"
        echo ""
        echo "Common issues:"
        echo "- Check database connection settings"
        echo "- Verify SMTP credentials"
        echo "- Ensure database server is running"
        echo "- Check firewall settings"
        echo ""
        read -p "Fix the issues and press Enter to retry, or Ctrl+C to exit..."
        test_configuration
    fi
    echo ""
}

setup_database_logging() {
    print_step "Step 5: Setting up database logging..."

    # Check if database logging is enabled
    if grep -q "LOG_TO_DATABASE=true" .env 2>/dev/null; then
        echo "Database logging is enabled. Creating worker_logs table..."

        if npm run migrate; then
            print_success "Database logging table created successfully"
        else
            print_warning "Failed to create database logging table"
            echo "You can create it manually later with: npm run migrate"
        fi
    else
        echo "Database logging is disabled (LOG_TO_DATABASE=false or not set)"
        echo "Logs will be written to files only"
        print_success "File logging configured"
    fi
    echo ""
}

choose_startup_method() {
    print_step "Step 6: Choose startup method..."
    echo ""
    echo "Available startup methods:"
    echo "1) Foreground (npm start) - Good for testing"
    echo "2) Background (./start.sh --background) - Runs in background"
    echo "3) Development (npm run dev) - Auto-restart on changes"
    echo "4) PM2 (./start.sh --pm2) - Production process manager"
    echo ""
    
    while true; do
        read -p "Choose method (1-4): " choice
        case $choice in
            1)
                start_foreground
                break
                ;;
            2)
                start_background
                break
                ;;
            3)
                start_development
                break
                ;;
            4)
                start_pm2
                break
                ;;
            *)
                echo "Please choose 1, 2, 3, or 4"
                ;;
        esac
    done
}

start_foreground() {
    print_success "Starting worker in foreground mode..."
    echo ""
    echo "The worker will run in the foreground. Press Ctrl+C to stop."
    echo "Monitor: Open another terminal and run 'npm run status'"
    echo ""
    read -p "Press Enter to start..."
    npm start
}

start_background() {
    print_success "Starting worker in background mode..."
    ./start.sh --background
    echo ""
    show_monitoring_commands
}

start_development() {
    print_success "Starting worker in development mode..."
    echo ""
    echo "The worker will auto-restart on file changes. Press Ctrl+C to stop."
    echo ""
    read -p "Press Enter to start..."
    npm run dev
}

start_pm2() {
    print_success "Starting worker with PM2..."
    
    # Check if PM2 is installed
    if ! command -v pm2 &> /dev/null; then
        print_warning "PM2 is not installed globally"
        echo ""
        read -p "Install PM2 globally? (y/n): " install_pm2
        if [ "$install_pm2" = "y" ] || [ "$install_pm2" = "Y" ]; then
            npm install -g pm2
            print_success "PM2 installed"
        else
            print_error "PM2 is required for this startup method"
            choose_startup_method
            return
        fi
    fi
    
    ./start.sh --pm2
    echo ""
    show_pm2_commands
}

show_monitoring_commands() {
    echo -e "${CYAN}📊 Monitoring Commands:${NC}"
    echo "   npm run status    - Check worker status"
    echo "   npm run health    - Health check"
    echo "   npm run logs      - View logs"
    echo "   npm run stop      - Stop worker"
    echo "   ./monitor.sh      - Real-time monitoring"
    echo ""
}

show_pm2_commands() {
    echo -e "${CYAN}📊 PM2 Commands:${NC}"
    echo "   pm2 status                    - Check status"
    echo "   pm2 logs hlenergy-email-worker - View logs"
    echo "   pm2 restart hlenergy-email-worker - Restart"
    echo "   pm2 stop hlenergy-email-worker    - Stop"
    echo "   pm2 monit                     - Real-time monitoring"
    echo ""
}

show_next_steps() {
    echo -e "${CYAN}🎉 Quick Start Complete!${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Monitor the worker using the commands shown above"
    echo "2. Check the main backend and disable integrated worker:"
    echo "   Set DISABLE_INTEGRATED_EMAIL_WORKER=true in main backend .env"
    echo "3. Test email sending by submitting a contact form"
    echo "4. Set up monitoring and alerting for production use"
    echo ""
    echo "Documentation:"
    echo "   README.md      - Complete documentation"
    echo "   DEPLOYMENT.md  - Production deployment guide"
    echo ""
    echo "Support:"
    echo "   npm run health - Diagnose issues"
    echo "   npm test       - Test connections"
    echo ""
}

# Main execution
print_header

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -f "worker.js" ]; then
    print_error "Please run this script from the email-worker directory"
    exit 1
fi

# Run setup steps
check_prerequisites
install_dependencies
setup_configuration
test_configuration
setup_database_logging
choose_startup_method

# Show next steps (only for background/PM2 modes)
if [ "$choice" = "2" ] || [ "$choice" = "4" ]; then
    show_next_steps
fi

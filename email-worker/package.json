{"name": "hlenergy-email-worker", "version": "1.0.0", "description": "Standalone email worker for HLenergy backend", "main": "worker.js", "scripts": {"start": "node worker.js", "dev": "nodemon worker.js", "stop": "node scripts/stop.js", "status": "node scripts/status.js", "restart": "npm run stop && npm start", "logs": "tail -f logs/email-worker.log", "logs:errors": "tail -f logs/email-worker-error.log", "health": "node scripts/health-check.js", "cleanup": "node scripts/cleanup.js", "test": "node scripts/test-connection.js", "migrate": "node scripts/create-worker-logs-table.js", "migrate:info": "node scripts/create-worker-logs-table.js --info", "test:logging": "node scripts/test-database-logging.js", "debug:smtp": "node scripts/debug-smtp.js", "debug:smtp:send": "node scripts/debug-smtp.js --send-test", "test:ports": "node scripts/test-smtp-ports.js", "deploy:prod": "./deploy-production.sh", "monitor": "node scripts/production-monitor.js", "pm2:start": "pm2 start ecosystem.config.js --env production", "pm2:stop": "pm2 stop hlenergy-email-worker", "pm2:restart": "pm2 restart hlenergy-email-worker", "pm2:delete": "pm2 delete hlenergy-email-worker", "pm2:logs": "pm2 logs hlenergy-email-worker", "pm2:monit": "pm2 monit"}, "keywords": ["email", "worker", "queue", "background", "hlenergy"], "author": "HLenergy Team", "license": "ISC", "dependencies": {"dotenv": "^17.2.0", "mysql2": "^3.14.1", "nodemailer": "^7.0.5", "sequelize": "^6.37.7", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"nodemon": "^3.1.10"}, "engines": {"node": ">=18.0.0"}}
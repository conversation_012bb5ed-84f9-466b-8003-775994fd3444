# HLenergy Standalone Email Worker

A standalone email worker service for processing email queues independently from the main HLenergy backend application.

## Features

- **Independent Operation**: Runs separately from the main backend
- **Queue Processing**: Processes emails from the database queue
- **Retry Logic**: Automatic retry with exponential backoff
- **Health Monitoring**: Built-in health checks and status monitoring
- **Logging**: Comprehensive logging with file and database options
- **Process Management**: Easy start/stop/restart operations
- **Cleanup**: Automatic cleanup of old processed emails and logs

## Quick Start

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the Worker**
   ```bash
   npm start
   ```

## Configuration

Copy `.env.example` to `.env` and configure:

- **Database**: Connection details for the HLenergy database
- **SMTP**: Email server configuration
- **Worker**: Batch size, processing intervals, retry settings
- **Logging**: Log levels and file rotation settings

## Scripts

- `npm start` - Start the worker
- `npm run dev` - Start with auto-restart (development)
- `npm run stop` - Stop the worker
- `npm run status` - Check worker status
- `npm run restart` - Restart the worker
- `npm run health` - Health check
- `npm run logs` - View logs
- `npm run cleanup` - Manual cleanup of old emails
- `npm run test` - Test database connection
- `npm run migrate` - Create worker_logs table for database logging

## Monitoring

The worker provides several monitoring endpoints and scripts:

- Health check endpoint (if enabled)
- Status monitoring script
- Log file monitoring
- Process ID tracking

## Deployment

### Production Deployment

1. Install dependencies: `npm install --production`
2. Configure environment variables
3. Start with process manager (PM2 recommended)

### Using PM2

```bash
# Install PM2 globally
npm install -g pm2

# Start worker with PM2
pm2 start ecosystem.config.js

# Monitor
pm2 status
pm2 logs hlenergy-email-worker
```

## Architecture

The standalone worker:
- Connects to the same database as the main backend
- Processes emails from the `email_queue` table
- Uses the same email models and services
- Operates independently with its own logging and monitoring

## Detailed Setup Guide

### 1. Prerequisites

- Node.js 18+ installed
- Access to the HLenergy database
- SMTP server credentials
- Basic understanding of process management

### 2. Installation

```bash
# Navigate to the email worker directory
cd backend/email-worker

# Install dependencies
npm install

# Copy and configure environment
cp .env.example .env
# Edit .env with your configuration
```

### 3. Configuration

Edit the `.env` file with your settings:

```bash
# Database - Use the same as your main backend
DB_HOST=localhost
DB_PORT=3306
DB_NAME=hlenergy_db
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# Email - Configure your SMTP server
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_NAME=HLenergy
SMTP_FROM_EMAIL=<EMAIL>

# Worker Settings - Adjust based on your needs
EMAIL_BATCH_SIZE=10              # Emails processed per batch
EMAIL_PROCESSING_INTERVAL=30000  # Interval in milliseconds (30s)
EMAIL_MAX_RETRIES=3              # Maximum retry attempts
EMAIL_RETRY_DELAYS=5,15,60       # Retry delays in minutes

# Logging
LOG_LEVEL=info                   # debug, info, warn, error
LOG_FILE_MAX_SIZE=20m           # Max log file size
LOG_FILE_MAX_FILES=14d          # Log retention period

# Health Check (optional)
HEALTH_CHECK_PORT=3002
HEALTH_CHECK_ENABLED=true

# Cleanup
CLEANUP_OLD_EMAILS_DAYS=30      # Days to keep processed emails
CLEANUP_INTERVAL_HOURS=24       # Cleanup frequency

# Environment
NODE_ENV=production
```

### 4. Testing the Setup

Before starting the worker, test your configuration:

```bash
# Test database and email connections
npm test

# Check worker health
npm run health

# View current status
npm run status
```

### 5. Starting the Worker

Choose your preferred method:

#### Option A: Direct Start (Foreground)
```bash
npm start
```

#### Option B: Background Process
```bash
./start.sh --background
```

#### Option C: Development Mode (Auto-restart)
```bash
npm run dev
# or
./start.sh --dev
```

#### Option D: PM2 Process Manager (Recommended for Production)
```bash
# Install PM2 globally
npm install -g pm2

# Start with PM2
./start.sh --pm2
# or
pm2 start ecosystem.config.js

# Monitor
pm2 status
pm2 logs hlenergy-email-worker
```

### 6. Monitoring

#### Real-time Monitoring
```bash
# Interactive monitor
./monitor.sh

# Check status once
npm run status

# Health check
npm run health

# View logs
npm run logs
npm run logs:errors
```

#### PM2 Monitoring (if using PM2)
```bash
pm2 status                    # Process status
pm2 logs hlenergy-email-worker # View logs
pm2 monit                     # Real-time monitoring
pm2 restart hlenergy-email-worker # Restart
pm2 stop hlenergy-email-worker    # Stop
```

### 7. Maintenance

#### Manual Cleanup
```bash
# Clean up old emails (with confirmation)
npm run cleanup -- --force

# Dry run (preview what would be deleted)
npm run cleanup -- --dry-run
```

#### Log Management
```bash
# View recent logs
npm run logs

# View error logs only
npm run logs:errors

# Log files are automatically rotated
# Location: ./logs/
```

#### Stopping the Worker
```bash
# Graceful stop
npm run stop

# Or if using PM2
pm2 stop hlenergy-email-worker
```

## Deployment Scenarios

### Scenario 1: Same Server as Backend

1. Keep the integrated worker disabled in the main backend:
   ```bash
   # In main backend .env
   DISABLE_INTEGRATED_EMAIL_WORKER=true
   ```

2. Run the standalone worker alongside the main backend:
   ```bash
   cd backend/email-worker
   ./start.sh --pm2
   ```

### Scenario 2: Separate Server

1. Copy the email worker to a separate server
2. Configure database connection to point to the main database
3. Ensure network connectivity between servers
4. Start the worker on the separate server

### Scenario 3: Docker Deployment

#### Single Container
```bash
# Build the image
docker build -t hlenergy-email-worker .

# Run the container
docker run -d \
  --name hlenergy-email-worker \
  --env-file .env \
  -v $(pwd)/logs:/app/logs \
  hlenergy-email-worker
```

#### Docker Compose
```bash
# Start with docker-compose
docker-compose up -d

# View logs
docker-compose logs -f email-worker

# Stop
docker-compose down
```

### Scenario 4: Kubernetes Deployment

Create Kubernetes manifests for:
- ConfigMap for environment variables
- Secret for sensitive data (DB password, SMTP password)
- Deployment for the worker
- Service for health checks (if needed)

## Troubleshooting

### Common Issues

#### 1. Database Connection Failed
```bash
# Check database connectivity
npm test

# Verify credentials in .env
# Ensure database server is accessible
# Check firewall settings
```

#### 2. Email Service Connection Failed
```bash
# Test SMTP settings
npm test

# Common issues:
# - Wrong SMTP server/port
# - Invalid credentials
# - 2FA enabled (use app password)
# - Firewall blocking SMTP ports
```

#### 3. Worker Not Processing Emails
```bash
# Check worker status
npm run status

# Check health
npm run health

# View logs for errors
npm run logs:errors

# Common causes:
# - Worker not running
# - Database connection lost
# - No emails in queue
# - All emails already processed
```

#### 4. High Memory Usage
```bash
# Monitor memory usage
./monitor.sh

# Reduce batch size in .env:
EMAIL_BATCH_SIZE=5

# Restart worker
npm run restart
```

#### 5. Stuck Emails
```bash
# Check for stuck emails in processing state
npm run health

# The worker automatically resets stuck emails after 10 minutes
# Or restart the worker to reset them immediately
npm run restart
```

### Log Analysis

#### Log Locations
- Main log: `./logs/email-worker-YYYY-MM-DD.log`
- Error log: `./logs/email-worker-error-YYYY-MM-DD.log`
- PM2 logs: `~/.pm2/logs/`

#### Important Log Messages
- `Email sent successfully` - Normal operation
- `Email scheduled for retry` - Temporary failure, will retry
- `Email failed permanently` - Max retries reached
- `Database connection established` - Startup success
- `Email service connection verified` - SMTP connection OK

## Performance Tuning

### Batch Size
- **Small batches (1-5)**: Lower memory usage, more database queries
- **Large batches (20-50)**: Higher memory usage, fewer database queries
- **Recommended**: 10-20 for most use cases

### Processing Interval
- **Frequent (10-30s)**: Lower latency, higher CPU usage
- **Infrequent (60-300s)**: Higher latency, lower CPU usage
- **Recommended**: 30-60 seconds for most use cases

### Retry Strategy
- **Aggressive (1,2,5 min)**: Faster recovery, more load
- **Conservative (5,15,60 min)**: Slower recovery, less load
- **Recommended**: 5,15,60 minutes

## Security Considerations

1. **Environment Variables**: Store sensitive data in `.env`, never commit to version control
2. **Database Access**: Use dedicated database user with minimal permissions
3. **SMTP Credentials**: Use app passwords, not main account passwords
4. **File Permissions**: Ensure log files are not world-readable
5. **Network Security**: Restrict database and SMTP access to necessary IPs

## Integration with Main Backend

### Disabling Integrated Worker

In your main backend `.env`:
```bash
DISABLE_INTEGRATED_EMAIL_WORKER=true
```

This prevents the main backend from starting its own email worker, allowing the standalone worker to handle all email processing.

### API Endpoints

The main backend provides these endpoints for email queue management:
- `GET /api/v1/email-queue/status` - Queue statistics
- `GET /api/v1/email-queue/emails` - List queued emails
- `POST /api/v1/email-queue/emails/:id/retry` - Retry failed email
- `POST /api/v1/email-queue/emails/:id/cancel` - Cancel pending email

### Monitoring Integration

The standalone worker can be monitored through:
- Health check endpoint (if enabled)
- Log file analysis
- Database queue statistics
- Process monitoring (PM2, systemd, etc.)

## Scaling

### Horizontal Scaling
- Run multiple worker instances on different servers
- Each worker will process different emails from the queue
- Database handles coordination automatically

### Vertical Scaling
- Increase batch size for higher throughput
- Decrease processing interval for lower latency
- Add more CPU/memory to the server

### Load Balancing
- Use a load balancer for health check endpoints
- Distribute workers across multiple servers
- Monitor queue depth to scale workers up/down

#!/bin/bash

# HLenergy Email Worker Start Script
# This script provides various ways to start the email worker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
WORKER_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKER_NAME="hlenergy-email-worker"
PID_FILE="${WORKER_DIR}/worker.pid"
LOG_FILE="${WORKER_DIR}/logs/email-worker.log"

# Functions
print_header() {
    echo -e "${BLUE}📧 HLenergy Email Worker Starter${NC}"
    echo -e "${BLUE}===================================${NC}"
}

print_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --dev, -d          Start in development mode with auto-restart"
    echo "  --pm2, -p          Start with PM2 process manager"
    echo "  --background, -b   Start in background (daemon mode)"
    echo "  --foreground, -f   Start in foreground (default)"
    echo "  --test, -t         Test connections before starting"
    echo "  --help, -h         Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                 # Start in foreground"
    echo "  $0 --dev          # Start in development mode"
    echo "  $0 --pm2          # Start with PM2"
    echo "  $0 --background   # Start in background"
    echo "  $0 --test         # Test connections first"
}

check_dependencies() {
    echo -e "${YELLOW}🔍 Checking dependencies...${NC}"
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js is not installed${NC}"
        exit 1
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm is not installed${NC}"
        exit 1
    fi
    
    # Check if package.json exists
    if [ ! -f "${WORKER_DIR}/package.json" ]; then
        echo -e "${RED}❌ package.json not found${NC}"
        exit 1
    fi
    
    # Check if node_modules exists
    if [ ! -d "${WORKER_DIR}/node_modules" ]; then
        echo -e "${YELLOW}⚠️  node_modules not found, installing dependencies...${NC}"
        cd "${WORKER_DIR}"
        npm install
    fi
    
    # Check if .env file exists
    if [ ! -f "${WORKER_DIR}/.env" ]; then
        echo -e "${YELLOW}⚠️  .env file not found${NC}"
        if [ -f "${WORKER_DIR}/.env.example" ]; then
            echo -e "${YELLOW}📋 Please copy .env.example to .env and configure it:${NC}"
            echo "   cp .env.example .env"
            echo "   # Edit .env with your configuration"
        fi
        exit 1
    fi
    
    echo -e "${GREEN}✅ Dependencies check passed${NC}"
}

check_if_running() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if kill -0 "$PID" 2>/dev/null; then
            echo -e "${YELLOW}⚠️  Worker is already running (PID: $PID)${NC}"
            echo "   Use 'npm run stop' to stop it first"
            exit 1
        else
            echo -e "${YELLOW}🧹 Removing stale PID file${NC}"
            rm -f "$PID_FILE"
        fi
    fi
}

test_connections() {
    echo -e "${YELLOW}🧪 Testing connections...${NC}"
    cd "${WORKER_DIR}"
    if npm test; then
        echo -e "${GREEN}✅ Connection tests passed${NC}"
    else
        echo -e "${RED}❌ Connection tests failed${NC}"
        exit 1
    fi
}

create_logs_dir() {
    if [ ! -d "${WORKER_DIR}/logs" ]; then
        mkdir -p "${WORKER_DIR}/logs"
        echo -e "${GREEN}📁 Created logs directory${NC}"
    fi
}

start_foreground() {
    echo -e "${GREEN}🚀 Starting worker in foreground mode...${NC}"
    cd "${WORKER_DIR}"
    exec npm start
}

start_background() {
    echo -e "${GREEN}🚀 Starting worker in background mode...${NC}"
    cd "${WORKER_DIR}"
    nohup npm start > "$LOG_FILE" 2>&1 &
    echo $! > "$PID_FILE"
    echo -e "${GREEN}✅ Worker started in background (PID: $(cat $PID_FILE))${NC}"
    echo "   View logs: tail -f $LOG_FILE"
    echo "   Stop worker: npm run stop"
}

start_dev() {
    echo -e "${GREEN}🚀 Starting worker in development mode...${NC}"
    cd "${WORKER_DIR}"
    exec npm run dev
}

start_pm2() {
    echo -e "${GREEN}🚀 Starting worker with PM2...${NC}"
    
    # Check if PM2 is installed
    if ! command -v pm2 &> /dev/null; then
        echo -e "${YELLOW}⚠️  PM2 is not installed globally${NC}"
        echo "   Install PM2: npm install -g pm2"
        exit 1
    fi
    
    cd "${WORKER_DIR}"
    
    # Stop existing PM2 process if running
    pm2 stop "$WORKER_NAME" 2>/dev/null || true
    pm2 delete "$WORKER_NAME" 2>/dev/null || true
    
    # Start with PM2
    pm2 start ecosystem.config.js
    pm2 save
    
    echo -e "${GREEN}✅ Worker started with PM2${NC}"
    echo "   Status: pm2 status"
    echo "   Logs: pm2 logs $WORKER_NAME"
    echo "   Stop: pm2 stop $WORKER_NAME"
    echo "   Restart: pm2 restart $WORKER_NAME"
}

# Main execution
print_header

# Parse command line arguments
MODE="foreground"
RUN_TESTS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --dev|-d)
            MODE="dev"
            shift
            ;;
        --pm2|-p)
            MODE="pm2"
            shift
            ;;
        --background|-b)
            MODE="background"
            shift
            ;;
        --foreground|-f)
            MODE="foreground"
            shift
            ;;
        --test|-t)
            RUN_TESTS=true
            shift
            ;;
        --help|-h)
            print_usage
            exit 0
            ;;
        *)
            echo -e "${RED}❌ Unknown option: $1${NC}"
            print_usage
            exit 1
            ;;
    esac
done

# Run checks
check_dependencies
check_if_running
create_logs_dir

# Run tests if requested
if [ "$RUN_TESTS" = true ]; then
    test_connections
fi

# Start worker based on mode
case $MODE in
    "foreground")
        start_foreground
        ;;
    "background")
        start_background
        ;;
    "dev")
        start_dev
        ;;
    "pm2")
        start_pm2
        ;;
    *)
        echo -e "${RED}❌ Unknown mode: $MODE${NC}"
        exit 1
        ;;
esac

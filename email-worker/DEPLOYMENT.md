# HLenergy Email Worker Deployment Guide

This guide covers different deployment scenarios for the standalone email worker.

## Table of Contents

1. [Production Deployment](#production-deployment)
2. [Docker Deployment](#docker-deployment)
3. [PM2 Deployment](#pm2-deployment)
4. [Systemd Service](#systemd-service)
5. [Kubernetes Deployment](#kubernetes-deployment)
6. [Monitoring & Alerting](#monitoring--alerting)
7. [Backup & Recovery](#backup--recovery)

## Production Deployment

### Prerequisites

- Node.js 18+ installed
- PM2 or systemd for process management
- Nginx (optional, for health check proxy)
- Monitoring solution (Prometheus, Grafana, etc.)

### Step 1: Server Setup

```bash
# Create dedicated user
sudo useradd -m -s /bin/bash emailworker
sudo usermod -aG sudo emailworker

# Switch to worker user
sudo su - emailworker

# Create application directory
mkdir -p /home/<USER>/hlenergy-email-worker
cd /home/<USER>/hlenergy-email-worker
```

### Step 2: Application Deployment

```bash
# Copy application files
# (Use your preferred deployment method: git, rsync, CI/CD, etc.)
git clone <repository-url> .
cd backend/email-worker

# Install dependencies
npm ci --production

# Configure environment
cp .env.example .env
# Edit .env with production values
```

### Step 3: Security Configuration

```bash
# Set proper file permissions
chmod 600 .env
chmod +x *.sh
chmod +x scripts/*.js

# Create logs directory
mkdir -p logs
chmod 755 logs
```

### Step 4: Test Configuration

```bash
# Test connections
npm test

# Verify health check
npm run health

# Check status
npm run status
```

## Docker Deployment

### Single Container

```bash
# Build image
docker build -t hlenergy-email-worker:latest .

# Create network (if needed)
docker network create hlenergy-network

# Run container
docker run -d \
  --name hlenergy-email-worker \
  --network hlenergy-network \
  --restart unless-stopped \
  --env-file .env \
  -v $(pwd)/logs:/app/logs \
  -p 3002:3002 \
  hlenergy-email-worker:latest

# Check logs
docker logs -f hlenergy-email-worker

# Health check
docker exec hlenergy-email-worker npm run health
```

### Docker Compose

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  email-worker:
    build: .
    container_name: hlenergy-email-worker
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - /etc/localtime:/etc/localtime:ro
    networks:
      - hlenergy-network
    healthcheck:
      test: ["CMD", "npm", "run", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  hlenergy-network:
    external: true
```

```bash
# Deploy with compose
docker-compose -f docker-compose.prod.yml up -d

# Monitor
docker-compose -f docker-compose.prod.yml logs -f

# Update deployment
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

## PM2 Deployment

### Installation

```bash
# Install PM2 globally
npm install -g pm2

# Configure PM2 startup
pm2 startup
# Follow the instructions to enable PM2 on boot
```

### Configuration

```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'hlenergy-email-worker',
    script: 'worker.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      LOG_LEVEL: 'info'
    },
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true,
    merge_logs: true,
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    min_uptime: '10s',
    max_restarts: 10,
    restart_delay: 4000,
    kill_timeout: 5000,
    listen_timeout: 3000
  }]
};
```

### Deployment Commands

```bash
# Start application
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Monitor
pm2 status
pm2 logs hlenergy-email-worker
pm2 monit

# Update application
git pull
npm ci --production
pm2 restart hlenergy-email-worker

# Stop application
pm2 stop hlenergy-email-worker
pm2 delete hlenergy-email-worker
```

## Systemd Service

### Service File

```ini
# /etc/systemd/system/hlenergy-email-worker.service
[Unit]
Description=HLenergy Email Worker
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=simple
User=emailworker
Group=emailworker
WorkingDirectory=/home/<USER>/hlenergy-email-worker
ExecStart=/usr/bin/node worker.js
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=hlenergy-email-worker
Environment=NODE_ENV=production
EnvironmentFile=/home/<USER>/hlenergy-email-worker/.env

[Install]
WantedBy=multi-user.target
```

### Service Management

```bash
# Install service
sudo cp hlenergy-email-worker.service /etc/systemd/system/
sudo systemctl daemon-reload

# Enable and start
sudo systemctl enable hlenergy-email-worker
sudo systemctl start hlenergy-email-worker

# Check status
sudo systemctl status hlenergy-email-worker

# View logs
sudo journalctl -u hlenergy-email-worker -f

# Restart service
sudo systemctl restart hlenergy-email-worker

# Stop service
sudo systemctl stop hlenergy-email-worker
```

## Kubernetes Deployment

### ConfigMap

```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: email-worker-config
  namespace: hlenergy
data:
  NODE_ENV: "production"
  LOG_LEVEL: "info"
  EMAIL_BATCH_SIZE: "10"
  EMAIL_PROCESSING_INTERVAL: "30000"
  EMAIL_MAX_RETRIES: "3"
  CLEANUP_OLD_EMAILS_DAYS: "30"
  CLEANUP_INTERVAL_HOURS: "24"
```

### Secret

```yaml
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: email-worker-secret
  namespace: hlenergy
type: Opaque
data:
  DB_HOST: <base64-encoded-value>
  DB_USER: <base64-encoded-value>
  DB_PASSWORD: <base64-encoded-value>
  DB_NAME: <base64-encoded-value>
  SMTP_HOST: <base64-encoded-value>
  SMTP_USER: <base64-encoded-value>
  SMTP_PASSWORD: <base64-encoded-value>
```

### Deployment

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: email-worker
  namespace: hlenergy
  labels:
    app: email-worker
spec:
  replicas: 1
  selector:
    matchLabels:
      app: email-worker
  template:
    metadata:
      labels:
        app: email-worker
    spec:
      containers:
      - name: email-worker
        image: hlenergy-email-worker:latest
        ports:
        - containerPort: 3002
        envFrom:
        - configMapRef:
            name: email-worker-config
        - secretRef:
            name: email-worker-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - node
            - scripts/health-check.js
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - node
            - scripts/health-check.js
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: logs
        emptyDir: {}
      restartPolicy: Always
```

### Service (Optional)

```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: email-worker-service
  namespace: hlenergy
spec:
  selector:
    app: email-worker
  ports:
  - port: 3002
    targetPort: 3002
    name: health-check
  type: ClusterIP
```

### Deployment Commands

```bash
# Create namespace
kubectl create namespace hlenergy

# Apply configurations
kubectl apply -f configmap.yaml
kubectl apply -f secret.yaml
kubectl apply -f deployment.yaml
kubectl apply -f service.yaml

# Check status
kubectl get pods -n hlenergy
kubectl get services -n hlenergy

# View logs
kubectl logs -f deployment/email-worker -n hlenergy

# Scale deployment
kubectl scale deployment email-worker --replicas=2 -n hlenergy

# Update deployment
kubectl set image deployment/email-worker email-worker=hlenergy-email-worker:v2 -n hlenergy

# Delete deployment
kubectl delete -f deployment.yaml
```

## Monitoring & Alerting

### Health Check Endpoint

If `HEALTH_CHECK_ENABLED=true` and `HEALTH_CHECK_PORT=3002`:

```bash
# Health check URL
curl http://localhost:3002/health

# Response format
{
  "status": "healthy",
  "worker": { ... },
  "queue": { ... },
  "database": { ... },
  "email": { ... }
}
```

### Prometheus Metrics

Create a metrics endpoint for Prometheus monitoring:

```javascript
// Add to worker.js for Prometheus integration
const promClient = require('prom-client');

// Create metrics
const emailsProcessed = new promClient.Counter({
  name: 'emails_processed_total',
  help: 'Total number of emails processed'
});

const emailsSent = new promClient.Counter({
  name: 'emails_sent_total',
  help: 'Total number of emails sent successfully'
});

const emailsFailed = new promClient.Counter({
  name: 'emails_failed_total',
  help: 'Total number of emails that failed'
});

const queueSize = new promClient.Gauge({
  name: 'email_queue_size',
  help: 'Current size of email queue'
});
```

### Log Monitoring

Configure log aggregation with:
- ELK Stack (Elasticsearch, Logstash, Kibana)
- Fluentd
- Grafana Loki

### Alerting Rules

Set up alerts for:
- Worker process down
- High queue backlog (>100 emails)
- High failure rate (>10%)
- Database connection failures
- SMTP connection failures

## Backup & Recovery

### Database Backup

The email worker uses the same database as the main backend. Ensure regular backups of the `email_queue` table.

### Configuration Backup

```bash
# Backup configuration
tar -czf email-worker-config-$(date +%Y%m%d).tar.gz \
  .env \
  ecosystem.config.js \
  package.json \
  package-lock.json

# Store in secure location
```

### Disaster Recovery

1. **Worker Failure**: Restart the worker process
2. **Database Failure**: Restore from backup, worker will resume processing
3. **SMTP Failure**: Fix SMTP configuration, failed emails will retry automatically
4. **Complete System Failure**: Restore configuration and restart worker

### Recovery Testing

```bash
# Test recovery procedures regularly
npm run health
npm test
npm run status
```

## Performance Optimization

### Database Optimization

```sql
-- Ensure proper indexes exist
CREATE INDEX idx_email_queue_status_priority_scheduled 
ON email_queue (status, priority, scheduled_at);

CREATE INDEX idx_email_queue_created_at 
ON email_queue (created_at);
```

### Worker Tuning

```bash
# Adjust based on load
EMAIL_BATCH_SIZE=20              # Increase for higher throughput
EMAIL_PROCESSING_INTERVAL=15000  # Decrease for lower latency
```

### Resource Monitoring

Monitor:
- CPU usage
- Memory usage
- Database connections
- Network I/O
- Disk I/O (for logs)

This deployment guide provides comprehensive coverage for deploying the standalone email worker in various environments. Choose the deployment method that best fits your infrastructure and requirements.

#!/bin/bash

# HLenergy Email Worker - Production Deployment Script
# This script deploys the email worker to production with PM2

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="hlenergy-email-worker"
DEPLOY_DIR="/opt/hlenergy/email-worker"
BACKUP_DIR="/opt/hlenergy/backups"
LOG_DIR="/var/log/hlenergy"
SERVICE_USER="hlenergy"

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   log_error "This script should not be run as root for security reasons"
   exit 1
fi

# Check if PM2 is installed
if ! command -v pm2 &> /dev/null; then
    log_error "PM2 is not installed. Please install PM2 first:"
    echo "npm install -g pm2"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "worker.js" ]; then
    log_error "worker.js not found. Please run this script from the email-worker directory."
    exit 1
fi

log_info "🚀 Starting HLenergy Email Worker Production Deployment..."

# Step 1: Pre-deployment checks
log_step "1. Running pre-deployment checks..."

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"
if ! node -e "process.exit(require('semver').gte('$NODE_VERSION', '$REQUIRED_VERSION') ? 0 : 1)" 2>/dev/null; then
    log_error "Node.js version $REQUIRED_VERSION or higher is required. Current: $NODE_VERSION"
    exit 1
fi
log_info "✅ Node.js version check passed: $NODE_VERSION"

# Check if .env exists
if [ ! -f ".env" ]; then
    if [ -f ".env.production" ]; then
        log_warn "No .env file found. Copying from .env.production"
        cp .env.production .env
    else
        log_error "No .env file found. Please create one based on .env.production"
        exit 1
    fi
fi
log_info "✅ Environment configuration found"

# Step 2: Install dependencies
log_step "2. Installing production dependencies..."
npm ci --production --silent
log_info "✅ Dependencies installed"

# Step 3: Run tests
log_step "3. Running connection tests..."
if npm run test --silent; then
    log_info "✅ Connection tests passed"
else
    log_error "❌ Connection tests failed"
    exit 1
fi

# Step 4: Create necessary directories
log_step "4. Creating directories..."
mkdir -p logs
mkdir -p scripts
chmod 755 logs
log_info "✅ Directories created"

# Step 5: Set file permissions
log_step "5. Setting file permissions..."
chmod 600 .env
chmod +x *.sh 2>/dev/null || true
chmod +x scripts/*.js 2>/dev/null || true
log_info "✅ File permissions set"

# Step 6: Stop existing worker if running
log_step "6. Stopping existing worker..."
if pm2 describe $APP_NAME > /dev/null 2>&1; then
    log_info "Stopping existing worker..."
    pm2 stop $APP_NAME
    pm2 delete $APP_NAME
    log_info "✅ Existing worker stopped"
else
    log_info "✅ No existing worker found"
fi

# Step 7: Start worker with PM2
log_step "7. Starting email worker..."
pm2 start ecosystem.config.js --env production
log_info "✅ Email worker started"

# Step 8: Save PM2 configuration
log_step "8. Saving PM2 configuration..."
pm2 save
log_info "✅ PM2 configuration saved"

# Step 9: Setup PM2 startup script (if not already done)
log_step "9. Setting up PM2 startup script..."
if ! pm2 startup | grep -q "already"; then
    log_warn "PM2 startup script not configured. Run the following command as root:"
    pm2 startup | grep "sudo"
else
    log_info "✅ PM2 startup script already configured"
fi

# Step 10: Health check
log_step "10. Running health check..."
sleep 5  # Wait for worker to start
if npm run health --silent; then
    log_info "✅ Health check passed"
else
    log_warn "⚠️ Health check failed - worker may still be starting"
fi

# Step 11: Display status
log_step "11. Deployment completed!"
echo ""
echo "📊 Worker Status:"
pm2 status $APP_NAME

echo ""
echo "📋 Useful Commands:"
echo "  View logs:        pm2 logs $APP_NAME"
echo "  Monitor:          pm2 monit"
echo "  Restart:          pm2 restart $APP_NAME"
echo "  Stop:             pm2 stop $APP_NAME"
echo "  Health check:     npm run health"
echo "  Queue status:     npm run status"

echo ""
echo "🔗 Monitoring URLs:"
if grep -q "HEALTH_CHECK_ENABLED=true" .env 2>/dev/null; then
    HEALTH_PORT=$(grep "HEALTH_CHECK_PORT" .env | cut -d'=' -f2 | tr -d ' ')
    echo "  Health Check:     http://localhost:${HEALTH_PORT:-3002}/health"
fi

if grep -q "ENABLE_METRICS=true" .env 2>/dev/null; then
    METRICS_PORT=$(grep "METRICS_PORT" .env | cut -d'=' -f2 | tr -d ' ')
    echo "  Metrics:          http://localhost:${METRICS_PORT:-3003}/metrics"
fi

echo ""
log_info "🎉 HLenergy Email Worker deployed successfully!"

# Optional: Show recent logs
echo ""
read -p "Would you like to view recent logs? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    pm2 logs $APP_NAME --lines 20
fi

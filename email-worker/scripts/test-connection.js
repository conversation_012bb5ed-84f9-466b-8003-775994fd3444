#!/usr/bin/env node

const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import worker components
const { sequelize, emailService } = require('../worker.js');
const nodemailer = require('nodemailer');

async function testConnections() {
  console.log('🧪 HLenergy Email Worker Connection Test');
  console.log('========================================');

  let allTestsPassed = true;

  // Test 1: Environment Variables
  console.log('1️⃣  Testing Environment Variables...');
  const requiredEnvVars = [
    'DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD',
    'SMTP_HOST', 'SMTP_USER', 'SMTP_PASSWORD'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.log('❌ Missing environment variables:');
    missingVars.forEach(varName => {
      console.log(`   • ${varName}`);
    });
    allTestsPassed = false;
  } else {
    console.log('✅ All required environment variables are set');
  }
  console.log('');

  // Test 2: Database Connection
  console.log('2️⃣  Testing Database Connection...');
  try {
    await sequelize.authenticate();
    console.log('✅ Database connection successful');
    
    // Test query
    const [results] = await sequelize.query('SELECT COUNT(*) as count FROM email_queue');
    console.log(`   📊 Email queue contains ${results[0].count} emails`);
    
  } catch (error) {
    console.log('❌ Database connection failed:', error.message);
    console.log('   💡 Check your database credentials and ensure the server is running');
    allTestsPassed = false;
  }
  console.log('');

  // Test 3: Email Service Connection (Enhanced)
  console.log('3️⃣  Testing Email Service Connection...');

  // Display current SMTP configuration
  console.log('📧 SMTP Configuration:');
  console.log(`   Host: ${process.env.SMTP_HOST || 'NOT SET'}`);
  console.log(`   Port: ${process.env.SMTP_PORT || '587 (default)'}`);
  console.log(`   User: ${process.env.SMTP_USER || 'NOT SET'}`);
  console.log(`   Password: ${process.env.SMTP_PASSWORD ? '***SET***' : 'NOT SET'}`);
  console.log(`   Secure: ${process.env.SMTP_SECURE || 'false (default)'}`);
  console.log('');

  try {
    const smtpConfig = {
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT) || 587,
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD
      },
      connectionTimeout: 10000, // 10 seconds
      greetingTimeout: 10000,   // 10 seconds
      socketTimeout: 10000,     // 10 seconds
      debug: true,              // Enable debug output
      logger: true              // Enable logging
    };

    console.log('🔍 Detailed SMTP Test:');
    console.log(`   Connecting to: ${smtpConfig.host}:${smtpConfig.port}`);
    console.log(`   Security: ${smtpConfig.secure ? 'SSL/TLS' : 'STARTTLS'}`);
    console.log(`   Timeout: ${smtpConfig.connectionTimeout}ms`);
    console.log('');

    const transporter = nodemailer.createTransport(smtpConfig);

    // Test connection with detailed error handling
    console.log('⏳ Attempting SMTP connection...');
    await transporter.verify();
    console.log('✅ Email service connection successful');

  } catch (error) {
    console.log('❌ Email service connection failed');
    console.log('');

    // Detailed error analysis
    console.log('🔍 Error Analysis:');
    console.log(`   Error Type: ${error.constructor.name}`);
    console.log(`   Error Code: ${error.code || 'N/A'}`);
    console.log(`   Error Message: ${error.message}`);

    if (error.response) {
      console.log(`   SMTP Response: ${error.response}`);
    }

    console.log('');
    console.log('💡 Troubleshooting Tips:');

    if (error.code === 'ECONNREFUSED') {
      console.log('   • Connection refused - check if SMTP server is running');
      console.log('   • Verify SMTP_HOST and SMTP_PORT are correct');
      console.log('   • Check firewall settings');
    } else if (error.code === 'ENOTFOUND') {
      console.log('   • DNS resolution failed - check SMTP_HOST spelling');
      console.log('   • Verify internet connection');
    } else if (error.code === 'ETIMEDOUT' || error.message.includes('timeout')) {
      console.log('   • Connection timeout - server may be slow or unreachable');
      console.log('   • Try increasing timeout values');
      console.log('   • Check network connectivity');
    } else if (error.responseCode === 535 || error.message.includes('authentication')) {
      console.log('   • Authentication failed - check SMTP_USER and SMTP_PASSWORD');
      console.log('   • For Gmail: use App Password, not regular password');
      console.log('   • Verify 2FA settings if enabled');
    } else if (error.responseCode === 587 || error.message.includes('STARTTLS')) {
      console.log('   • STARTTLS issue - try setting SMTP_SECURE=false');
      console.log('   • For port 465: set SMTP_SECURE=true');
      console.log('   • For port 587: set SMTP_SECURE=false');
    }

    console.log('');
    console.log('🔧 Common SMTP Settings:');
    console.log('   Gmail:');
    console.log('     SMTP_HOST=smtp.gmail.com');
    console.log('     SMTP_PORT=587');
    console.log('     SMTP_SECURE=false');
    console.log('   ');
    console.log('   Outlook/Hotmail:');
    console.log('     SMTP_HOST=smtp-mail.outlook.com');
    console.log('     SMTP_PORT=587');
    console.log('     SMTP_SECURE=false');

    allTestsPassed = false;
  }
  console.log('');

  // Test 4: File Permissions
  console.log('4️⃣  Testing File Permissions...');
  const fs = require('fs');
  
  try {
    // Test logs directory
    const logsDir = path.join(__dirname, '..', 'logs');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    
    // Test write permission
    const testFile = path.join(logsDir, 'test-write.tmp');
    fs.writeFileSync(testFile, 'test');
    fs.unlinkSync(testFile);
    
    console.log('✅ File permissions are correct');
    
  } catch (error) {
    console.log('❌ File permission test failed:', error.message);
    console.log('   💡 Check write permissions for the logs directory');
    allTestsPassed = false;
  }
  console.log('');

  // Test 5: Configuration Values
  console.log('5️⃣  Testing Configuration Values...');
  const config = {
    batchSize: parseInt(process.env.EMAIL_BATCH_SIZE) || 10,
    processingInterval: parseInt(process.env.EMAIL_PROCESSING_INTERVAL) || 30000,
    maxRetries: parseInt(process.env.EMAIL_MAX_RETRIES) || 3,
    cleanupDays: parseInt(process.env.CLEANUP_OLD_EMAILS_DAYS) || 30
  };

  console.log('📋 Current Configuration:');
  console.log(`   Batch Size: ${config.batchSize}`);
  console.log(`   Processing Interval: ${config.processingInterval / 1000}s`);
  console.log(`   Max Retries: ${config.maxRetries}`);
  console.log(`   Cleanup Days: ${config.cleanupDays}`);

  // Validate configuration
  if (config.batchSize < 1 || config.batchSize > 100) {
    console.log('⚠️  Warning: Batch size should be between 1 and 100');
  }
  if (config.processingInterval < 5000) {
    console.log('⚠️  Warning: Processing interval should be at least 5 seconds');
  }
  if (config.maxRetries < 1 || config.maxRetries > 10) {
    console.log('⚠️  Warning: Max retries should be between 1 and 10');
  }

  console.log('✅ Configuration values loaded');
  console.log('');

  // Summary
  console.log('📋 Test Summary:');
  console.log('================');
  if (allTestsPassed) {
    console.log('✅ All tests passed! The worker should run successfully.');
    console.log('');
    console.log('🚀 Ready to start:');
    console.log('   npm start     - Start the worker');
    console.log('   npm run dev   - Start with auto-restart');
  } else {
    console.log('❌ Some tests failed. Please fix the issues above before starting the worker.');
    console.log('');
    console.log('💡 Common solutions:');
    console.log('   • Copy .env.example to .env and configure it');
    console.log('   • Ensure database server is running');
    console.log('   • Verify SMTP credentials');
    console.log('   • Check file permissions');
  }

  console.log('');
  console.log(`🕐 Test completed at: ${new Date().toISOString()}`);
  console.log('========================================');

  // Close database connection
  try {
    await sequelize.close();
  } catch (e) {
    // Ignore errors during cleanup
  }

  // Exit with appropriate code
  process.exit(allTestsPassed ? 0 : 1);
}

// Main execution
testConnections().catch(error => {
  console.error('❌ Unexpected error during connection test:', error);
  process.exit(1);
});

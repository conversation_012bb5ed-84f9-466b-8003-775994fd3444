#!/usr/bin/env node

/**
 * Test database logging functionality
 * This script tests the database logging features of the email worker
 */

const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import worker components
const { WorkerLog, sequelize, logger } = require('../worker.js');

async function testDatabaseLogging() {
  console.log('🧪 Testing Database Logging');
  console.log('===========================');

  try {
    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Check if database logging is enabled
    const logToDatabase = process.env.LOG_TO_DATABASE === 'true';
    console.log(`📊 Database logging: ${logToDatabase ? 'ENABLED' : 'DISABLED'}`);

    if (!logToDatabase) {
      console.log('');
      console.log('💡 To enable database logging:');
      console.log('   1. Set LOG_TO_DATABASE=true in .env');
      console.log('   2. Run: npm run migrate');
      console.log('   3. Restart the worker');
      return;
    }

    // Ensure table exists
    await WorkerLog.sync({ alter: false });
    console.log('✅ worker_logs table verified');

    // Test logging at different levels
    console.log('');
    console.log('📝 Testing log levels...');

    // Test info log
    logger.info('Test info message', {
      type: 'test',
      testData: { value: 123, text: 'test' },
      emailId: 999,
    });

    // Test warning log
    logger.warn('Test warning message', {
      type: 'test',
      warning: 'This is a test warning',
    });

    // Test error log
    logger.error('Test error message', {
      type: 'test',
      error: 'This is a test error',
      stack: 'Test stack trace',
    });

    // Test debug log (may not appear depending on log level)
    logger.debug('Test debug message', {
      type: 'test',
      debug: 'This is a test debug message',
    });

    // Wait a moment for logs to be written
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Query recent logs
    console.log('');
    console.log('📋 Recent logs from database:');
    console.log('=============================');

    const recentLogs = await WorkerLog.findAll({
      where: {
        worker_instance: `worker-${process.pid}`,
      },
      order: [['created_at', 'DESC']],
      limit: 10,
    });

    if (recentLogs.length === 0) {
      console.log('   No logs found for this worker instance');
      console.log('   This might be normal if database logging just started');
    } else {
      recentLogs.forEach((log, index) => {
        console.log(`   ${index + 1}. [${log.level.toUpperCase()}] ${log.message}`);
        console.log(`      Time: ${log.created_at}`);
        console.log(`      Worker: ${log.worker_instance}`);
        if (log.email_id) {
          console.log(`      Email ID: ${log.email_id}`);
        }
        if (log.duration_ms) {
          console.log(`      Duration: ${log.duration_ms}ms`);
        }
        if (log.memory_usage) {
          console.log(`      Memory: ${log.memory_usage.rss}MB RSS, ${log.memory_usage.heapUsed}MB Heap`);
        }
        console.log('      ---');
      });
    }

    // Show statistics
    console.log('');
    console.log('📊 Database logging statistics:');
    console.log('===============================');

    const [
      totalLogs,
      infoLogs,
      warnLogs,
      errorLogs,
      thisWorkerLogs
    ] = await Promise.all([
      WorkerLog.count(),
      WorkerLog.count({ where: { level: 'info' } }),
      WorkerLog.count({ where: { level: 'warn' } }),
      WorkerLog.count({ where: { level: 'error' } }),
      WorkerLog.count({ where: { worker_instance: `worker-${process.pid}` } })
    ]);

    console.log(`   Total logs: ${totalLogs}`);
    console.log(`   Info logs: ${infoLogs}`);
    console.log(`   Warning logs: ${warnLogs}`);
    console.log(`   Error logs: ${errorLogs}`);
    console.log(`   This worker logs: ${thisWorkerLogs}`);

    // Test cleanup (dry run)
    console.log('');
    console.log('🧹 Testing log cleanup (dry run):');
    console.log('==================================');

    const { Op } = require('sequelize');
    const testCutoff = new Date(Date.now() - 1 * 24 * 60 * 60 * 1000); // 1 day ago
    
    const oldLogs = await WorkerLog.count({
      where: {
        created_at: { [Op.lt]: testCutoff },
      },
    });

    console.log(`   Logs older than 1 day: ${oldLogs}`);
    console.log('   (These would be cleaned up with: npm run cleanup)');

    console.log('');
    console.log('✅ Database logging test completed successfully!');
    console.log('');
    console.log('💡 Next steps:');
    console.log('   • Start the worker: npm start');
    console.log('   • Monitor logs: npm run health');
    console.log('   • View logs in database or check log files');

  } catch (error) {
    console.log('❌ Database logging test failed:', error.message);
    console.log('');
    console.log('💡 Troubleshooting:');
    console.log('   • Ensure database connection is working: npm test');
    console.log('   • Create worker_logs table: npm run migrate');
    console.log('   • Check LOG_TO_DATABASE setting in .env');
    console.log('   • Verify database permissions');
    
    process.exit(1);
  } finally {
    // Close database connection
    try {
      await sequelize.close();
      console.log('');
      console.log('🔌 Database connection closed');
    } catch (e) {
      // Ignore errors during cleanup
    }
  }

  console.log('');
  console.log('===========================');
}

// Main execution
testDatabaseLogging().catch(error => {
  console.error('❌ Unexpected error during database logging test:', error);
  process.exit(1);
});

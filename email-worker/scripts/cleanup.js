#!/usr/bin/env node

const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import worker components
const { EmailQueue, sequelize } = require('../worker.js');

async function performCleanup() {
  console.log('🧹 HLenergy Email Worker Cleanup');
  console.log('=================================');

  try {
    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    const daysOld = parseInt(process.env.CLEANUP_OLD_EMAILS_DAYS) || 30;
    console.log(`📅 Cleaning up emails older than ${daysOld} days...`);

    // Get stats before cleanup
    const statsBefore = await EmailQueue.getQueueStats();
    console.log('');
    console.log('📊 Before Cleanup:');
    console.log(`   Total emails: ${statsBefore.queue.total}`);
    console.log(`   Sent: ${statsBefore.queue.sent}`);
    console.log(`   Failed: ${statsBefore.queue.failed}`);

    // Perform cleanup
    const deleted = await EmailQueue.cleanupOldEmails(daysOld);
    
    // Get stats after cleanup
    const statsAfter = await EmailQueue.getQueueStats();
    
    console.log('');
    console.log('🗑️  Cleanup Results:');
    console.log(`   Deleted emails: ${deleted}`);
    console.log('');
    console.log('📊 After Cleanup:');
    console.log(`   Total emails: ${statsAfter.queue.total}`);
    console.log(`   Sent: ${statsAfter.queue.sent}`);
    console.log(`   Failed: ${statsAfter.queue.failed}`);
    console.log(`   Pending: ${statsAfter.queue.pending}`);
    console.log(`   Processing: ${statsAfter.queue.processing}`);

    if (deleted > 0) {
      console.log('');
      console.log(`✅ Successfully cleaned up ${deleted} old emails`);
    } else {
      console.log('');
      console.log('ℹ️  No old emails found to clean up');
    }

    // Show space savings estimate
    if (deleted > 0) {
      const estimatedSpaceSavedKB = deleted * 2; // Rough estimate: 2KB per email
      console.log(`💾 Estimated space saved: ~${estimatedSpaceSavedKB}KB`);
    }

  } catch (error) {
    console.log('❌ Cleanup failed:', error.message);
    console.log('');
    console.log('💡 Troubleshooting:');
    console.log('   • Check database connection');
    console.log('   • Verify .env configuration');
    console.log('   • Check database permissions');
    
    process.exit(1);
  } finally {
    // Close database connection
    try {
      await sequelize.close();
      console.log('');
      console.log('🔌 Database connection closed');
    } catch (e) {
      // Ignore errors during cleanup
    }
  }

  console.log('');
  console.log(`🕐 Cleanup completed at: ${new Date().toISOString()}`);
  console.log('=================================');
}

// Handle command line arguments
const args = process.argv.slice(2);
const force = args.includes('--force') || args.includes('-f');
const dryRun = args.includes('--dry-run') || args.includes('-d');

if (dryRun) {
  console.log('🔍 DRY RUN MODE - No emails will be deleted');
  console.log('===========================================');
  
  // TODO: Implement dry run mode
  console.log('💡 Dry run mode not yet implemented');
  console.log('   Run without --dry-run to perform actual cleanup');
  process.exit(0);
}

if (!force) {
  console.log('⚠️  This will permanently delete old processed emails');
  console.log('   Add --force flag to confirm: npm run cleanup -- --force');
  console.log('   Add --dry-run flag to preview: npm run cleanup -- --dry-run');
  process.exit(0);
}

// Main execution
performCleanup().catch(error => {
  console.error('❌ Unexpected error during cleanup:', error);
  process.exit(1);
});

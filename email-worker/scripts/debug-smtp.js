#!/usr/bin/env node

/**
 * HLenergy Email Worker - SMTP Debug Tool
 * 
 * This script provides detailed SMTP debugging and testing
 */

require('dotenv').config();
const nodemailer = require('nodemailer');
const dns = require('dns').promises;
const net = require('net');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function debugSMTP() {
  log('🔍 HLenergy SMTP Debug Tool', 'bright');
  log('=' * 50, 'cyan');

  // Step 1: Check environment variables
  log('\n📋 Environment Variables:', 'blue');
  const smtpVars = {
    'SMTP_HOST': process.env.SMTP_HOST,
    'SMTP_PORT': process.env.SMTP_PORT || '587',
    'SMTP_USER': process.env.SMTP_USER,
    'SMTP_PASSWORD': process.env.SMTP_PASSWORD ? '***SET***' : 'NOT SET',
    'SMTP_SECURE': process.env.SMTP_SECURE || 'false'
  };

  Object.entries(smtpVars).forEach(([key, value]) => {
    const status = value && value !== 'NOT SET' ? '✅' : '❌';
    log(`   ${status} ${key}: ${value}`);
  });

  if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.SMTP_PASSWORD) {
    log('\n❌ Missing required SMTP configuration!', 'red');
    log('Please set SMTP_HOST, SMTP_USER, and SMTP_PASSWORD in your .env file', 'yellow');
    return;
  }

  // Step 2: DNS Resolution
  log('\n🌐 DNS Resolution Test:', 'blue');
  try {
    const addresses = await dns.lookup(process.env.SMTP_HOST);
    log(`   ✅ ${process.env.SMTP_HOST} resolves to: ${addresses.address}`, 'green');
  } catch (error) {
    log(`   ❌ DNS resolution failed: ${error.message}`, 'red');
    log('   💡 Check your internet connection and SMTP_HOST spelling', 'yellow');
    return;
  }

  // Step 3: Port connectivity
  log('\n🔌 Port Connectivity Test:', 'blue');
  const port = parseInt(process.env.SMTP_PORT) || 587;
  
  try {
    await testPortConnection(process.env.SMTP_HOST, port);
    log(`   ✅ Port ${port} is reachable on ${process.env.SMTP_HOST}`, 'green');
  } catch (error) {
    log(`   ❌ Port ${port} connection failed: ${error.message}`, 'red');
    log('   💡 Check firewall settings and port configuration', 'yellow');
    return;
  }

  // Step 4: SMTP Configuration Analysis
  log('\n⚙️ SMTP Configuration Analysis:', 'blue');
  const secure = process.env.SMTP_SECURE === 'true';
  const expectedSecure = port === 465;
  
  log(`   Port: ${port}`);
  log(`   Secure (SSL/TLS): ${secure}`);
  log(`   Expected secure for port: ${expectedSecure}`);
  
  if (secure !== expectedSecure) {
    log(`   ⚠️ Security mismatch detected!`, 'yellow');
    if (port === 465) {
      log('   💡 For port 465, set SMTP_SECURE=true', 'yellow');
    } else if (port === 587) {
      log('   💡 For port 587, set SMTP_SECURE=false (uses STARTTLS)', 'yellow');
    }
  } else {
    log(`   ✅ Security configuration looks correct`, 'green');
  }

  // Step 5: SMTP Connection Test
  log('\n📧 SMTP Connection Test:', 'blue');
  
  const transportConfig = {
    host: process.env.SMTP_HOST,
    port: port,
    secure: secure,
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASSWORD
    },
    connectionTimeout: 15000,
    greetingTimeout: 15000,
    socketTimeout: 15000,
    debug: true,
    logger: {
      debug: (info) => log(`   [DEBUG] ${info}`, 'cyan'),
      info: (info) => log(`   [INFO] ${info}`, 'blue'),
      warn: (info) => log(`   [WARN] ${info}`, 'yellow'),
      error: (info) => log(`   [ERROR] ${info}`, 'red')
    }
  };

  log('   Creating transporter with configuration:');
  log(`     Host: ${transportConfig.host}`);
  log(`     Port: ${transportConfig.port}`);
  log(`     Secure: ${transportConfig.secure}`);
  log(`     User: ${transportConfig.auth.user}`);
  log('');

  try {
    const transporter = nodemailer.createTransporter(transportConfig);
    
    log('   ⏳ Testing SMTP connection...', 'yellow');
    await transporter.verify();
    log('   ✅ SMTP connection successful!', 'green');
    
    // Step 6: Test email sending (optional)
    if (process.argv.includes('--send-test')) {
      log('\n📤 Sending Test Email:', 'blue');
      
      const testEmail = {
        from: `"HLenergy Test" <${process.env.SMTP_USER}>`,
        to: process.env.SMTP_USER, // Send to self
        subject: `HLenergy SMTP Test - ${new Date().toISOString()}`,
        text: 'This is a test email from the HLenergy email worker SMTP debug tool.',
        html: `
          <h2>HLenergy SMTP Test</h2>
          <p>This is a <strong>test email</strong> from the HLenergy email worker.</p>
          <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
          <p><strong>SMTP Host:</strong> ${process.env.SMTP_HOST}</p>
          <p><strong>SMTP Port:</strong> ${port}</p>
          <p><strong>Secure:</strong> ${secure}</p>
        `
      };

      const info = await transporter.sendMail(testEmail);
      log(`   ✅ Test email sent successfully!`, 'green');
      log(`   📧 Message ID: ${info.messageId}`);
      log(`   📬 Check your inbox: ${process.env.SMTP_USER}`);
    }
    
    transporter.close();
    
  } catch (error) {
    log('   ❌ SMTP connection failed!', 'red');
    log('');
    
    // Detailed error analysis
    log('🔍 Error Details:', 'red');
    log(`   Type: ${error.constructor.name}`);
    log(`   Code: ${error.code || 'N/A'}`);
    log(`   Message: ${error.message}`);
    
    if (error.response) {
      log(`   SMTP Response: ${error.response}`);
    }
    
    if (error.responseCode) {
      log(`   Response Code: ${error.responseCode}`);
    }
    
    log('');
    log('💡 Troubleshooting Guide:', 'yellow');
    
    if (error.code === 'ECONNREFUSED') {
      log('   🔸 Connection Refused:', 'yellow');
      log('     • SMTP server may be down or not accepting connections');
      log('     • Check if the port is correct (587 for STARTTLS, 465 for SSL)');
      log('     • Verify firewall settings');
      
    } else if (error.code === 'ETIMEDOUT') {
      log('   🔸 Connection Timeout:', 'yellow');
      log('     • Server is taking too long to respond');
      log('     • Check network connectivity');
      log('     • Try a different port (587 vs 465)');
      
    } else if (error.code === 'ENOTFOUND') {
      log('   🔸 Host Not Found:', 'yellow');
      log('     • Check SMTP_HOST spelling');
      log('     • Verify DNS resolution');
      log('     • Check internet connection');
      
    } else if (error.responseCode === 535) {
      log('   🔸 Authentication Failed:', 'yellow');
      log('     • Check SMTP_USER and SMTP_PASSWORD');
      log('     • For Gmail: use App Password, not regular password');
      log('     • Enable "Less secure app access" if using regular password');
      log('     • Check if 2FA is enabled and requires app password');
      
    } else if (error.message.includes('STARTTLS')) {
      log('   🔸 STARTTLS Issue:', 'yellow');
      log('     • Try setting SMTP_SECURE=false for port 587');
      log('     • Try setting SMTP_SECURE=true for port 465');
      log('     • Some servers require specific TLS versions');
      
    } else if (error.message.includes('self signed certificate')) {
      log('   🔸 Certificate Issue:', 'yellow');
      log('     • Server using self-signed certificate');
      log('     • Add rejectUnauthorized: false to config (not recommended for production)');
      
    } else {
      log('   🔸 General Troubleshooting:', 'yellow');
      log('     • Verify all SMTP settings with your email provider');
      log('     • Check server logs for more details');
      log('     • Try connecting with a different email client');
    }
    
    log('');
    log('🔧 Common Provider Settings:', 'cyan');
    log('   Gmail:');
    log('     SMTP_HOST=smtp.gmail.com');
    log('     SMTP_PORT=587');
    log('     SMTP_SECURE=false');
    log('     (Use App Password if 2FA enabled)');
    log('');
    log('   Outlook/Hotmail:');
    log('     SMTP_HOST=smtp-mail.outlook.com');
    log('     SMTP_PORT=587');
    log('     SMTP_SECURE=false');
    log('');
    log('   Yahoo:');
    log('     SMTP_HOST=smtp.mail.yahoo.com');
    log('     SMTP_PORT=587');
    log('     SMTP_SECURE=false');
  }
}

function testPortConnection(host, port) {
  return new Promise((resolve, reject) => {
    const socket = new net.Socket();
    
    socket.setTimeout(10000); // 10 second timeout
    
    socket.on('connect', () => {
      socket.destroy();
      resolve();
    });
    
    socket.on('timeout', () => {
      socket.destroy();
      reject(new Error('Connection timeout'));
    });
    
    socket.on('error', (error) => {
      socket.destroy();
      reject(error);
    });
    
    socket.connect(port, host);
  });
}

// Run if called directly
if (require.main === module) {
  debugSMTP()
    .then(() => {
      log('\n🎉 SMTP debug completed!', 'green');
      log('');
      log('💡 Usage tips:', 'blue');
      log('   • Run with --send-test to send a test email');
      log('   • Check your .env file for correct SMTP settings');
      log('   • Use this output to troubleshoot connection issues');
    })
    .catch(error => {
      log(`\n❌ Debug failed: ${error.message}`, 'red');
      process.exit(1);
    });
}

#!/usr/bin/env node

/**
 * Create worker_logs table migration script
 * Run this script to create the worker_logs table in your database
 */

const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import database connection
const { sequelize, WorkerLog } = require('../worker.js');

async function createWorkerLogsTable() {
  console.log('🗄️  Creating worker_logs table...');
  console.log('===================================');

  try {
    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Create the table
    await WorkerLog.sync({ force: false, alter: true });
    console.log('✅ worker_logs table created/updated successfully');

    // Show table structure
    const tableInfo = await sequelize.getQueryInterface().describeTable('worker_logs');
    console.log('');
    console.log('📋 Table Structure:');
    console.log('==================');
    
    Object.entries(tableInfo).forEach(([column, info]) => {
      console.log(`   ${column.padEnd(20)} ${info.type}${info.allowNull ? '' : ' NOT NULL'}${info.defaultValue ? ` DEFAULT ${info.defaultValue}` : ''}`);
    });

    // Show indexes
    try {
      const indexes = await sequelize.getQueryInterface().showIndex('worker_logs');
      if (indexes.length > 0) {
        console.log('');
        console.log('📊 Indexes:');
        console.log('===========');
        indexes.forEach(index => {
          const columns = index.fields.map(f => f.attribute).join(', ');
          console.log(`   ${index.name}: (${columns})${index.unique ? ' UNIQUE' : ''}`);
        });
      }
    } catch (e) {
      // Ignore index listing errors
    }

    console.log('');
    console.log('🎉 Migration completed successfully!');
    console.log('');
    console.log('💡 Next steps:');
    console.log('   1. Start the email worker: npm start');
    console.log('   2. Check database logging: npm run health');
    console.log('   3. View logs in database or files');

  } catch (error) {
    console.log('❌ Migration failed:', error.message);
    console.log('');
    console.log('💡 Troubleshooting:');
    console.log('   • Check database connection settings in .env');
    console.log('   • Ensure database user has CREATE TABLE permissions');
    console.log('   • Verify database server is running');
    
    process.exit(1);
  } finally {
    // Close database connection
    try {
      await sequelize.close();
      console.log('');
      console.log('🔌 Database connection closed');
    } catch (e) {
      // Ignore errors during cleanup
    }
  }

  console.log('');
  console.log('===================================');
}

// Show usage information
function showUsage() {
  console.log('📚 Worker Logs Table Migration');
  console.log('==============================');
  console.log('');
  console.log('This script creates the worker_logs table for database logging.');
  console.log('');
  console.log('Table: worker_logs');
  console.log('Purpose: Store email worker activity logs in the database');
  console.log('');
  console.log('Columns:');
  console.log('  • id              - Primary key');
  console.log('  • worker_type     - Type of worker (email-worker)');
  console.log('  • worker_instance - Worker instance identifier');
  console.log('  • level           - Log level (debug, info, warn, error)');
  console.log('  • message         - Log message');
  console.log('  • metadata        - Additional log data (JSON)');
  console.log('  • email_id        - Related email ID (if applicable)');
  console.log('  • error_stack     - Error stack trace (for errors)');
  console.log('  • duration_ms     - Operation duration in milliseconds');
  console.log('  • memory_usage    - Memory usage snapshot (JSON)');
  console.log('  • ip_address      - IP address (if applicable)');
  console.log('  • created_at      - Timestamp when log was created');
  console.log('  • updated_at      - Timestamp when log was last updated');
  console.log('');
  console.log('Indexes:');
  console.log('  • worker_type + created_at');
  console.log('  • level + created_at');
  console.log('  • email_id');
  console.log('  • worker_instance + created_at');
  console.log('');
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  showUsage();
  process.exit(0);
}

if (args.includes('--info') || args.includes('-i')) {
  showUsage();
  console.log('🚀 Ready to create the table? Run without flags to proceed.');
  process.exit(0);
}

// Main execution
createWorkerLogsTable().catch(error => {
  console.error('❌ Unexpected error during migration:', error);
  process.exit(1);
});

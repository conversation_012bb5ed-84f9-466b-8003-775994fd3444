#!/usr/bin/env node

const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import worker components
const { StandaloneEmailWorker, sequelize } = require('../worker.js');

async function performHealthCheck() {
  console.log('🏥 HLenergy Email Worker Health Check');
  console.log('=====================================');

  const worker = new StandaloneEmailWorker();
  
  try {
    const health = await worker.healthCheck();
    
    console.log(`📊 Overall Status: ${getStatusEmoji(health.status)} ${health.status.toUpperCase()}`);
    console.log('');

    // Worker status
    if (health.worker) {
      console.log('👷 Worker Status:');
      console.log(`   Running: ${health.worker.isRunning ? '✅' : '❌'}`);
      console.log(`   PID: ${health.worker.pid || 'N/A'}`);
      console.log(`   Uptime: ${health.worker.uptimeFormatted || 'N/A'}`);
      console.log(`   Processed: ${health.worker.processed || 0}`);
      console.log(`   Sent: ${health.worker.sent || 0}`);
      console.log(`   Failed: ${health.worker.failed || 0}`);
      console.log(`   Success Rate: ${health.worker.successRate || 'N/A'}`);
      console.log('');
    }

    // Queue status
    if (health.queue) {
      console.log('📬 Queue Status:');
      console.log(`   Pending: ${health.queue.queue.pending || 0}`);
      console.log(`   Processing: ${health.queue.queue.processing || 0}`);
      console.log(`   Sent: ${health.queue.queue.sent || 0}`);
      console.log(`   Failed: ${health.queue.queue.failed || 0}`);
      console.log(`   Total: ${health.queue.queue.total || 0}`);
      console.log('');
      
      if (health.queue.last24h) {
        console.log('📈 Last 24 Hours:');
        console.log(`   Sent: ${health.queue.last24h.sent || 0}`);
        console.log(`   Failed: ${health.queue.last24h.failed || 0}`);
        console.log(`   Success Rate: ${health.queue.last24h.successRate || 'N/A'}`);
        console.log('');
      }
    }

    // Database status
    if (health.database) {
      console.log(`🗄️  Database: ${getStatusEmoji(health.database.status)} ${health.database.status}`);
      console.log(`   Message: ${health.database.message}`);
      console.log('');
    }

    // Email service status
    if (health.email) {
      console.log(`📧 Email Service: ${getStatusEmoji(health.email.status)} ${health.email.status}`);
      console.log(`   Message: ${health.email.message}`);
      console.log('');
    }

    // Issues
    if (health.issues && health.issues.length > 0) {
      console.log('⚠️  Issues:');
      health.issues.forEach(issue => {
        console.log(`   • ${issue}`);
      });
      console.log('');
    }

    // Recommendations
    console.log('💡 Recommendations:');
    if (health.status === 'healthy') {
      console.log('   • System is operating normally');
    } else {
      if (health.queue && health.queue.queue.pending > 100) {
        console.log('   • High queue backlog - consider increasing batch size or processing frequency');
      }
      if (health.database && health.database.status !== 'healthy') {
        console.log('   • Check database connection and credentials');
      }
      if (health.email && health.email.status !== 'healthy') {
        console.log('   • Verify SMTP configuration and credentials');
      }
      if (!health.worker || !health.worker.isRunning) {
        console.log('   • Start the worker: npm start');
      }
    }

    console.log('');
    console.log(`🕐 Check completed at: ${new Date().toISOString()}`);
    console.log('=====================================');

    // Exit with appropriate code
    process.exit(health.status === 'healthy' ? 0 : 1);

  } catch (error) {
    console.log('❌ Health check failed:', error.message);
    console.log('');
    console.log('💡 Troubleshooting:');
    console.log('   • Check if .env file exists and is configured');
    console.log('   • Verify database connection');
    console.log('   • Check SMTP settings');
    console.log('   • Review logs: npm run logs');
    console.log('');
    console.log('=====================================');
    
    process.exit(1);
  } finally {
    // Close database connection
    try {
      await sequelize.close();
    } catch (e) {
      // Ignore errors during cleanup
    }
  }
}

function getStatusEmoji(status) {
  switch (status) {
    case 'healthy': return '✅';
    case 'warning': return '⚠️';
    case 'error': return '❌';
    default: return '❓';
  }
}

// Main execution
performHealthCheck().catch(error => {
  console.error('❌ Unexpected error during health check:', error);
  process.exit(1);
});

#!/usr/bin/env node

/**
 * HLenergy Email Worker - Production Monitoring Script
 * 
 * This script provides comprehensive monitoring for the production email worker
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

class ProductionMonitor {
  constructor() {
    this.appName = 'hlenergy-email-worker';
    this.startTime = Date.now();
  }

  log(message, color = 'reset') {
    const timestamp = new Date().toISOString();
    console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
  }

  async checkPM2Status() {
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      const { stdout } = await execAsync(`pm2 jlist`);
      const processes = JSON.parse(stdout);
      
      const worker = processes.find(p => p.name === this.appName);
      
      if (!worker) {
        this.log(`❌ Worker process not found in PM2`, 'red');
        return { status: 'not_found', worker: null };
      }

      const status = {
        status: worker.pm2_env.status,
        pid: worker.pid,
        uptime: worker.pm2_env.pm_uptime,
        restarts: worker.pm2_env.restart_time,
        memory: worker.monit.memory,
        cpu: worker.monit.cpu,
        created_at: worker.pm2_env.created_at,
        unstable_restarts: worker.pm2_env.unstable_restarts
      };

      if (worker.pm2_env.status === 'online') {
        this.log(`✅ Worker is running (PID: ${worker.pid})`, 'green');
      } else {
        this.log(`⚠️ Worker status: ${worker.pm2_env.status}`, 'yellow');
      }

      return { status: 'found', worker: status };
    } catch (error) {
      this.log(`❌ Error checking PM2 status: ${error.message}`, 'red');
      return { status: 'error', error: error.message };
    }
  }

  async checkDatabaseConnection() {
    try {
      const mysql = require('mysql2/promise');
      
      const connection = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        timeout: 10000
      });

      await connection.execute('SELECT 1');
      await connection.end();
      
      this.log('✅ Database connection successful', 'green');
      return { status: 'connected' };
    } catch (error) {
      this.log(`❌ Database connection failed: ${error.message}`, 'red');
      return { status: 'failed', error: error.message };
    }
  }

  async checkEmailQueue() {
    try {
      const mysql = require('mysql2/promise');
      
      const connection = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME
      });

      // Get queue statistics
      const [queueStats] = await connection.execute(`
        SELECT 
          status,
          COUNT(*) as count
        FROM email_queue 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY status
      `);

      // Get oldest pending email
      const [oldestPending] = await connection.execute(`
        SELECT 
          id,
          created_at,
          TIMESTAMPDIFF(MINUTE, created_at, NOW()) as age_minutes
        FROM email_queue 
        WHERE status = 'pending'
        ORDER BY created_at ASC 
        LIMIT 1
      `);

      // Get recent failures
      const [recentFailures] = await connection.execute(`
        SELECT COUNT(*) as count
        FROM email_queue 
        WHERE status = 'failed' 
        AND failed_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
      `);

      await connection.end();

      const stats = {};
      queueStats.forEach(row => {
        stats[row.status] = row.count;
      });

      this.log('📊 Queue Statistics (24h):', 'cyan');
      Object.entries(stats).forEach(([status, count]) => {
        const color = status === 'failed' ? 'red' : status === 'sent' ? 'green' : 'yellow';
        this.log(`   ${status}: ${count}`, color);
      });

      // Check for issues
      const pending = stats.pending || 0;
      const failed = stats.failed || 0;
      const recentFailureCount = recentFailures[0]?.count || 0;

      if (pending > 100) {
        this.log(`⚠️ High queue backlog: ${pending} pending emails`, 'yellow');
      }

      if (failed > 10) {
        this.log(`⚠️ High failure rate: ${failed} failed emails in 24h`, 'yellow');
      }

      if (recentFailureCount > 5) {
        this.log(`❌ Recent failures: ${recentFailureCount} in last hour`, 'red');
      }

      if (oldestPending.length > 0 && oldestPending[0].age_minutes > 60) {
        this.log(`⚠️ Old pending email: ${oldestPending[0].age_minutes} minutes old`, 'yellow');
      }

      return { 
        status: 'success', 
        stats, 
        oldestPending: oldestPending[0] || null,
        recentFailures: recentFailureCount
      };
    } catch (error) {
      this.log(`❌ Error checking email queue: ${error.message}`, 'red');
      return { status: 'error', error: error.message };
    }
  }

  async checkSMTPConnection() {
    try {
      const nodemailer = require('nodemailer');
      
      const transporter = nodemailer.createTransporter({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT) || 587,
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASSWORD,
        },
        connectionTimeout: 10000,
        greetingTimeout: 10000,
        socketTimeout: 10000
      });

      await transporter.verify();
      transporter.close();
      
      this.log('✅ SMTP connection successful', 'green');
      return { status: 'connected' };
    } catch (error) {
      this.log(`❌ SMTP connection failed: ${error.message}`, 'red');
      return { status: 'failed', error: error.message };
    }
  }

  async checkLogFiles() {
    try {
      const logsDir = path.join(__dirname, '..', 'logs');
      
      if (!fs.existsSync(logsDir)) {
        this.log('⚠️ Logs directory not found', 'yellow');
        return { status: 'no_logs' };
      }

      const files = fs.readdirSync(logsDir);
      const logFiles = files.filter(f => f.endsWith('.log'));
      
      this.log(`📁 Found ${logFiles.length} log files`, 'cyan');
      
      // Check log file sizes
      for (const file of logFiles) {
        const filePath = path.join(logsDir, file);
        const stats = fs.statSync(filePath);
        const sizeMB = (stats.size / 1024 / 1024).toFixed(2);
        
        if (stats.size > 100 * 1024 * 1024) { // 100MB
          this.log(`⚠️ Large log file: ${file} (${sizeMB}MB)`, 'yellow');
        } else {
          this.log(`   ${file}: ${sizeMB}MB`, 'reset');
        }
      }

      return { status: 'success', files: logFiles };
    } catch (error) {
      this.log(`❌ Error checking log files: ${error.message}`, 'red');
      return { status: 'error', error: error.message };
    }
  }

  async checkHealthEndpoint() {
    try {
      const healthEnabled = process.env.HEALTH_CHECK_ENABLED === 'true';
      
      if (!healthEnabled) {
        this.log('ℹ️ Health check endpoint disabled', 'blue');
        return { status: 'disabled' };
      }

      const port = process.env.HEALTH_CHECK_PORT || 3002;
      const axios = require('axios');
      
      const response = await axios.get(`http://localhost:${port}/health`, {
        timeout: 5000
      });
      
      if (response.status === 200) {
        this.log('✅ Health endpoint responding', 'green');
        return { status: 'healthy', data: response.data };
      } else {
        this.log(`⚠️ Health endpoint returned ${response.status}`, 'yellow');
        return { status: 'unhealthy', code: response.status };
      }
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        this.log('❌ Health endpoint not responding (connection refused)', 'red');
      } else {
        this.log(`❌ Health endpoint error: ${error.message}`, 'red');
      }
      return { status: 'error', error: error.message };
    }
  }

  async generateReport() {
    this.log('🔍 HLenergy Email Worker - Production Monitor', 'bright');
    this.log('=' * 60, 'cyan');

    const results = {
      pm2: await this.checkPM2Status(),
      database: await this.checkDatabaseConnection(),
      queue: await this.checkEmailQueue(),
      smtp: await this.checkSMTPConnection(),
      logs: await this.checkLogFiles(),
      health: await this.checkHealthEndpoint()
    };

    this.log('=' * 60, 'cyan');

    // Overall health assessment
    const issues = [];
    
    if (results.pm2.status !== 'found' || results.pm2.worker?.status !== 'online') {
      issues.push('Worker process not running');
    }
    
    if (results.database.status !== 'connected') {
      issues.push('Database connection failed');
    }
    
    if (results.smtp.status !== 'connected') {
      issues.push('SMTP connection failed');
    }
    
    if (results.queue.status === 'success') {
      if (results.queue.recentFailures > 5) {
        issues.push('High recent failure rate');
      }
      if ((results.queue.stats.pending || 0) > 100) {
        issues.push('High queue backlog');
      }
    }

    if (issues.length === 0) {
      this.log('🎉 Overall Status: HEALTHY', 'green');
    } else {
      this.log('⚠️ Overall Status: ISSUES DETECTED', 'yellow');
      issues.forEach(issue => {
        this.log(`   - ${issue}`, 'red');
      });
    }

    return results;
  }
}

// Run monitor if called directly
if (require.main === module) {
  const monitor = new ProductionMonitor();
  
  monitor.generateReport()
    .then(() => {
      process.exit(0);
    })
    .catch(error => {
      console.error('Monitor failed:', error);
      process.exit(1);
    });
}

module.exports = ProductionMonitor;

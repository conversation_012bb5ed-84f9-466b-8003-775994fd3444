#!/usr/bin/env node

/**
 * Test Email Sending Script
 * Tests actual email sending functionality with the configured SMTP settings
 */

require('dotenv').config();
const nodemailer = require('nodemailer');
const { Sequelize, DataTypes } = require('sequelize');

console.log('📧 HLenergy Email Sending Test');
console.log('===============================');

// Database setup
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'mysql',
    logging: false,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  }
);

// Email Queue Model
const EmailQueue = sequelize.define('EmailQueue', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  to_email: {
    type: DataTypes.STRING,
    allowNull: false
  },
  subject: {
    type: DataTypes.STRING,
    allowNull: false
  },
  html_content: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  text_content: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('pending', 'sent', 'failed'),
    defaultValue: 'pending'
  },
  attempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  last_attempt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  sent_at: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'email_queue',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Email transporter setup
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT) || 587,
  secure: parseInt(process.env.SMTP_PORT) === 465,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASSWORD
  }
});

async function testEmailSending() {
  try {
    console.log('1️⃣  Verifying SMTP connection...');
    await transporter.verify();
    console.log('✅ SMTP connection verified');
    
    console.log('\n📋 Current SMTP Configuration:');
    console.log(`   Host: ${process.env.SMTP_HOST}`);
    console.log(`   Port: ${process.env.SMTP_PORT}`);
    console.log(`   User: ${process.env.SMTP_USER}`);
    console.log(`   From: ${process.env.SMTP_FROM_NAME} <${process.env.SMTP_FROM_EMAIL}>`);
    console.log(`   Secure: ${parseInt(process.env.SMTP_PORT) === 465 ? 'Yes (SSL)' : 'No (TLS)'}`);

    // Get test email address
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const testEmail = await new Promise((resolve) => {
      rl.question('\n📧 Enter test email address to send to: ', (email) => {
        rl.close();
        resolve(email);
      });
    });

    if (!testEmail || !testEmail.includes('@')) {
      console.log('❌ Invalid email address provided');
      process.exit(1);
    }

    console.log(`\n2️⃣  Preparing test email for: ${testEmail}`);

    // Create test email content
    const subject = 'HLenergy Email Worker Test - ' + new Date().toISOString();
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>HLenergy Email Test</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #02342b; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 10px; text-align: center; font-size: 12px; color: #666; }
          .success { color: #5cad64; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚀 HLenergy Email Worker</h1>
            <p>Email Service Test</p>
          </div>
          <div class="content">
            <h2 class="success">✅ Email Service Working!</h2>
            <p>This is a test email from the HLenergy email worker system.</p>
            <p><strong>Test Details:</strong></p>
            <ul>
              <li>Sent at: ${new Date().toLocaleString()}</li>
              <li>SMTP Host: ${process.env.SMTP_HOST}</li>
              <li>From: ${process.env.SMTP_FROM_EMAIL}</li>
              <li>Worker Status: Active</li>
            </ul>
            <p>If you received this email, your SMTP configuration is working correctly!</p>
          </div>
          <div class="footer">
            <p>HLenergy Email Worker System | Test Email</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const textContent = `
HLenergy Email Worker Test

✅ Email Service Working!

This is a test email from the HLenergy email worker system.

Test Details:
- Sent at: ${new Date().toLocaleString()}
- SMTP Host: ${process.env.SMTP_HOST}
- From: ${process.env.SMTP_FROM_EMAIL}
- Worker Status: Active

If you received this email, your SMTP configuration is working correctly!

HLenergy Email Worker System | Test Email
    `;

    // Test 1: Direct send (bypass queue)
    console.log('\n3️⃣  Testing direct email send...');
    
    const mailOptions = {
      from: {
        name: process.env.SMTP_FROM_NAME || 'HLenergy',
        address: process.env.SMTP_FROM_EMAIL || process.env.SMTP_USER
      },
      to: testEmail,
      subject: subject,
      html: htmlContent,
      text: textContent
    };

    const directResult = await transporter.sendMail(mailOptions);
    console.log('✅ Direct email sent successfully!');
    console.log(`   Message ID: ${directResult.messageId}`);
    if (directResult.preview) {
      console.log(`   Preview URL: ${directResult.preview}`);
    }

    // Test 2: Queue-based send
    console.log('\n4️⃣  Testing queue-based email send...');
    
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Add email to queue
    const queuedEmail = await EmailQueue.create({
      to_email: testEmail,
      subject: subject + ' (Queued)',
      html_content: htmlContent.replace('Direct Send', 'Queue-based Send'),
      text_content: textContent.replace('Direct Send', 'Queue-based Send'),
      status: 'pending'
    });

    console.log('✅ Email added to queue');
    console.log(`   Queue ID: ${queuedEmail.id}`);

    // Check queue status
    const queueCount = await EmailQueue.count({ where: { status: 'pending' } });
    console.log(`   Pending emails in queue: ${queueCount}`);

    console.log('\n📊 Test Summary:');
    console.log('================');
    console.log('✅ SMTP connection: Working');
    console.log('✅ Direct email send: Success');
    console.log('✅ Queue email add: Success');
    console.log('✅ Database connection: Working');
    
    console.log('\n🎉 Email sending test completed successfully!');
    console.log('\n📝 Next Steps:');
    console.log('   1. Check your email inbox for the test messages');
    console.log('   2. Start the email worker to process queued emails:');
    console.log('      npm start');
    console.log('   3. Monitor worker status:');
    console.log('      node scripts/status.js');

  } catch (error) {
    console.error('\n❌ Email sending test failed:');
    console.error('Error:', error.message);
    
    if (error.code) {
      console.error('Error Code:', error.code);
    }
    
    if (error.response) {
      console.error('SMTP Response:', error.response);
    }

    console.log('\n🔧 Troubleshooting Tips:');
    console.log('   1. Verify SMTP credentials in .env file');
    console.log('   2. Check if SMTP server allows connections from your IP');
    console.log('   3. Ensure firewall allows outbound connections on SMTP port');
    console.log('   4. Check if email provider requires app-specific passwords');
    
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run the test
testEmailSending();

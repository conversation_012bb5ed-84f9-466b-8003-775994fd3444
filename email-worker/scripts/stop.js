#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const pidFile = process.env.WORKER_PID_FILE || path.join(__dirname, '..', 'worker.pid');

function stopWorker() {
  console.log('🛑 Stopping HLenergy Email Worker');
  console.log('==================================');

  // Check if PID file exists
  if (!fs.existsSync(pidFile)) {
    console.log('❌ Worker is not running (no PID file found)');
    return false;
  }

  // Read PID from file
  let pid;
  try {
    pid = parseInt(fs.readFileSync(pidFile, 'utf8').trim());
  } catch (error) {
    console.log('❌ Error reading PID file:', error.message);
    return false;
  }

  console.log(`📍 Found worker PID: ${pid}`);

  // Try to stop the process gracefully
  try {
    console.log('📤 Sending SIGTERM signal...');
    process.kill(pid, 'SIGTERM');
    
    // Wait a bit for graceful shutdown
    setTimeout(() => {
      try {
        // Check if process is still running
        process.kill(pid, 0);
        console.log('⚠️  Process still running, sending SIGKILL...');
        process.kill(pid, 'SIGKILL');
        
        setTimeout(() => {
          try {
            process.kill(pid, 0);
            console.log('❌ Failed to stop process');
            return false;
          } catch (e) {
            console.log('✅ Worker stopped forcefully');
            cleanupPidFile();
            return true;
          }
        }, 2000);
        
      } catch (error) {
        if (error.code === 'ESRCH') {
          console.log('✅ Worker stopped gracefully');
          cleanupPidFile();
          return true;
        } else {
          console.log('❌ Error checking process:', error.message);
          return false;
        }
      }
    }, 5000); // Wait 5 seconds for graceful shutdown

  } catch (error) {
    if (error.code === 'ESRCH') {
      console.log('❌ Process not found (already stopped?)');
      cleanupPidFile();
      return false;
    } else {
      console.log('❌ Error stopping process:', error.message);
      return false;
    }
  }

  return true;
}

function cleanupPidFile() {
  try {
    if (fs.existsSync(pidFile)) {
      fs.unlinkSync(pidFile);
      console.log('🧹 Cleaned up PID file');
    }
  } catch (error) {
    console.log('⚠️  Failed to remove PID file:', error.message);
  }
}

// Main execution
const stopped = stopWorker();

if (!stopped) {
  console.log('\n💡 If the worker is stuck, you can:');
  console.log('   1. Find the process: ps aux | grep worker.js');
  console.log('   2. Kill manually: kill -9 <PID>');
  console.log('   3. Remove PID file: rm worker.pid');
}

console.log('\n==================================');

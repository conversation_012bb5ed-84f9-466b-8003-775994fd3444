#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const pidFile = process.env.WORKER_PID_FILE || path.join(__dirname, '..', 'worker.pid');

function checkWorkerStatus() {
  console.log('📧 HLenergy Email Worker Status');
  console.log('================================');

  // Check if PID file exists
  if (!fs.existsSync(pidFile)) {
    console.log('❌ Worker is not running (no PID file found)');
    console.log(`   Expected PID file: ${pidFile}`);
    return false;
  }

  // Read PID from file
  let pid;
  try {
    pid = parseInt(fs.readFileSync(pidFile, 'utf8').trim());
  } catch (error) {
    console.log('❌ Error reading PID file:', error.message);
    return false;
  }

  // Check if process is running
  try {
    process.kill(pid, 0); // Signal 0 checks if process exists without killing it
    console.log('✅ Worker is running');
    console.log(`   PID: ${pid}`);
    console.log(`   PID file: ${pidFile}`);
    
    // Try to get additional info
    try {
      const stats = fs.statSync(pidFile);
      console.log(`   Started: ${stats.mtime.toISOString()}`);
      console.log(`   Uptime: ${Math.floor((Date.now() - stats.mtime.getTime()) / 1000)}s`);
    } catch (e) {
      // Ignore errors getting file stats
    }

    return true;
  } catch (error) {
    if (error.code === 'ESRCH') {
      console.log('❌ Worker is not running (process not found)');
      console.log(`   Stale PID file found: ${pid}`);
      
      // Remove stale PID file
      try {
        fs.unlinkSync(pidFile);
        console.log('   Removed stale PID file');
      } catch (e) {
        console.log('   Failed to remove stale PID file:', e.message);
      }
    } else {
      console.log('❌ Error checking process:', error.message);
    }
    return false;
  }
}

function showLogInfo() {
  const logsDir = path.join(__dirname, '..', 'logs');
  
  console.log('\n📋 Log Files:');
  console.log('=============');
  
  if (!fs.existsSync(logsDir)) {
    console.log('   No logs directory found');
    return;
  }

  try {
    const files = fs.readdirSync(logsDir)
      .filter(file => file.endsWith('.log'))
      .sort();

    if (files.length === 0) {
      console.log('   No log files found');
      return;
    }

    files.forEach(file => {
      const filePath = path.join(logsDir, file);
      try {
        const stats = fs.statSync(filePath);
        const sizeKB = Math.round(stats.size / 1024);
        console.log(`   ${file} (${sizeKB}KB, modified: ${stats.mtime.toISOString()})`);
      } catch (e) {
        console.log(`   ${file} (error reading stats)`);
      }
    });

    console.log(`\n   View logs: npm run logs`);
    console.log(`   View errors: npm run logs:errors`);
  } catch (error) {
    console.log('   Error reading logs directory:', error.message);
  }
}

function showCommands() {
  console.log('\n🛠️  Available Commands:');
  console.log('======================');
  console.log('   npm start          - Start the worker');
  console.log('   npm run dev        - Start with auto-restart');
  console.log('   npm run stop       - Stop the worker');
  console.log('   npm run restart    - Restart the worker');
  console.log('   npm run status     - Show this status');
  console.log('   npm run health     - Health check');
  console.log('   npm run logs       - View logs');
  console.log('   npm run cleanup    - Manual cleanup');
  console.log('   npm test           - Test database connection');
}

// Main execution
const isRunning = checkWorkerStatus();
showLogInfo();
showCommands();

console.log('\n================================');

// Exit with appropriate code
process.exit(isRunning ? 0 : 1);

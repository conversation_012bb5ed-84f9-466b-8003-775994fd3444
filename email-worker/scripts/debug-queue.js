#!/usr/bin/env node

const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import worker components
const { EmailQueue, sequelize } = require('../worker.js');

async function debugQueue() {
  console.log('🔍 Email Queue Debug Information');
  console.log('================================');

  try {
    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connection successful');
    console.log('');

    // Get queue statistics
    console.log('📊 Queue Statistics:');
    const stats = await EmailQueue.getQueueStats();
    console.log(`   Pending: ${stats.queue.pending}`);
    console.log(`   Processing: ${stats.queue.processing}`);
    console.log(`   Sent: ${stats.queue.sent}`);
    console.log(`   Failed: ${stats.queue.failed}`);
    console.log(`   Total: ${stats.queue.total}`);
    console.log('');

    // Get recent emails
    console.log('📧 Recent Emails (last 10):');
    const recentEmails = await EmailQueue.findAll({
      order: [['created_at', 'DESC']],
      limit: 10,
      attributes: ['id', 'to_email', 'subject', 'status', 'attempts', 'created_at', 'scheduled_at']
    });

    if (recentEmails.length === 0) {
      console.log('   No emails found in queue');
    } else {
      recentEmails.forEach(email => {
        console.log(`   ID: ${email.id}`);
        console.log(`   To: ${email.to_email}`);
        console.log(`   Subject: ${email.subject}`);
        console.log(`   Status: ${email.status}`);
        console.log(`   Attempts: ${email.attempts}`);
        console.log(`   Created: ${email.created_at}`);
        console.log(`   Scheduled: ${email.scheduled_at || 'Now'}`);
        console.log('   ---');
      });
    }

    // Get pending emails specifically
    console.log('⏳ Pending Emails:');
    const { Op } = require('sequelize');
    const now = new Date();
    
    const pendingEmails = await EmailQueue.findAll({
      where: {
        status: 'pending',
        [Op.or]: [
          { scheduled_at: null },
          { scheduled_at: { [Op.lte]: now } },
        ],
      },
      order: [
        ['priority', 'DESC'],
        ['created_at', 'ASC'],
      ],
      limit: 5,
      attributes: ['id', 'to_email', 'subject', 'priority', 'created_at', 'scheduled_at']
    });

    if (pendingEmails.length === 0) {
      console.log('   No pending emails ready for processing');
      console.log('   This is why the worker is not processing anything!');
    } else {
      console.log(`   Found ${pendingEmails.length} pending emails ready for processing:`);
      pendingEmails.forEach(email => {
        console.log(`   - ID: ${email.id}, To: ${email.to_email}, Priority: ${email.priority}`);
      });
    }
    console.log('');

    // Check for stuck emails
    console.log('🔒 Stuck Emails (processing > 10 minutes):');
    const stuckThreshold = new Date(Date.now() - 10 * 60 * 1000);
    const stuckEmails = await EmailQueue.findAll({
      where: {
        status: 'processing',
        updated_at: { [Op.lt]: stuckThreshold },
      },
      attributes: ['id', 'to_email', 'subject', 'attempts', 'updated_at']
    });

    if (stuckEmails.length === 0) {
      console.log('   No stuck emails found');
    } else {
      console.log(`   Found ${stuckEmails.length} stuck emails:`);
      stuckEmails.forEach(email => {
        console.log(`   - ID: ${email.id}, To: ${email.to_email}, Last updated: ${email.updated_at}`);
      });
    }
    console.log('');

    // Check scheduled emails
    console.log('⏰ Scheduled Emails (future):');
    const scheduledEmails = await EmailQueue.findAll({
      where: {
        status: 'pending',
        scheduled_at: { [Op.gt]: now },
      },
      order: [['scheduled_at', 'ASC']],
      limit: 5,
      attributes: ['id', 'to_email', 'subject', 'scheduled_at']
    });

    if (scheduledEmails.length === 0) {
      console.log('   No emails scheduled for future processing');
    } else {
      console.log(`   Found ${scheduledEmails.length} emails scheduled for later:`);
      scheduledEmails.forEach(email => {
        console.log(`   - ID: ${email.id}, To: ${email.to_email}, Scheduled: ${email.scheduled_at}`);
      });
    }
    console.log('');

    // Recommendations
    console.log('💡 Recommendations:');
    if (stats.queue.total === 0) {
      console.log('   • No emails in queue - submit a contact form to test');
      console.log('   • Or insert a test email manually');
    } else if (stats.queue.pending === 0) {
      console.log('   • All emails have been processed or are scheduled for later');
      console.log('   • Check if main backend is still adding emails to queue');
    } else if (pendingEmails.length === 0) {
      console.log('   • Pending emails exist but none are ready for processing');
      console.log('   • Check scheduled_at times');
    } else {
      console.log('   • Emails are ready for processing - check worker logs for errors');
      console.log('   • Verify SMTP configuration with: npm test');
    }

    if (stuckEmails.length > 0) {
      console.log('   • Reset stuck emails by restarting the worker');
    }

  } catch (error) {
    console.log('❌ Debug failed:', error.message);
    console.log('');
    console.log('💡 Troubleshooting:');
    console.log('   • Check database connection settings');
    console.log('   • Ensure email_queue table exists');
    console.log('   • Verify .env configuration');
  } finally {
    // Close database connection
    try {
      await sequelize.close();
    } catch (e) {
      // Ignore errors during cleanup
    }
  }

  console.log('');
  console.log(`🕐 Debug completed at: ${new Date().toISOString()}`);
  console.log('================================');
}

// Main execution
debugQueue().catch(error => {
  console.error('❌ Unexpected error during debug:', error);
  process.exit(1);
});

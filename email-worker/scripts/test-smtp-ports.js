#!/usr/bin/env node

/**
 * Test SMTP port connectivity for SecureMail Pro
 */

const net = require('net');

const host = 'smtp-pt.securemail.pro';
const ports = [25, 587, 465, 993, 995]; // Common email ports

console.log(`🔍 Testing SMTP port connectivity for ${host}...\n`);

async function testPort(host, port) {
  return new Promise((resolve) => {
    const socket = new net.Socket();
    const timeout = 10000; // 10 seconds
    
    let result = {
      port: port,
      open: false,
      error: null,
      time: null
    };
    
    const startTime = Date.now();
    
    socket.setTimeout(timeout);
    
    socket.on('connect', () => {
      result.open = true;
      result.time = Date.now() - startTime;
      socket.destroy();
      resolve(result);
    });
    
    socket.on('timeout', () => {
      result.error = 'Connection timeout';
      socket.destroy();
      resolve(result);
    });
    
    socket.on('error', (error) => {
      result.error = error.message;
      socket.destroy();
      resolve(result);
    });
    
    socket.connect(port, host);
  });
}

async function testAllPorts() {
  console.log('Port | Status | Time | Description');
  console.log('-----|--------|------|------------');
  
  for (const port of ports) {
    const result = await testPort(host, port);
    
    const status = result.open ? '✅ OPEN' : '❌ CLOSED';
    const time = result.time ? `${result.time}ms` : 'N/A';
    const error = result.error ? `(${result.error})` : '';
    
    let description = '';
    switch (port) {
      case 25:
        description = 'SMTP (Plain/STARTTLS)';
        break;
      case 587:
        description = 'SMTP (STARTTLS) - Recommended';
        break;
      case 465:
        description = 'SMTP (SSL/TLS)';
        break;
      case 993:
        description = 'IMAP (SSL)';
        break;
      case 995:
        description = 'POP3 (SSL)';
        break;
    }
    
    console.log(`${port.toString().padEnd(4)} | ${status.padEnd(6)} | ${time.padEnd(4)} | ${description} ${error}`);
  }
  
  console.log('\n💡 Recommendations:');
  
  const openPorts = ports.filter(async (port) => {
    const result = await testPort(host, port);
    return result.open;
  });
  
  // Test each port again to get final results
  const results = await Promise.all(ports.map(port => testPort(host, port)));
  const openSmtpPorts = results.filter(r => r.open && [25, 587, 465].includes(r.port));
  
  if (openSmtpPorts.length === 0) {
    console.log('❌ No SMTP ports are accessible');
    console.log('   • Check your internet connection');
    console.log('   • Verify the SMTP server address');
    console.log('   • Contact your email provider');
    console.log('   • Check if you\'re behind a firewall blocking SMTP ports');
  } else {
    console.log('✅ Available SMTP ports found:');
    openSmtpPorts.forEach(result => {
      if (result.port === 587) {
        console.log(`   • Port ${result.port}: Use SMTP_PORT=587 and SMTP_SECURE=false`);
      } else if (result.port === 465) {
        console.log(`   • Port ${result.port}: Use SMTP_PORT=465 and SMTP_SECURE=true`);
      } else if (result.port === 25) {
        console.log(`   • Port ${result.port}: Use SMTP_PORT=25 and SMTP_SECURE=false`);
      }
    });
  }
  
  console.log('\n🔧 Next steps:');
  console.log('1. Update your .env file with an open port');
  console.log('2. Run: npm run debug:smtp');
  console.log('3. If still failing, contact your email provider');
}

testAllPorts().catch(console.error);

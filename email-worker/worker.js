#!/usr/bin/env node

/**
 * HLenergy Standalone Email Worker
 * 
 * This is a standalone email worker that processes the email queue
 * independently from the main backend application.
 */

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Setup logging
const winston = require('winston');
require('winston-daily-rotate-file');

// Database logging configuration
const logToDatabase = process.env.LOG_TO_DATABASE === 'true';
const logToFiles = process.env.LOG_TO_FILES !== 'false'; // Default to true

// Custom database transport
class DatabaseTransport extends winston.Transport {
  constructor(options) {
    super(options);
    this.name = 'database';
  }

  async log(info, callback) {
    if (!logToDatabase) {
      return callback(null, true);
    }

    try {
      // Extract metadata
      const { level, message, timestamp, stack, emailId, duration, ...metadata } = info;

      // Get memory usage
      const memoryUsage = process.memoryUsage();

      await WorkerLog.create({
        level: level,
        message: message,
        metadata: Object.keys(metadata).length > 0 ? metadata : null,
        email_id: emailId || null,
        error_stack: stack || null,
        duration_ms: duration || null,
        memory_usage: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024 * 100) / 100, // MB
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024 * 100) / 100, // MB
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024 * 100) / 100, // MB
          external: Math.round(memoryUsage.external / 1024 / 1024 * 100) / 100, // MB
        },
        worker_instance: `worker-${process.pid}`,
      });

      callback(null, true);
    } catch (error) {
      // Don't let database logging errors crash the worker
      console.error('Database logging error:', error.message);
      callback(null, true);
    }
  }
}

// Create transports array
const transports = [
  // Console output
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  })
];

// Add file transports if enabled
if (logToFiles) {
  transports.push(
    // Rotating log file
    new winston.transports.DailyRotateFile({
      filename: path.join(logsDir, 'email-worker-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: process.env.LOG_FILE_MAX_SIZE || '20m',
      maxFiles: process.env.LOG_FILE_MAX_FILES || '14d',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),
    // Error log file
    new winston.transports.DailyRotateFile({
      filename: path.join(logsDir, 'email-worker-error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      maxSize: process.env.LOG_FILE_MAX_SIZE || '20m',
      maxFiles: process.env.LOG_FILE_MAX_FILES || '14d',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  );
}

// Add database transport if enabled
if (logToDatabase) {
  transports.push(new DatabaseTransport());
}

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: transports
});

// Database setup
const { Sequelize, DataTypes } = require('sequelize');

const sequelize = new Sequelize({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  database: process.env.DB_NAME || 'hlenergy_db',
  username: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  dialect: 'mysql',
  logging: (msg) => logger.debug(msg),
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000
  }
});

// Email service setup
const nodemailer = require('nodemailer');

const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT) || 587,
  secure: parseInt(process.env.SMTP_PORT) === 465, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASSWORD
  }
});

// Email service
const emailService = {
  async sendEmail(to, subject, htmlContent, textContent) {
    try {
      const mailOptions = {
        from: {
          name: process.env.SMTP_FROM_NAME || 'HLenergy',
          address: process.env.SMTP_FROM_EMAIL || process.env.SMTP_USER
        },
        to: to,
        subject: subject,
        html: htmlContent,
        text: textContent || htmlContent.replace(/<[^>]*>/g, '')
      };

      const result = await transporter.sendMail(mailOptions);
      
      return {
        success: true,
        messageId: result.messageId,
        preview: nodemailer.getTestMessageUrl(result)
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
};

// Worker Log model for database logging
const WorkerLog = sequelize.define('WorkerLog', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  worker_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'email-worker',
  },
  worker_instance: {
    type: DataTypes.STRING(100),
    allowNull: false,
    defaultValue: () => `worker-${process.pid}`,
  },
  level: {
    type: DataTypes.ENUM('debug', 'info', 'warn', 'error'),
    allowNull: false,
    defaultValue: 'info',
  },
  message: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
  },
  email_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'email_queue',
      key: 'id',
    },
  },
  error_stack: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  duration_ms: {
    type: DataTypes.INTEGER,
    allowNull: true,
  },
  memory_usage: {
    type: DataTypes.JSON,
    allowNull: true,
  },
  ip_address: {
    type: DataTypes.STRING(45),
    allowNull: true,
  },
}, {
  tableName: 'worker_logs',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['worker_type', 'created_at'],
    },
    {
      fields: ['level', 'created_at'],
    },
    {
      fields: ['email_id'],
    },
    {
      fields: ['worker_instance', 'created_at'],
    },
  ],
});

// Load WorkerStatus model
const WorkerStatus = sequelize.define('WorkerStatus', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  worker_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'email',
    unique: true,
  },
  status: {
    type: DataTypes.ENUM('starting', 'running', 'stopping', 'stopped', 'error'),
    allowNull: false,
    defaultValue: 'stopped',
  },
  pid: {
    type: DataTypes.INTEGER,
    allowNull: true,
  },
  started_at: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  stopped_at: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  last_heartbeat: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  uptime_seconds: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
  },
  processed_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
  },
  error_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
  },
  last_error: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
  },
}, {
  tableName: 'worker_status',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

// Load EmailQueue model (simplified version)
const EmailQueue = sequelize.define('EmailQueue', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
  },
  to_email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      isEmail: true,
    },
  },
  to_name: {
    type: DataTypes.STRING(100),
    allowNull: true,
  },
  from_email: {
    type: DataTypes.STRING(255),
    allowNull: true,
  },
  from_name: {
    type: DataTypes.STRING(100),
    allowNull: true,
  },
  subject: {
    type: DataTypes.STRING(255),
    allowNull: false,
  },
  html_content: {
    type: DataTypes.TEXT('long'),
    allowNull: false,
  },
  text_content: {
    type: DataTypes.TEXT('long'),
    allowNull: true,
  },
  email_type: {
    type: DataTypes.ENUM('verification', 'password_reset', 'welcome', 'notification', 'marketing'),
    allowNull: false,
  },
  priority: {
    type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
    allowNull: false,
    defaultValue: 'normal',
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'sent', 'failed', 'cancelled'),
    allowNull: false,
    defaultValue: 'pending',
  },
  scheduled_at: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  sent_at: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  failed_at: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  attempts: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
  },
  max_attempts: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: parseInt(process.env.EMAIL_MAX_RETRIES) || 3,
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  error_stack: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
  },
  template_data: {
    type: DataTypes.JSON,
    allowNull: true,
  },
  ip_address: {
    type: DataTypes.STRING(45),
    allowNull: true,
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
}, {
  tableName: 'email_queue',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

// Add instance methods
EmailQueue.prototype.markAsProcessing = async function() {
  this.status = 'processing';
  this.attempts += 1;
  await this.save();
};

EmailQueue.prototype.markAsSent = async function() {
  this.status = 'sent';
  this.sent_at = new Date();
  await this.save();
};

EmailQueue.prototype.markAsFailed = async function(error) {
  this.status = 'failed';
  this.failed_at = new Date();
  this.error_message = error.message;
  this.error_stack = error.stack;
  await this.save();
};

EmailQueue.prototype.canRetry = function() {
  return this.attempts < this.max_attempts;
};

EmailQueue.prototype.scheduleRetry = async function(delayMinutes = 5) {
  if (this.canRetry()) {
    this.status = 'pending';
    this.scheduled_at = new Date(Date.now() + delayMinutes * 60 * 1000);
    await this.save();
    return true;
  }
  return false;
};

// Add static methods
EmailQueue.getNextBatch = async function(batchSize = 10) {
  const { Op } = require('sequelize');
  const now = new Date();
  
  return await EmailQueue.findAll({
    where: {
      status: 'pending',
      [Op.or]: [
        { scheduled_at: null },
        { scheduled_at: { [Op.lte]: now } },
      ],
    },
    order: [
      ['priority', 'DESC'], // urgent, high, normal, low
      ['created_at', 'ASC'], // FIFO within same priority
    ],
    limit: batchSize,
  });
};

EmailQueue.getQueueStats = async function() {
  const { Op } = require('sequelize');
  const now = new Date();
  const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);

  const [
    totalPending,
    totalProcessing,
    totalSent,
    totalFailed,
    recentSent,
    recentFailed,
  ] = await Promise.all([
    EmailQueue.count({ where: { status: 'pending' } }),
    EmailQueue.count({ where: { status: 'processing' } }),
    EmailQueue.count({ where: { status: 'sent' } }),
    EmailQueue.count({ where: { status: 'failed' } }),
    EmailQueue.count({ 
      where: { 
        status: 'sent',
        sent_at: { [Op.gte]: last24h },
      },
    }),
    EmailQueue.count({ 
      where: { 
        status: 'failed',
        failed_at: { [Op.gte]: last24h },
      },
    }),
  ]);

  return {
    queue: {
      pending: totalPending,
      processing: totalProcessing,
      sent: totalSent,
      failed: totalFailed,
      total: totalPending + totalProcessing + totalSent + totalFailed,
    },
    last24h: {
      sent: recentSent,
      failed: recentFailed,
      successRate: recentSent + recentFailed > 0 
        ? ((recentSent / (recentSent + recentFailed)) * 100).toFixed(2) + '%'
        : 'N/A',
    },
  };
};

EmailQueue.cleanupOldEmails = async function(daysOld = 30) {
  const { Op } = require('sequelize');
  const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);
  
  const deleted = await EmailQueue.destroy({
    where: {
      status: { [Op.in]: ['sent', 'failed'] },
      created_at: { [Op.lt]: cutoffDate },
    },
  });
  
  return deleted;
};

// Add static methods to WorkerStatus
WorkerStatus.updateStatus = async function(status, data = {}) {
  const now = new Date();
  const updateData = {
    status,
    last_heartbeat: now,
    pid: process.pid,
    ...data,
  };

  // Set specific timestamps based on status
  if (status === 'starting' || status === 'running') {
    if (!data.started_at) {
      updateData.started_at = now;
    }
    updateData.stopped_at = null;
  } else if (status === 'stopped' || status === 'error') {
    updateData.stopped_at = now;
  }

  const [worker, created] = await this.findOrCreate({
    where: { worker_type: 'email' },
    defaults: {
      worker_type: 'email',
      status: 'stopped',
      metadata: {
        version: '1.0.0',
        location: 'backend/email-worker/',
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch
      },
      ...updateData,
    },
  });

  if (!created) {
    // Calculate uptime if worker is running
    if (status === 'running' && worker.started_at) {
      updateData.uptime_seconds = Math.floor((now - new Date(worker.started_at)) / 1000);
    }
    await worker.update(updateData);
  }

  return worker;
};

WorkerStatus.sendHeartbeat = async function(data = {}) {
  try {
    const worker = await this.findOne({
      where: { worker_type: 'email' },
    });

    if (worker && worker.status === 'running') {
      const now = new Date();
      const updateData = {
        last_heartbeat: now,
        ...data,
      };

      // Calculate uptime
      if (worker.started_at) {
        updateData.uptime_seconds = Math.floor((now - new Date(worker.started_at)) / 1000);
      }

      await worker.update(updateData);
      return worker;
    }
  } catch (error) {
    logger.error('Failed to send heartbeat', { error: error.message });
  }
  return null;
};

// Standalone Email Worker Class
class StandaloneEmailWorker {
  constructor() {
    this.isRunning = false;
    this.intervalId = null;
    this.cleanupIntervalId = null;
    this.batchSize = parseInt(process.env.EMAIL_BATCH_SIZE) || 10;
    this.processingInterval = parseInt(process.env.EMAIL_PROCESSING_INTERVAL) || 30000; // 30 seconds
    this.retryDelays = (process.env.EMAIL_RETRY_DELAYS || '5,15,60').split(',').map(d => parseInt(d.trim())); // minutes
    this.cleanupInterval = parseInt(process.env.CLEANUP_INTERVAL_HOURS) || 24; // hours
    this.cleanupDays = parseInt(process.env.CLEANUP_OLD_EMAILS_DAYS) || 30;
    this.pidFile = process.env.WORKER_PID_FILE || path.join(__dirname, 'worker.pid');
    this.stats = {
      processed: 0,
      sent: 0,
      failed: 0,
      retried: 0,
      startTime: null,
    };
  }

  /**
   * Initialize the worker
   */
  async initialize() {
    try {
      // Update worker status to starting
      await WorkerStatus.updateStatus('starting', {
        metadata: {
          version: '1.0.0',
          location: 'backend/email-worker/',
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch
        }
      });

      // Test database connection
      await sequelize.authenticate();
      logger.info('Database connection established successfully', {
        type: 'worker_startup',
        database: {
          host: process.env.DB_HOST,
          name: process.env.DB_NAME,
          dialect: 'mysql'
        }
      });

      // Sync database models (create tables if they don't exist)
      if (logToDatabase) {
        await WorkerLog.sync({ alter: false });
        logger.info('Worker log table synchronized', {
          type: 'worker_startup',
          table: 'worker_logs'
        });
      }

      // Sync WorkerStatus table
      await WorkerStatus.sync({ alter: false });

      // Test email service
      await transporter.verify();
      logger.info('Email service connection verified', {
        type: 'worker_startup',
        smtp: {
          host: process.env.SMTP_HOST,
          port: process.env.SMTP_PORT,
          secure: parseInt(process.env.SMTP_PORT) === 465
        }
      });

      // Write PID file
      fs.writeFileSync(this.pidFile, process.pid.toString());
      logger.info('Worker PID file created', {
        type: 'worker_startup',
        pid: process.pid,
        pidFile: this.pidFile
      });

      // Log worker startup
      logger.info('Email worker initialized successfully', {
        type: 'worker_startup',
        config: {
          batchSize: this.batchSize,
          processingInterval: this.processingInterval,
          retryDelays: this.retryDelays,
          cleanupInterval: this.cleanupInterval,
          logToDatabase: logToDatabase,
          logToFiles: logToFiles
        },
        environment: {
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch,
          pid: process.pid
        }
      });

      return true;
    } catch (error) {
      logger.error('Worker initialization failed', {
        type: 'worker_startup',
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Start the email worker
   */
  async start() {
    if (this.isRunning) {
      logger.warn('Email worker is already running');
      return;
    }

    const initialized = await this.initialize();
    if (!initialized) {
      logger.error('Failed to initialize worker');
      process.exit(1);
    }

    this.isRunning = true;
    this.stats.startTime = new Date();

    // Update worker status to running
    await WorkerStatus.updateStatus('running', {
      started_at: this.stats.startTime,
      processed_count: 0,
      error_count: 0
    });

    logger.info('Starting standalone email worker', {
      type: 'email_worker',
      batchSize: this.batchSize,
      processingInterval: this.processingInterval,
      retryDelays: this.retryDelays,
      cleanupInterval: this.cleanupInterval,
      pid: process.pid,
    });

    // Process immediately, then set interval
    this.processQueue();
    this.intervalId = setInterval(() => {
      this.processQueue();
    }, this.processingInterval);

    // Setup cleanup interval
    this.cleanupIntervalId = setInterval(() => {
      this.performCleanup();
    }, this.cleanupInterval * 60 * 60 * 1000); // Convert hours to milliseconds

    // Setup heartbeat interval (every 30 seconds)
    this.heartbeatIntervalId = setInterval(() => {
      this.sendHeartbeat();
    }, 30000);

    console.log('📧 Standalone Email Worker Started');
    console.log(`   PID: ${process.pid}`);
    console.log(`   Batch size: ${this.batchSize}`);
    console.log(`   Processing interval: ${this.processingInterval / 1000}s`);
    console.log(`   Retry delays: ${this.retryDelays.join(', ')} minutes`);
    console.log(`   Cleanup interval: ${this.cleanupInterval} hours`);
  }

  /**
   * Send heartbeat to update worker status
   */
  async sendHeartbeat() {
    try {
      await WorkerStatus.sendHeartbeat({
        processed_count: this.stats.processed,
        error_count: this.stats.failed
      });
    } catch (error) {
      logger.error('Failed to send heartbeat', { error: error.message });
    }
  }

  /**
   * Stop the email worker
   */
  async stop() {
    if (!this.isRunning) {
      logger.warn('Email worker is not running');
      return;
    }

    // Update worker status to stopping
    try {
      await WorkerStatus.updateStatus('stopping');
    } catch (error) {
      logger.error('Failed to update worker status to stopping', { error: error.message });
    }

    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    if (this.cleanupIntervalId) {
      clearInterval(this.cleanupIntervalId);
      this.cleanupIntervalId = null;
    }

    if (this.heartbeatIntervalId) {
      clearInterval(this.heartbeatIntervalId);
      this.heartbeatIntervalId = null;
    }

    // Update worker status to stopped
    try {
      await WorkerStatus.updateStatus('stopped', {
        processed_count: this.stats.processed,
        error_count: this.stats.failed
      });
    } catch (error) {
      logger.error('Failed to update worker status to stopped', { error: error.message });
    }

    // Remove PID file
    if (fs.existsSync(this.pidFile)) {
      fs.unlinkSync(this.pidFile);
    }

    // Close database connection
    await sequelize.close();

    logger.info('Standalone email worker stopped', {
      type: 'email_worker',
      stats: this.getStats(),
    });

    console.log('📧 Standalone Email Worker Stopped');
  }

  /**
   * Process the email queue
   */
  async processQueue() {
    if (!this.isRunning) {
      return;
    }

    try {
      // Get next batch of emails to process
      const emails = await EmailQueue.getNextBatch(this.batchSize);

      if (emails.length === 0) {
        return; // No emails to process
      }

      logger.info('Processing email batch', {
        type: 'email_worker',
        batchSize: emails.length,
      });

      // Process each email
      const promises = emails.map(email => this.processEmail(email));
      await Promise.allSettled(promises);

      this.stats.processed += emails.length;

    } catch (error) {
      logger.error('Email queue processing error', {
        type: 'email_worker',
        error: error.message,
        stack: error.stack,
      });
    }
  }

  /**
   * Process a single email
   */
  async processEmail(email) {
    const startTime = Date.now();

    try {
      // Mark as processing
      await email.markAsProcessing();

      logger.info('Processing email', {
        type: 'email_worker',
        emailId: email.id,
        to: email.to_email,
        subject: email.subject,
        emailType: email.email_type,
        attempt: email.attempts,
        priority: email.priority,
      });

      // Send the email
      const result = await emailService.sendEmail(
        email.to_email,
        email.subject,
        email.html_content,
        email.text_content
      );

      const duration = Date.now() - startTime;

      if (result.success) {
        // Mark as sent
        await email.markAsSent();
        this.stats.sent++;

        logger.info('Email sent successfully', {
          type: 'email_worker',
          emailId: email.id,
          to: email.to_email,
          subject: email.subject,
          emailType: email.email_type,
          messageId: result.messageId,
          preview: result.preview,
          duration: duration,
          attempt: email.attempts,
        });

      } else {
        // Handle failure
        await this.handleEmailFailure(email, new Error(result.error), duration);
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      await this.handleEmailFailure(email, error, duration);
    }
  }

  /**
   * Handle email sending failure
   */
  async handleEmailFailure(email, error, duration = null) {
    try {
      logger.error('Email sending failed', {
        type: 'email_worker',
        emailId: email.id,
        to: email.to_email,
        subject: email.subject,
        emailType: email.email_type,
        attempt: email.attempts,
        error: error.message,
        stack: error.stack,
        duration: duration,
      });

      // Check if we can retry
      if (email.canRetry()) {
        // Schedule retry with exponential backoff
        const retryDelay = this.retryDelays[email.attempts - 1] || 60;
        const scheduled = await email.scheduleRetry(retryDelay);

        if (scheduled) {
          this.stats.retried++;
          logger.info('Email scheduled for retry', {
            type: 'email_worker',
            emailId: email.id,
            to: email.to_email,
            subject: email.subject,
            emailType: email.email_type,
            nextAttempt: email.attempts + 1,
            retryDelay: retryDelay,
            scheduledAt: email.scheduled_at,
          });
        } else {
          await email.markAsFailed(error);
          this.stats.failed++;

          logger.error('Email retry scheduling failed', {
            type: 'email_worker',
            emailId: email.id,
            to: email.to_email,
            subject: email.subject,
            emailType: email.email_type,
            attempts: email.attempts,
          });
        }
      } else {
        // Max attempts reached, mark as failed
        await email.markAsFailed(error);
        this.stats.failed++;

        logger.error('Email failed permanently', {
          type: 'email_worker',
          emailId: email.id,
          to: email.to_email,
          subject: email.subject,
          emailType: email.email_type,
          attempts: email.attempts,
          maxAttempts: email.max_attempts,
          finalError: error.message,
        });
      }

    } catch (handlingError) {
      logger.error('Error handling email failure', {
        type: 'email_worker',
        emailId: email.id,
        originalError: error.message,
        handlingError: handlingError.message,
        stack: handlingError.stack,
      });
    }
  }

  /**
   * Perform cleanup of old emails and logs
   */
  async performCleanup() {
    try {
      // Clean up old emails
      const deletedEmails = await EmailQueue.cleanupOldEmails(this.cleanupDays);

      if (deletedEmails > 0) {
        logger.info('Cleaned up old emails', {
          type: 'cleanup',
          deleted: deletedEmails,
          daysOld: this.cleanupDays,
        });
      }

      // Clean up old worker logs if database logging is enabled
      if (logToDatabase) {
        const deletedLogs = await this.cleanupOldWorkerLogs(this.cleanupDays);

        if (deletedLogs > 0) {
          logger.info('Cleaned up old worker logs', {
            type: 'cleanup',
            deleted: deletedLogs,
            daysOld: this.cleanupDays,
          });
        }
      }

    } catch (error) {
      logger.error('Error during cleanup', {
        type: 'cleanup',
        error: error.message,
        stack: error.stack,
      });
    }
  }

  /**
   * Clean up old worker logs
   */
  async cleanupOldWorkerLogs(daysOld = 30) {
    try {
      const { Op } = require('sequelize');
      const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);

      const deleted = await WorkerLog.destroy({
        where: {
          created_at: { [Op.lt]: cutoffDate },
        },
      });

      return deleted;
    } catch (error) {
      logger.error('Error cleaning up worker logs', {
        type: 'cleanup',
        error: error.message,
      });
      return 0;
    }
  }

  /**
   * Get worker statistics
   */
  getStats() {
    const uptime = this.stats.startTime
      ? Math.floor((Date.now() - this.stats.startTime.getTime()) / 1000)
      : 0;

    return {
      isRunning: this.isRunning,
      pid: process.pid,
      uptime: uptime,
      uptimeFormatted: this.formatUptime(uptime),
      processed: this.stats.processed,
      sent: this.stats.sent,
      failed: this.stats.failed,
      retried: this.stats.retried,
      successRate: this.stats.processed > 0
        ? ((this.stats.sent / this.stats.processed) * 100).toFixed(2) + '%'
        : 'N/A',
      config: {
        batchSize: this.batchSize,
        processingInterval: this.processingInterval,
        retryDelays: this.retryDelays,
        cleanupInterval: this.cleanupInterval,
      },
    };
  }

  /**
   * Get queue status
   */
  async getQueueStatus() {
    const queueStats = await EmailQueue.getQueueStats();
    const workerStats = this.getStats();

    return {
      worker: workerStats,
      queue: queueStats,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Format uptime in human readable format
   */
  formatUptime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    return `${days}d ${hours}h ${minutes}m ${secs}s`;
  }

  /**
   * Health check for the worker
   */
  async healthCheck() {
    try {
      const queueStatus = await this.getQueueStatus();
      const isHealthy = this.isRunning && queueStatus.queue.queue.pending < 1000; // Arbitrary threshold

      const healthData = {
        status: isHealthy ? 'healthy' : 'warning',
        worker: queueStatus.worker,
        queue: queueStatus.queue,
        database: await this.checkDatabaseHealth(),
        email: await this.checkEmailHealth(),
        logging: await this.getLoggingStatus(),
        issues: isHealthy ? [] : [
          !this.isRunning ? 'Worker not running' : null,
          queueStatus.queue.queue.pending >= 1000 ? 'High queue backlog' : null,
        ].filter(Boolean),
      };

      // Log health check
      logger.debug('Health check performed', {
        type: 'health_check',
        status: healthData.status,
        issues: healthData.issues,
      });

      return healthData;
    } catch (error) {
      logger.error('Health check failed', {
        type: 'health_check',
        error: error.message,
        stack: error.stack,
      });

      return {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Get logging status and statistics
   */
  async getLoggingStatus() {
    try {
      const status = {
        fileLogging: logToFiles,
        databaseLogging: logToDatabase,
        logLevel: process.env.LOG_LEVEL || 'info',
      };

      if (logToDatabase) {
        // Get recent log statistics
        const { Op } = require('sequelize');
        const last24h = new Date(Date.now() - 24 * 60 * 60 * 1000);

        const [
          totalLogs,
          recentLogs,
          errorLogs,
          workerLogs
        ] = await Promise.all([
          WorkerLog.count(),
          WorkerLog.count({ where: { created_at: { [Op.gte]: last24h } } }),
          WorkerLog.count({ where: { level: 'error', created_at: { [Op.gte]: last24h } } }),
          WorkerLog.count({ where: { worker_instance: `worker-${process.pid}` } })
        ]);

        status.database = {
          totalLogs,
          last24h: recentLogs,
          errors24h: errorLogs,
          thisWorkerLogs: workerLogs,
        };
      }

      if (logToFiles) {
        // Get log file information
        const logFiles = [];
        try {
          const files = fs.readdirSync(logsDir).filter(f => f.endsWith('.log'));
          for (const file of files) {
            const filePath = path.join(logsDir, file);
            const stats = fs.statSync(filePath);
            logFiles.push({
              name: file,
              size: Math.round(stats.size / 1024), // KB
              modified: stats.mtime,
            });
          }
        } catch (e) {
          // Ignore file system errors
        }

        status.files = {
          directory: logsDir,
          files: logFiles,
        };
      }

      return status;
    } catch (error) {
      return {
        error: error.message,
        fileLogging: logToFiles,
        databaseLogging: logToDatabase,
      };
    }
  }

  /**
   * Check database health
   */
  async checkDatabaseHealth() {
    try {
      await sequelize.authenticate();
      return { status: 'healthy', message: 'Database connection OK' };
    } catch (error) {
      return { status: 'error', message: error.message };
    }
  }

  /**
   * Check email service health
   */
  async checkEmailHealth() {
    try {
      await transporter.verify();
      return { status: 'healthy', message: 'Email service OK' };
    } catch (error) {
      return { status: 'error', message: error.message };
    }
  }
}

// Main execution
if (require.main === module) {
  const worker = new StandaloneEmailWorker();

  // Graceful shutdown handling
  const gracefulShutdown = async (signal) => {
    console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);

    try {
      await worker.stop();
      console.log('✅ Worker shut down gracefully');
      process.exit(0);
    } catch (error) {
      console.error('❌ Error during shutdown:', error);
      process.exit(1);
    }
  };

  // Handle different termination signals
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));   // Ctrl+C
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM')); // Termination request
  process.on('SIGQUIT', () => gracefulShutdown('SIGQUIT')); // Quit request

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception', { error: error.message, stack: error.stack });
    console.error('❌ Uncaught Exception:', error);
    gracefulShutdown('UNCAUGHT_EXCEPTION');
  });

  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection', { reason, promise });
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    gracefulShutdown('UNHANDLED_REJECTION');
  });

  // Start the worker
  worker.start().catch((error) => {
    console.error('❌ Failed to start worker:', error);
    process.exit(1);
  });
}

module.exports = { StandaloneEmailWorker, EmailQueue, WorkerStatus, WorkerLog, emailService, logger, sequelize };

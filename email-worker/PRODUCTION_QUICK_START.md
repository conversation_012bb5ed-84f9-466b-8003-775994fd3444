# HLenergy Email Worker - Production Quick Start

This guide will get your email worker running in production in under 10 minutes.

## 🚀 Quick Deployment

### Prerequisites

- Node.js 18+ installed
- PM2 installed globally: `npm install -g pm2`
- MySQL database running
- SMTP credentials ready

### 1. Configure Environment

```bash
cd backend/email-worker

# Copy production environment template
cp .env.production .env

# Edit with your production settings
nano .env
```

**Required settings to update:**
```bash
# Database
DB_HOST=your-db-host
DB_USER=your-db-user
DB_PASSWORD=your-db-password
DB_NAME=hlenergy

# SMTP
SMTP_HOST=your-smtp-host
SMTP_USER=your-smtp-user
SMTP_PASSWORD=your-smtp-password
```

### 2. Deploy to Production

```bash
# Run the automated deployment script
./deploy-production.sh
```

That's it! The script will:
- ✅ Install dependencies
- ✅ Run connection tests
- ✅ Start the worker with PM2
- ✅ Configure auto-restart
- ✅ Run health checks

## 📊 Monitoring & Management

### Check Status
```bash
# Quick status check
npm run status

# Detailed monitoring
npm run monitor

# PM2 status
pm2 status hlenergy-email-worker
```

### View Logs
```bash
# Real-time logs
npm run pm2:logs

# Error logs only
npm run logs:errors

# PM2 monitoring dashboard
npm run pm2:monit
```

### Management Commands
```bash
# Restart worker
npm run pm2:restart

# Stop worker
npm run pm2:stop

# Start worker
npm run pm2:start

# Health check
npm run health
```

## 🔧 Production Configuration

### Key Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `EMAIL_BATCH_SIZE` | 10 | Emails processed per cycle |
| `EMAIL_PROCESSING_INTERVAL` | 30000 | Delay between cycles (ms) |
| `EMAIL_MAX_RETRIES` | 3 | Max retry attempts |
| `LOG_TO_DATABASE` | true | Enable database logging |
| `HEALTH_CHECK_ENABLED` | true | Enable health endpoint |
| `CLEANUP_ENABLED` | true | Auto-cleanup old emails |

### Performance Tuning

For **high volume** (1000+ emails/day):
```bash
EMAIL_BATCH_SIZE=20
EMAIL_PROCESSING_INTERVAL=15000
MAX_CONCURRENT_EMAILS=10
```

For **low volume** (< 100 emails/day):
```bash
EMAIL_BATCH_SIZE=5
EMAIL_PROCESSING_INTERVAL=60000
MAX_CONCURRENT_EMAILS=2
```

## 🚨 Troubleshooting

### Worker Not Starting
```bash
# Check PM2 logs
pm2 logs hlenergy-email-worker --lines 50

# Test connections
npm test

# Check configuration
npm run health
```

### High Memory Usage
```bash
# Restart worker
npm run pm2:restart

# Check memory limit in ecosystem.config.js
# Default: max_memory_restart: '1G'
```

### Database Connection Issues
```bash
# Test database connection
npm test

# Check database credentials in .env
# Verify database server is running
```

### SMTP Issues
```bash
# Test SMTP connection
npm test

# Check SMTP credentials in .env
# Verify SMTP server settings
```

### Queue Backlog
```bash
# Check queue status
npm run status

# Monitor in real-time
npm run monitor

# Increase processing speed
# Edit .env: EMAIL_BATCH_SIZE=20
npm run pm2:restart
```

## 📈 Monitoring URLs

If health check is enabled:
- **Health Check**: `http://localhost:3002/health`
- **Metrics**: `http://localhost:3003/metrics` (if enabled)

## 🔄 Updates & Maintenance

### Update Worker
```bash
# Pull latest changes
git pull

# Redeploy
./deploy-production.sh
```

### Database Maintenance
```bash
# Clean up old emails (automatic if CLEANUP_ENABLED=true)
npm run cleanup

# Check worker logs table
npm run migrate:info
```

### Log Rotation
Logs are automatically rotated daily. Old logs are kept for 30 days by default.

## 🆘 Emergency Procedures

### Worker Crashed
```bash
# Check what happened
pm2 logs hlenergy-email-worker --lines 100

# Restart immediately
npm run pm2:restart

# Monitor for stability
npm run pm2:monit
```

### Database Down
1. Worker will automatically retry connections
2. Emails remain queued
3. Fix database issue
4. Worker resumes automatically

### SMTP Down
1. Emails will be marked as failed
2. Automatic retries will occur
3. Fix SMTP issue
4. Retry failed emails via admin dashboard

## 📞 Support

For issues:
1. Check logs: `npm run pm2:logs`
2. Run monitor: `npm run monitor`
3. Check health: `npm run health`
4. Review configuration in `.env`

## 🎯 Production Checklist

- [ ] Environment variables configured
- [ ] Database connection tested
- [ ] SMTP connection tested
- [ ] Worker deployed with PM2
- [ ] Health checks passing
- [ ] Monitoring set up
- [ ] Log rotation configured
- [ ] Backup procedures in place

**Your HLenergy Email Worker is now production-ready!** 🚀

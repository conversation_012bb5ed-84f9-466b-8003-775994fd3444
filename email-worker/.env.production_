# Database Configuration
DB_HOST=logs.hlenergy.pt
DB_PORT=3306
DB_USER=hlenes_admin
DB_PASSWORD=hlenergyadmin1
DB_NAME=api_v2
DB_NAME_TEST=api_v2
DB_SSL=false

# Email Configuration
SMTP_HOST=smtp-pt.securemail.pro
SMTP_PORT=465
SMTP_USER=<EMAIL>
SMTP_PASSWORD=kufduk-Toxxap-pozti1
SMTP_FROM_NAME=HLenergy
SMTP_FROM_EMAIL=<EMAIL>

# Worker Configuration
EMAIL_BATCH_SIZE=10
EMAIL_PROCESSING_INTERVAL=30000
EMAIL_MAX_RETRIES=3
EMAIL_RETRY_DELAYS=5,15,60

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_MAX_SIZE=20m
LOG_FILE_MAX_FILES=14d

# Database Logging
LOG_TO_DATABASE=true    # Enable database logging
LOG_TO_FILES=true       # Enable file logging (default: true)

# Process Configuration
WORKER_NAME=hlenergy-email-worker
WORKER_PID_FILE=./worker.pid
WORKER_LOG_FILE=./logs/email-worker.log

# Health Check Configuration
HEALTH_CHECK_PORT=3002
HEALTH_CHECK_ENABLED=true

# Cleanup Configuration
CLEANUP_OLD_EMAILS_DAYS=30
CLEANUP_INTERVAL_HOURS=48

# Environment
NODE_ENV=production

# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_HOST=logs.hlenergy.pt
DB_PORT=3306
DB_USER=hlenes_admin
DB_PASSWORD=hlenergyadmin1
DB_NAME=api_v2
DB_NAME_TEST=hlenergy_test
DB_SSL=false




# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your_refresh_token_secret_here
JWT_REFRESH_EXPIRES_IN=30d

# CORS Configuration
FRONTEND_URL=http://localhost:5173

# Google reCAPTCHA v3
RECAPTCHA_SECRET_KEY=6LcEPn4rAAAAAIBtjWWF-bJJg5-Vk_0p3GzXKRwy

# Email Configuration (used by contact forms and notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Note: Email worker is now standalone
# Location: backend/email-worker/
# Configure email worker separately in backend/email-worker/.env

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Admin User (for seeding)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123456
STAFF_EMAIL=<EMAIL>
STAFF_PASSWORD=staff123456

# Logging
LOG_LEVEL=info

# Deployment (Production)
DEPLOY_TIME=
DEPLOY_USER=
DEPLOYMENT_ID=
SERVER_INSTANCE=
DEPLOY_REGION=

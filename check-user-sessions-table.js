#!/usr/bin/env node

/**
 * Check User Sessions Table Structure
 * Analyzes the existing user_sessions table structure
 */

require('dotenv').config();
const { sequelize } = require('./shared/config/database');

async function checkUserSessionsTable() {
  try {
    console.log('🔍 Checking user_sessions table structure...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established');
    
    // Check if table exists
    const [tableExists] = await sequelize.query(`
      SELECT COUNT(*) as table_exists 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() 
      AND table_name = 'user_sessions'
    `);
    
    if (tableExists[0].table_exists === 0) {
      console.log('❌ user_sessions table does not exist');
      return;
    }
    
    console.log('✅ user_sessions table exists');
    
    // Get table structure
    const [columns] = await sequelize.query(`
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT,
        COLUMN_COMMENT,
        EXTRA
      FROM information_schema.columns 
      WHERE table_schema = DATABASE() 
      AND table_name = 'user_sessions'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('\n📊 Current table structure:');
    console.log('Column Name | Data Type | Nullable | Default | Comment');
    console.log('------------|-----------|----------|---------|--------');
    
    columns.forEach(col => {
      const nullable = col.IS_NULLABLE === 'YES' ? 'YES' : 'NO';
      const defaultVal = col.COLUMN_DEFAULT || 'NULL';
      const comment = col.COLUMN_COMMENT || '';
      console.log(`${col.COLUMN_NAME.padEnd(11)} | ${col.DATA_TYPE.padEnd(9)} | ${nullable.padEnd(8)} | ${defaultVal.padEnd(7)} | ${comment}`);
    });
    
    // Get indexes
    const [indexes] = await sequelize.query(`
      SELECT 
        INDEX_NAME,
        COLUMN_NAME,
        NON_UNIQUE,
        SEQ_IN_INDEX
      FROM information_schema.statistics 
      WHERE table_schema = DATABASE() 
      AND table_name = 'user_sessions'
      ORDER BY INDEX_NAME, SEQ_IN_INDEX
    `);
    
    console.log('\n🔍 Current indexes:');
    const indexGroups = {};
    indexes.forEach(idx => {
      if (!indexGroups[idx.INDEX_NAME]) {
        indexGroups[idx.INDEX_NAME] = {
          columns: [],
          unique: idx.NON_UNIQUE === 0
        };
      }
      indexGroups[idx.INDEX_NAME].columns.push(idx.COLUMN_NAME);
    });
    
    Object.entries(indexGroups).forEach(([indexName, info]) => {
      const uniqueStr = info.unique ? '(UNIQUE)' : '';
      console.log(`   - ${indexName}: [${info.columns.join(', ')}] ${uniqueStr}`);
    });
    
    // Get foreign keys
    const [foreignKeys] = await sequelize.query(`
      SELECT 
        CONSTRAINT_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM information_schema.key_column_usage 
      WHERE table_schema = DATABASE() 
      AND table_name = 'user_sessions'
      AND REFERENCED_TABLE_NAME IS NOT NULL
    `);
    
    if (foreignKeys.length > 0) {
      console.log('\n🔗 Foreign key constraints:');
      foreignKeys.forEach(fk => {
        console.log(`   - ${fk.CONSTRAINT_NAME}: ${fk.COLUMN_NAME} -> ${fk.REFERENCED_TABLE_NAME}.${fk.REFERENCED_COLUMN_NAME}`);
      });
    } else {
      console.log('\n🔗 No foreign key constraints found');
    }
    
    // Check for sample data
    const [sampleData] = await sequelize.query(`
      SELECT COUNT(*) as record_count
      FROM user_sessions
    `);
    
    console.log(`\n📈 Current record count: ${sampleData[0].record_count}`);
    
    if (sampleData[0].record_count > 0) {
      const [sample] = await sequelize.query(`
        SELECT * FROM user_sessions LIMIT 3
      `);
      
      console.log('\n📋 Sample records:');
      sample.forEach((record, index) => {
        console.log(`Record ${index + 1}:`, JSON.stringify(record, null, 2));
      });
    }
    
    // Generate model structure suggestion
    console.log('\n💡 Suggested Sequelize model structure based on current table:');
    console.log('```javascript');
    
    columns.forEach(col => {
      const fieldName = col.COLUMN_NAME;
      let dataType = '';
      
      switch (col.DATA_TYPE) {
        case 'int':
          dataType = 'DataTypes.INTEGER';
          break;
        case 'varchar':
          dataType = 'DataTypes.STRING';
          break;
        case 'text':
          dataType = 'DataTypes.TEXT';
          break;
        case 'datetime':
          dataType = 'DataTypes.DATE';
          break;
        case 'tinyint':
          dataType = 'DataTypes.BOOLEAN';
          break;
        case 'enum':
          dataType = 'DataTypes.ENUM';
          break;
        default:
          dataType = `DataTypes.${col.DATA_TYPE.toUpperCase()}`;
      }
      
      const nullable = col.IS_NULLABLE === 'YES';
      const isPrimary = col.EXTRA.includes('auto_increment');
      
      console.log(`  ${fieldName}: {`);
      console.log(`    type: ${dataType},`);
      if (isPrimary) {
        console.log(`    primaryKey: true,`);
        console.log(`    autoIncrement: true,`);
      }
      console.log(`    allowNull: ${nullable},`);
      if (col.COLUMN_DEFAULT && col.COLUMN_DEFAULT !== 'NULL') {
        console.log(`    defaultValue: ${col.COLUMN_DEFAULT},`);
      }
      if (col.COLUMN_COMMENT) {
        console.log(`    comment: '${col.COLUMN_COMMENT}',`);
      }
      console.log(`  },`);
    });
    
    console.log('```');
    
  } catch (error) {
    console.error('❌ Error checking table structure:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    try {
      await sequelize.close();
      console.log('\n🔌 Database connection closed');
    } catch (closeError) {
      console.error('⚠️  Error closing database connection:', closeError.message);
    }
  }
}

// Run check if called directly
if (require.main === module) {
  checkUserSessionsTable();
}

module.exports = { checkUserSessionsTable };

#!/usr/bin/env node

/**
 * Fix Database Schema - Comprehensive Fix
 * Fixes all database schema issues for session management
 */

require('dotenv').config();
const { Sequelize } = require('sequelize');

const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    dialect: 'mysql',
    logging: console.log,
  }
);

const fixDatabaseSchema = async () => {
  try {
    console.log('🔧 Comprehensive Database Schema Fix...');
    
    // 1. Check current lock_reason column
    console.log('\n1. Checking current lock_reason column...');
    const [currentSchema] = await sequelize.query(`
      DESCRIBE user_sessions lock_reason
    `);
    console.log('Current lock_reason:', currentSchema[0]);
    
    // 2. Drop the column and recreate it
    console.log('\n2. Dropping and recreating lock_reason column...');
    
    try {
      await sequelize.query(`
        ALTER TABLE user_sessions DROP COLUMN lock_reason
      `);
      console.log('✅ Dropped lock_reason column');
    } catch (error) {
      console.log('⚠️  Column might not exist:', error.message);
    }
    
    // 3. Add the column with correct ENUM values
    await sequelize.query(`
      ALTER TABLE user_sessions 
      ADD COLUMN lock_reason ENUM('inactivity', 'admin_action', 'security_breach', 'manual') 
      DEFAULT NULL 
      COMMENT 'Reason for session lock'
      AFTER locked_at
    `);
    console.log('✅ Added lock_reason column with correct ENUM values');
    
    // 4. Verify the fix
    console.log('\n3. Verifying the fix...');
    const [newSchema] = await sequelize.query(`
      DESCRIBE user_sessions lock_reason
    `);
    console.log('New lock_reason:', newSchema[0]);
    
    // 5. Test the column with a sample update
    console.log('\n4. Testing the column...');
    
    // Find a session to test with
    const [sessions] = await sequelize.query(`
      SELECT id FROM user_sessions WHERE is_active = 1 LIMIT 1
    `);
    
    if (sessions.length > 0) {
      const sessionId = sessions[0].id;
      console.log(`Testing with session ID: ${sessionId}`);
      
      // Test each enum value
      const testValues = ['inactivity', 'admin_action', 'security_breach', 'manual'];
      
      for (const value of testValues) {
        await sequelize.query(`
          UPDATE user_sessions 
          SET lock_reason = ? 
          WHERE id = ?
        `, {
          replacements: [value, sessionId]
        });
        console.log(`✅ Successfully set lock_reason to '${value}'`);
      }
      
      // Reset to NULL
      await sequelize.query(`
        UPDATE user_sessions 
        SET lock_reason = NULL 
        WHERE id = ?
      `, {
        replacements: [sessionId]
      });
      console.log('✅ Reset lock_reason to NULL');
    } else {
      console.log('⚠️  No active sessions found to test with');
    }
    
    // 6. Show final table structure
    console.log('\n5. Final table structure:');
    const [finalSchema] = await sequelize.query(`
      DESCRIBE user_sessions
    `);
    
    console.log('\nuser_sessions table structure:');
    finalSchema.forEach(column => {
      console.log(`  ${column.Field}: ${column.Type} ${column.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${column.Default ? `DEFAULT ${column.Default}` : ''}`);
    });
    
  } catch (error) {
    console.error('❌ Failed to fix database schema:', error.message);
    console.error('SQL Error:', error.sql);
    throw error;
  }
};

// Run if called directly
if (require.main === module) {
  fixDatabaseSchema()
    .then(() => {
      console.log('\n🎉 Database schema fix completed successfully!');
      console.log('🔄 Please restart the server to pick up the changes.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Database schema fix failed:', error.message);
      process.exit(1);
    });
}

module.exports = { fixDatabaseSchema };

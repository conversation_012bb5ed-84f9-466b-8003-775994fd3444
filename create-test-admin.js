#!/usr/bin/env node

/**
 * Create Test Admin User
 * Creates an admin user for testing the session security
 */

require('dotenv').config();
const { User } = require('./shared/models');

const createTestAdmin = async () => {
  try {
    console.log('🔧 Creating test admin user...');
    
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123456';
    
    // Check if admin already exists
    const existingAdmin = await User.findOne({
      where: { email: adminEmail }
    });
    
    if (existingAdmin) {
      console.log('✅ Admin user already exists:', adminEmail);
      console.log('   - ID:', existingAdmin.id);
      console.log('   - Role:', existingAdmin.role);
      console.log('   - Active:', existingAdmin.is_active);
      console.log('   - Email Verified:', existingAdmin.email_verified);
      return existingAdmin;
    }
    
    // Create new admin user
    const adminUser = await User.create({
      name: 'Test Admin',
      email: adminEmail,
      password: adminPassword,
      role: 'admin',
      email_verified: true,
      is_active: true,
    });
    
    console.log('✅ Test admin user created successfully!');
    console.log('   - Email:', adminUser.email);
    console.log('   - ID:', adminUser.id);
    console.log('   - Role:', adminUser.role);
    
    return adminUser;
    
  } catch (error) {
    console.error('❌ Failed to create admin user:', error.message);
    throw error;
  }
};

// Run if called directly
if (require.main === module) {
  createTestAdmin()
    .then(() => {
      console.log('🎉 Admin user setup complete!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Setup failed:', error.message);
      process.exit(1);
    });
}

module.exports = { createTestAdmin };

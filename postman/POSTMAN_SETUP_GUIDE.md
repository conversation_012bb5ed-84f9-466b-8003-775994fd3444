# 🚀 HLenergy API - Postman Setup Guide

## 📋 Overview

This guide provides **3 different methods** to add all HLenergy API endpoints to Postman, from automatic import to manual setup.

## 🎯 Method 1: Import from Swagger/OpenAPI (Recommended) ⭐

### **Step 1: Start the Backend Server**
```bash
cd backend
npm start
```

### **Step 2: Import from Swagger**
1. Open Postman
2. Click **"Import"** (top left corner)
3. Select **"Link"** tab
4. Enter: `http://localhost:3001/api-docs.json`
5. Click **"Continue"** → **"Import"**

### **✅ Benefits:**
- ✅ All 50+ endpoints automatically imported
- ✅ Request/response schemas included
- ✅ Authentication setup included
- ✅ Always up-to-date with code changes

---

## 🎯 Method 2: Import Custom Postman Collections

### **Step 1: Import Collections**
1. Open Postman
2. Click **"Import"** → **"Files"**
3. Select these files from `backend/postman/`:
   - `HLenergy-API-Collection.json` (Main endpoints)
   - `HLenergy-API-Collection-Part2.json` (Admin & System)
   - `HLenergy-Environment.json` (Environment variables)

### **Step 2: Set Environment**
1. Click the environment dropdown (top right)
2. Select **"HLenergy Development"**
3. Verify variables are loaded:
   - `baseUrl`: `http://localhost:3001`
   - `authToken`: (empty - will be auto-filled)
   - `userId`: (empty - will be auto-filled)

### **✅ What You Get:**
- 📁 **Authentication** - Register, Login, Refresh Token
- 📁 **System Health** - Health checks, Version info
- 📁 **Contact Management** - Form submission, Lead management
- 📁 **User Profile** - Profile management, Password changes
- 📁 **Analytics** - Event tracking, Metrics, Heatmaps
- 📁 **Socket.io** - Real-time features, Broadcasting
- 📁 **Admin Management** - User management, Dashboard
- 📁 **Security** - Security monitoring, IP blocking
- 📁 **Dependencies** - Package management, Security checks
- 📁 **Database** - Database statistics
- 📁 **Logs** - Log management, Cleanup
- 📁 **API Documentation** - OpenAPI specs, Versioning

---

## 🎯 Method 3: Manual Setup (Step-by-Step)

### **Step 1: Create New Collection**
1. Click **"New"** → **"Collection"**
2. Name: `HLenergy API`
3. Add description: `Complete HLenergy API endpoints`

### **Step 2: Set Collection Authorization**
1. Go to **"Authorization"** tab
2. Type: **"Bearer Token"**
3. Token: `{{authToken}}`

### **Step 3: Add Environment Variables**
1. Click **"Environments"** → **"Create Environment"**
2. Name: `HLenergy Development`
3. Add variables:
   ```
   baseUrl = http://localhost:3001
   authToken = (leave empty)
   userId = (leave empty)
   ```

### **Step 4: Add Folders and Requests**
Create folders for each category and add requests manually using the endpoint list from the landing page.

---

## 🔧 Quick Start Workflow

### **1. Authentication Flow**
```bash
# 1. Register a new user
POST {{baseUrl}}/api/v1/auth/register
{
  "name": "Test User",
  "email": "<EMAIL>",
  "password": "TestPassword123!"
}

# 2. Login (token auto-saved to environment)
POST {{baseUrl}}/api/v1/auth/login
{
  "email": "<EMAIL>",
  "password": "TestPassword123!"
}

# 3. Now all authenticated endpoints work automatically!
```

### **2. Test System Health**
```bash
GET {{baseUrl}}/health
GET {{baseUrl}}/version/summary
GET {{baseUrl}}/api/test
```

### **3. Submit Contact Form**
```bash
POST {{baseUrl}}/api/v1/contact/submit
{
  "name": "Jane Doe",
  "email": "<EMAIL>",
  "message": "Interested in solar consultation"
}
```

### **4. Track Analytics**
```bash
POST {{baseUrl}}/api/v1/analytics/events
{
  "events": [{
    "event": "page_view",
    "category": "navigation",
    "properties": {"page": "/services"}
  }]
}
```

---

## 🎯 Pro Tips

### **Auto-Authentication**
The collections include test scripts that automatically:
- Save JWT tokens after login/register
- Set user ID for subsequent requests
- Handle token refresh automatically

### **Environment Switching**
Create multiple environments for different stages:
- **Development**: `http://localhost:3001`
- **Staging**: `https://staging-api.hlenergy.com`
- **Production**: `https://api.hlenergy.com`

### **Testing Workflows**
Use Postman's **Collection Runner** to:
- Test all endpoints sequentially
- Validate response schemas
- Check authentication flows
- Monitor API performance

### **Variables Available**
- `{{baseUrl}}` - API base URL
- `{{authToken}}` - JWT authentication token
- `{{userId}}` - Current user ID
- `{{$timestamp}}` - Current timestamp
- `{{$randomEmail}}` - Random email for testing

---

## 📊 Endpoint Categories

| Category | Endpoints | Description |
|----------|-----------|-------------|
| 🔐 Authentication | 3 | Register, Login, Refresh |
| 🏥 System Health | 4 | Health checks, Version info |
| 📞 Contact | 3 | Form submission, Management |
| 👤 Profile | 4 | User profile management |
| 📧 Email | 4 | Email verification, Password reset |
| 📬 Email Queue | 4 | Queue management, Worker control |
| 📊 Analytics | 4 | Event tracking, Metrics |
| 🔌 Socket.io | 5 | Real-time features |
| 👑 Admin | 4 | User management, Dashboard |
| 🔒 Security | 4 | Security monitoring |
| 📦 Dependencies | 4 | Package management |
| 🗄️ Database | 1 | Database statistics |
| 📝 Logs | 7 | Log management |
| 📚 Documentation | 4 | API documentation |

**Total: 50+ Endpoints** 🎉

---

## 🚨 Troubleshooting

### **Common Issues:**

1. **"Connection Error"**
   - ✅ Ensure backend server is running: `npm start`
   - ✅ Check baseUrl: `http://localhost:3001`

2. **"401 Unauthorized"**
   - ✅ Login first to get authentication token
   - ✅ Check if token is set in environment

3. **"404 Not Found"**
   - ✅ Verify endpoint URL matches landing page
   - ✅ Check API version (v1) in URL

4. **"Import Failed"**
   - ✅ Ensure server is running before importing from Swagger
   - ✅ Use file import for custom collections

### **Support:**
- 📖 API Documentation: `http://localhost:3001/api-docs`
- 🏠 Landing Page: `http://localhost:3001/`
- 🔍 Health Check: `http://localhost:3001/health`

---

## 🎉 Success!

You now have access to all **50+ HLenergy API endpoints** in Postman with:
- ✅ Complete authentication flow
- ✅ Organized folder structure
- ✅ Auto-token management
- ✅ Example requests and responses
- ✅ Environment variables setup

**Happy API Testing!** 🚀

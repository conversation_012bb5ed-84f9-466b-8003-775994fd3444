{"info": {"name": "HLenergy API - Auto-Generated Collection", "description": "Auto-generated collection from landing page endpoints", "version": "2.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:3001", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}], "item": [{"name": "📁 System Health", "item": [{"name": "Health check endpoint", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}, {"name": "Version information", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/version", "host": ["{{baseUrl}}"], "path": ["version"]}}}, {"name": "Version summary", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/version/summary", "host": ["{{baseUrl}}"], "path": ["version", "summary"]}}}, {"name": "API test endpoint", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/test", "host": ["{{baseUrl}}"], "path": ["api", "test"]}}}]}, {"name": "📁 Authentication", "item": [{"name": "User registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "register"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test User\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"TestPassword123!\"\n}"}}}, {"name": "User login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "login"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"TestPassword123!\"\n}"}}}, {"name": "Refresh token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/refresh", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "refresh"]}, "body": {"mode": "raw", "raw": "{}"}}}]}, {"name": "📁 Contact", "item": [{"name": "Submit contact form", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/contact/submit", "host": ["{{baseUrl}}"], "path": ["api", "v1", "contact", "submit"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"message\": \"Interested in energy consultation\"\n}"}}}, {"name": "Get contact submissions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/contact", "host": ["{{baseUrl}}"], "path": ["api", "v1", "contact"]}}}, {"name": "Update contact status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/contact/:id/status", "host": ["{{baseUrl}}"], "path": ["api", "v1", "contact", ":id", "status"]}, "body": {"mode": "raw", "raw": "{}"}}}]}, {"name": "📁 Profile", "item": [{"name": "Get user profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/profile", "host": ["{{baseUrl}}"], "path": ["api", "v1", "profile"]}}}, {"name": "Update user profile", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/profile", "host": ["{{baseUrl}}"], "path": ["api", "v1", "profile"]}, "body": {"mode": "raw", "raw": "{}"}}}, {"name": "Change password", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/profile/password", "host": ["{{baseUrl}}"], "path": ["api", "v1", "profile", "password"]}, "body": {"mode": "raw", "raw": "{}"}}}, {"name": "Get user sessions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/profile/sessions", "host": ["{{baseUrl}}"], "path": ["api", "v1", "profile", "sessions"]}}}, {"name": "Get session details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/profile/sessions/:id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "profile", "sessions", ":id"]}}}]}, {"name": "📁 Admin", "item": [{"name": "Admin dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/dashboard", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "dashboard"]}}}, {"name": "Get all users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/users", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "users"]}}}, {"name": "Get user details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/users/:id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "users", ":id"]}}}, {"name": "Update user status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/users/:id/status", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "users", ":id", "status"]}, "body": {"mode": "raw", "raw": "{}"}}}, {"name": "Admin analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/analytics", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "analytics"]}}}, {"name": "Admin contacts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/contacts", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "contacts"]}}}, {"name": "Get contact details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/contacts/:id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "contacts", ":id"]}}}, {"name": "System health check", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/system/health", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "system", "health"]}}}]}, {"name": "📁 Email", "item": [{"name": "Send verification email", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/email/send-verification", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email", "send-verification"]}, "body": {"mode": "raw", "raw": "{}"}}}, {"name": "Verify email", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/email/verify", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email", "verify"]}, "body": {"mode": "raw", "raw": "{}"}}}, {"name": "Forgot password", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/email/forgot-password", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email", "forgot-password"]}, "body": {"mode": "raw", "raw": "{}"}}}, {"name": "Reset password", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/email/reset-password", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email", "reset-password"]}, "body": {"mode": "raw", "raw": "{}"}}}]}, {"name": "📁 <PERSON><PERSON>", "item": [{"name": "Get queue status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/email-queue/status", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email-queue", "status"]}}}, {"name": "Get queued emails", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/email-queue/emails", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email-queue", "emails"]}}}, {"name": "Get email details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/email-queue/emails/:id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email-queue", "emails", ":id"]}}}, {"name": "<PERSON><PERSON> failed email", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/email-queue/emails/:id/retry", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email-queue", "emails", ":id", "retry"]}, "body": {"mode": "raw", "raw": "{}"}}}, {"name": "Cancel email", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/email-queue/emails/:id/cancel", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email-queue", "emails", ":id", "cancel"]}, "body": {"mode": "raw", "raw": "{}"}}}, {"name": "Start email worker", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/email-queue/worker/start", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email-queue", "worker", "start"]}, "body": {"mode": "raw", "raw": "{}"}}}, {"name": "Stop email worker", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/email-queue/worker/stop", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email-queue", "worker", "stop"]}, "body": {"mode": "raw", "raw": "{}"}}}, {"name": "Cleanup queue", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/email-queue/cleanup", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email-queue", "cleanup"]}}}]}, {"name": "📁 Analytics", "item": [{"name": "Track analytics events", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/analytics/events", "host": ["{{baseUrl}}"], "path": ["api", "v1", "analytics", "events"]}, "body": {"mode": "raw", "raw": "{}"}}}, {"name": "Get analytics metrics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/analytics/metrics", "host": ["{{baseUrl}}"], "path": ["api", "v1", "analytics", "metrics"]}}}, {"name": "Get business metrics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/analytics/business-metrics", "host": ["{{baseUrl}}"], "path": ["api", "v1", "analytics", "business-metrics"]}}}, {"name": "Get heatmap data", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/analytics/heatmap", "host": ["{{baseUrl}}"], "path": ["api", "v1", "analytics", "heatmap"]}}}]}, {"name": "📁 Socket.io", "item": [{"name": "Socket health check", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/socket/health", "host": ["{{baseUrl}}"], "path": ["api", "v1", "socket", "health"]}}}, {"name": "Socket status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/socket/status", "host": ["{{baseUrl}}"], "path": ["api", "v1", "socket", "status"]}}}, {"name": "Get online users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/socket/users/online", "host": ["{{baseUrl}}"], "path": ["api", "v1", "socket", "users", "online"]}}}, {"name": "Broadcast message", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/socket/broadcast", "host": ["{{baseUrl}}"], "path": ["api", "v1", "socket", "broadcast"]}, "body": {"mode": "raw", "raw": "{}"}}}, {"name": "Get active rooms", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/socket/rooms", "host": ["{{baseUrl}}"], "path": ["api", "v1", "socket", "rooms"]}}}, {"name": "Send analytics event", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/socket/analytics", "host": ["{{baseUrl}}"], "path": ["api", "v1", "socket", "analytics"]}, "body": {"mode": "raw", "raw": "{}"}}}, {"name": "Get socket logs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/socket/logs", "host": ["{{baseUrl}}"], "path": ["api", "v1", "socket", "logs"]}}}, {"name": "Get socket metrics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/socket/metrics", "host": ["{{baseUrl}}"], "path": ["api", "v1", "socket", "metrics"]}}}, {"name": "Send notification", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/socket/notify", "host": ["{{baseUrl}}"], "path": ["api", "v1", "socket", "notify"]}, "body": {"mode": "raw", "raw": "{}"}}}, {"name": "Get memory usage", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/socket/memory", "host": ["{{baseUrl}}"], "path": ["api", "v1", "socket", "memory"]}}}, {"name": "Cleanup connections", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/socket/cleanup", "host": ["{{baseUrl}}"], "path": ["api", "v1", "socket", "cleanup"]}, "body": {"mode": "raw", "raw": "{}"}}}, {"name": "Get temporary token", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/socket/temp-token", "host": ["{{baseUrl}}"], "path": ["api", "v1", "socket", "temp-token"]}, "body": {"mode": "raw", "raw": "{}"}}}]}, {"name": "📁 Dependencies", "item": [{"name": "Dependency status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/dependencies", "host": ["{{baseUrl}}"], "path": ["dependencies"]}}}, {"name": "Check outdated packages", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/dependencies/outdated", "host": ["{{baseUrl}}"], "path": ["dependencies", "outdated"]}}}, {"name": "Security vulnerabilities", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/dependencies/security", "host": ["{{baseUrl}}"], "path": ["dependencies", "security"]}}}, {"name": "Key dependencies", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/dependencies/key", "host": ["{{baseUrl}}"], "path": ["dependencies", "key"]}}}]}, {"name": "📁 Database", "item": [{"name": "Database statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/database/stats", "host": ["{{baseUrl}}"], "path": ["database", "stats"]}}}]}, {"name": "📁 Logs", "item": [{"name": "Logging system status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/logs/status", "host": ["{{baseUrl}}"], "path": ["logs", "status"]}}}, {"name": "Get recent logs", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/logs/recent", "host": ["{{baseUrl}}"], "path": ["logs", "recent"]}}}, {"name": "Get database logs", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/logs/db", "host": ["{{baseUrl}}"], "path": ["logs", "db"]}}}, {"name": "Database log statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/logs/db/stats", "host": ["{{baseUrl}}"], "path": ["logs", "db", "stats"]}}}, {"name": "Force database reconnection", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/logs/reconnect", "host": ["{{baseUrl}}"], "path": ["logs", "reconnect"]}, "body": {"mode": "raw", "raw": "{}"}}}, {"name": "Cleanup old logs", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/logs/cleanup", "host": ["{{baseUrl}}"], "path": ["logs", "cleanup"]}}}, {"name": "Cleanup fallback files", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/logs/fallback/cleanup", "host": ["{{baseUrl}}"], "path": ["logs", "fallback", "cleanup"]}}}]}, {"name": "📁 Security", "item": [{"name": "Security dashboard", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/security/dashboard", "host": ["{{baseUrl}}"], "path": ["security", "dashboard"]}}}, {"name": "Get security events", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/security/events", "host": ["{{baseUrl}}"], "path": ["security", "events"]}}}, {"name": "Get threat analysis", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/security/threats", "host": ["{{baseUrl}}"], "path": ["security", "threats"]}}}, {"name": "Block IP address", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/security/block-ip", "host": ["{{baseUrl}}"], "path": ["security", "block-ip"]}, "body": {"mode": "raw", "raw": "{}"}}}]}]}
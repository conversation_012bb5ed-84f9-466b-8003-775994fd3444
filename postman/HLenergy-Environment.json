{"id": "hlenergy-environment", "name": "HLenergy Development", "values": [{"key": "baseUrl", "value": "http://localhost:3001", "description": "Base URL for the HLenergy API", "type": "default", "enabled": true}, {"key": "authToken", "value": "", "description": "JWT authentication token (auto-populated after login)", "type": "secret", "enabled": true}, {"key": "userId", "value": "", "description": "Current user ID (auto-populated after login)", "type": "default", "enabled": true}, {"key": "adminEmail", "value": "<EMAIL>", "description": "Admin user email for testing", "type": "default", "enabled": true}, {"key": "adminPassword", "value": "admin123", "description": "Admin user password for testing", "type": "secret", "enabled": true}, {"key": "testUserEmail", "value": "<EMAIL>", "description": "Test user email", "type": "default", "enabled": true}, {"key": "testUserPassword", "value": "TestPassword123!", "description": "Test user password", "type": "secret", "enabled": true}, {"key": "frontendUrl", "value": "http://localhost:5173", "description": "Frontend application URL", "type": "default", "enabled": true}, {"key": "swaggerUrl", "value": "http://localhost:3001/api-docs", "description": "Swagger UI URL", "type": "default", "enabled": true}, {"key": "apiVersion", "value": "v1", "description": "Current API version", "type": "default", "enabled": true}], "_postman_variable_scope": "environment"}
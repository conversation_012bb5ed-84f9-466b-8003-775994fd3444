# 🚀 HLenergy API - Postman Collections

## 📋 Quick Start

### **🎯 Method 1: Import from Swagger (Recommended)**
```bash
# 1. Start backend server
npm start

# 2. In Postman: Import → Link → http://localhost:3001/api-docs.json
```

### **🎯 Method 2: Import Pre-built Collections**
```bash
# Import these files in Postman:
postman/HLenergy-API-Collection.json          # Main endpoints
postman/HLenergy-API-Collection-Part2.json    # Admin & System
postman/HLenergy-Environment.json             # Environment variables
```

### **🎯 Method 3: Auto-Generate Collection**
```bash
# Generate fresh collection from code
npm run postman:generate

# Then import: postman/HLenergy-Auto-Generated-Collection.json
```

## 📁 Available Files

| File | Description | Endpoints |
|------|-------------|-----------|
| `HLenergy-API-Collection.json` | Main API endpoints | Auth, Contact, Profile, Analytics, Socket.io |
| `HLenergy-API-Collection-Part2.json` | Admin & System endpoints | Admin, Security, Dependencies, Logs |
| `HLenergy-Auto-Generated-Collection.json` | Auto-generated from code | All 50+ endpoints |
| `HLenergy-Environment.json` | Environment variables | baseUrl, authToken, etc. |
| `POSTMAN_SETUP_GUIDE.md` | Detailed setup guide | Step-by-step instructions |

## 🎯 What You Get

### **📊 50+ Endpoints Organized by Category:**
- 🔐 **Authentication** (3) - Register, Login, Refresh
- 🏥 **System Health** (4) - Health checks, Version info
- 📞 **Contact** (3) - Form submission, Management
- 👤 **Profile** (4) - User profile management
- 📧 **Email** (4) - Email verification, Password reset
- 📬 **Email Queue** (4) - Queue management, Worker control
- 📊 **Analytics** (4) - Event tracking, Metrics
- 🔌 **Socket.io** (4) - Real-time features
- 👑 **Admin** (4) - User management, Dashboard
- 🔒 **Security** (4) - Security monitoring
- 📦 **Dependencies** (4) - Package management
- 🗄️ **Database** (1) - Database statistics
- 📝 **Logs** (7) - Log management
- 📚 **Documentation** (4) - API documentation

### **✨ Features:**
- ✅ **Auto-Authentication** - Tokens saved automatically
- ✅ **Environment Variables** - Easy URL/token management
- ✅ **Example Requests** - Pre-filled request bodies
- ✅ **Test Scripts** - Automatic token extraction
- ✅ **Organized Folders** - Clean category structure

## 🔧 Usage Examples

### **Authentication Flow:**
```javascript
// 1. Register → Auto-saves token
POST {{baseUrl}}/api/v1/auth/register

// 2. All authenticated endpoints work automatically
GET {{baseUrl}}/api/v1/profile
```

### **Contact Form:**
```javascript
POST {{baseUrl}}/api/v1/contact/submit
{
  "name": "Jane Doe",
  "email": "<EMAIL>",
  "message": "Interested in solar consultation"
}
```

### **Analytics Tracking:**
```javascript
POST {{baseUrl}}/api/v1/analytics/events
{
  "events": [{
    "event": "page_view",
    "category": "navigation",
    "properties": {"page": "/services"}
  }]
}
```

## 🎯 Environment Variables

| Variable | Value | Description |
|----------|-------|-------------|
| `baseUrl` | `http://localhost:3001` | API base URL |
| `authToken` | (auto-filled) | JWT authentication token |
| `userId` | (auto-filled) | Current user ID |
| `adminEmail` | `<EMAIL>` | Admin test email |
| `testUserEmail` | `<EMAIL>` | Test user email |

## 🚀 Quick Commands

```bash
# Generate fresh Postman collection
npm run postman:generate

# Start backend server
npm start

# Run endpoint tests
npm run test:endpoints

# View API documentation
open http://localhost:3001/api-docs
```

## 📖 Documentation Links

- 🏠 **Landing Page**: http://localhost:3001/
- 📚 **Swagger UI**: http://localhost:3001/api-docs
- 🔍 **Health Check**: http://localhost:3001/health
- 📊 **Version Info**: http://localhost:3001/version/summary

## 🎉 Success!

You now have complete access to all **50+ HLenergy API endpoints** in Postman with:
- ✅ Automatic authentication
- ✅ Organized folder structure  
- ✅ Example requests
- ✅ Environment management
- ✅ Test automation

**Happy API Testing!** 🚀

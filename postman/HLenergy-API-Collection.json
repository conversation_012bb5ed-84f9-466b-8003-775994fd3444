{"info": {"name": "HLenergy API - Complete Collection", "description": "Comprehensive collection of all HLenergy API endpoints with authentication, examples, and tests", "version": "2.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:3001", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}], "item": [{"name": "🔐 Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"phone\": \"+1234567890\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "register"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('authToken', response.data.token);", "    pm.environment.set('userId', response.data.user.id);", "}"]}}]}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('authToken', response.data.token);", "    pm.environment.set('userId', response.data.user.id);", "}"]}}]}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/refresh", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "refresh"]}}}]}, {"name": "🏥 System Health", "item": [{"name": "Health Check", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}, {"name": "Version Info", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/version", "host": ["{{baseUrl}}"], "path": ["version"]}}}, {"name": "Version Summary", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/version/summary", "host": ["{{baseUrl}}"], "path": ["version", "summary"]}}}, {"name": "API Test", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/test", "host": ["{{baseUrl}}"], "path": ["api", "test"]}}}]}, {"name": "📞 Contact Management", "item": [{"name": "Submit Contact Form", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+**********\",\n  \"message\": \"Interested in solar energy consultation\",\n  \"service\": \"solar-consultation\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/contact/submit", "host": ["{{baseUrl}}"], "path": ["api", "v1", "contact", "submit"]}}}, {"name": "Get Contact Submissions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/contact?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "contact"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Update Contact Status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"contacted\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/contact/:id/status", "host": ["{{baseUrl}}"], "path": ["api", "v1", "contact", ":id", "status"], "variable": [{"key": "id", "value": "1"}]}}}]}, {"name": "👤 User Profile", "item": [{"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/profile", "host": ["{{baseUrl}}"], "path": ["api", "v1", "profile"]}}}, {"name": "Update Profile", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"phone\": \"+1234567890\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/profile", "host": ["{{baseUrl}}"], "path": ["api", "v1", "profile"]}}}, {"name": "Change Password", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"SecurePassword123!\",\n  \"newPassword\": \"NewSecurePassword123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/profile/password", "host": ["{{baseUrl}}"], "path": ["api", "v1", "profile", "password"]}}}, {"name": "Get User Sessions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/profile/sessions", "host": ["{{baseUrl}}"], "path": ["api", "v1", "profile", "sessions"]}}}]}, {"name": "📧 Email Management", "item": [{"name": "Send Verification Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/email/send-verification", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email", "send-verification"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"verification-token-here\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/email/verify", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email", "verify"]}}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/email/forgot-password", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email", "forgot-password"]}}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"reset-token-here\",\n  \"newPassword\": \"NewPassword123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/email/reset-password", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email", "reset-password"]}}}]}, {"name": "📬 <PERSON><PERSON>", "item": [{"name": "Get Queue Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/email-queue/status", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email-queue", "status"]}}}, {"name": "Get Queued Emails", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/email-queue/emails?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email-queue", "emails"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Retry Failed Email", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/email-queue/emails/:id/retry", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email-queue", "emails", ":id", "retry"], "variable": [{"key": "id", "value": "1"}]}}}, {"name": "Start Email Worker", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/email-queue/worker/start", "host": ["{{baseUrl}}"], "path": ["api", "v1", "email-queue", "worker", "start"]}}}]}, {"name": "📊 Analytics", "item": [{"name": "Track Events", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"events\": [\n    {\n      \"event\": \"page_view\",\n      \"category\": \"navigation\",\n      \"properties\": {\n        \"page\": \"/services\",\n        \"timestamp\": \"{{$timestamp}}\"\n      },\n      \"sessionId\": \"session-123\",\n      \"userId\": \"{{userId}}\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/analytics/events", "host": ["{{baseUrl}}"], "path": ["api", "v1", "analytics", "events"]}}}, {"name": "Get Analytics Metrics", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/analytics/metrics?timeRange=24h", "host": ["{{baseUrl}}"], "path": ["api", "v1", "analytics", "metrics"], "query": [{"key": "timeRange", "value": "24h"}]}}}, {"name": "Get Business Metrics", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/analytics/business-metrics?timeRange=7d", "host": ["{{baseUrl}}"], "path": ["api", "v1", "analytics", "business-metrics"], "query": [{"key": "timeRange", "value": "7d"}]}}}, {"name": "Get Heatmap Data", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/analytics/heatmap?page=/services", "host": ["{{baseUrl}}"], "path": ["api", "v1", "analytics", "heatmap"], "query": [{"key": "page", "value": "/services"}]}}}]}, {"name": "📊 Analytics", "item": [{"name": "Track Events", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"events\": [\n    {\n      \"event\": \"page_view\",\n      \"category\": \"navigation\",\n      \"properties\": {\n        \"page\": \"/services\",\n        \"timestamp\": \"{{$timestamp}}\"\n      },\n      \"sessionId\": \"session-123\",\n      \"userId\": \"{{userId}}\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/analytics/events", "host": ["{{baseUrl}}"], "path": ["api", "v1", "analytics", "events"]}}}, {"name": "Get Analytics Metrics", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/analytics/metrics?timeRange=24h", "host": ["{{baseUrl}}"], "path": ["api", "v1", "analytics", "metrics"], "query": [{"key": "timeRange", "value": "24h"}]}}}, {"name": "Get Business Metrics", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/analytics/business-metrics?timeRange=7d", "host": ["{{baseUrl}}"], "path": ["api", "v1", "analytics", "business-metrics"], "query": [{"key": "timeRange", "value": "7d"}]}}}, {"name": "Get Heatmap Data", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/analytics/heatmap?page=/services", "host": ["{{baseUrl}}"], "path": ["api", "v1", "analytics", "heatmap"], "query": [{"key": "page", "value": "/services"}]}}}]}, {"name": "🔌 Socket.io", "item": [{"name": "Socket Health Check", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/socket/health", "host": ["{{baseUrl}}"], "path": ["api", "v1", "socket", "health"]}}}, {"name": "Get Online Users", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/socket/users/online", "host": ["{{baseUrl}}"], "path": ["api", "v1", "socket", "users", "online"]}}}, {"name": "Get Active Rooms", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/socket/rooms", "host": ["{{baseUrl}}"], "path": ["api", "v1", "socket", "rooms"]}}}, {"name": "Broadcast Message", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"event\": \"notification\",\n  \"message\": \"System maintenance in 10 minutes\",\n  \"data\": {\n    \"type\": \"warning\",\n    \"priority\": \"high\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/socket/broadcast", "host": ["{{baseUrl}}"], "path": ["api", "v1", "socket", "broadcast"]}}}, {"name": "Test Socket Connection", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"Test socket message\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/socket/test", "host": ["{{baseUrl}}"], "path": ["api", "v1", "socket", "test"]}}}]}]}
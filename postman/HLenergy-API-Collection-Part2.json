{"info": {"name": "HLenergy API - Admin & System Collection", "description": "Admin, Security, Dependencies, Logs, and System endpoints", "version": "2.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:3001", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}], "item": [{"name": "👑 Admin Management", "item": [{"name": "Admin Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/dashboard", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "dashboard"]}}}, {"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/users?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get User Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/users/:id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "users", ":id"], "variable": [{"key": "id", "value": "1"}]}}}, {"name": "Update User Status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/admin/users/:id/status", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "users", ":id", "status"], "variable": [{"key": "id", "value": "1"}]}}}]}, {"name": "🔒 Security", "item": [{"name": "Security Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/security/dashboard", "host": ["{{baseUrl}}"], "path": ["security", "dashboard"]}}}, {"name": "Get Security Events", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/security/events?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["security", "events"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Threat Analysis", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/security/threats", "host": ["{{baseUrl}}"], "path": ["security", "threats"]}}}, {"name": "Block IP Address", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ip\": \"*************\",\n  \"reason\": \"Suspicious activity detected\",\n  \"duration\": \"24h\"\n}"}, "url": {"raw": "{{baseUrl}}/security/block-ip", "host": ["{{baseUrl}}"], "path": ["security", "block-ip"]}}}]}, {"name": "📦 Dependencies", "item": [{"name": "Dependency Status", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/dependencies", "host": ["{{baseUrl}}"], "path": ["dependencies"]}}}, {"name": "Check Outdated Packages", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/dependencies/outdated", "host": ["{{baseUrl}}"], "path": ["dependencies", "outdated"]}}}, {"name": "Security Vulnerabilities", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/dependencies/security", "host": ["{{baseUrl}}"], "path": ["dependencies", "security"]}}}, {"name": "Key Dependencies", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/dependencies/key", "host": ["{{baseUrl}}"], "path": ["dependencies", "key"]}}}]}, {"name": "🗄️ Database", "item": [{"name": "Database Statistics", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/database/stats", "host": ["{{baseUrl}}"], "path": ["database", "stats"]}}}]}, {"name": "📝 Logs", "item": [{"name": "Logging Status", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/logs/status", "host": ["{{baseUrl}}"], "path": ["logs", "status"]}}}, {"name": "Get Recent Logs", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/logs/recent?limit=50", "host": ["{{baseUrl}}"], "path": ["logs", "recent"], "query": [{"key": "limit", "value": "50"}]}}}, {"name": "Get Database Logs", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/logs/db?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["logs", "db"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Database Log Statistics", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/logs/db/stats", "host": ["{{baseUrl}}"], "path": ["logs", "db", "stats"]}}}, {"name": "Force Database Reconnection", "request": {"method": "POST", "url": {"raw": "{{baseUrl}}/logs/reconnect", "host": ["{{baseUrl}}"], "path": ["logs", "reconnect"]}}}, {"name": "Cleanup Old Logs", "request": {"method": "DELETE", "url": {"raw": "{{baseUrl}}/logs/cleanup?days=30", "host": ["{{baseUrl}}"], "path": ["logs", "cleanup"], "query": [{"key": "days", "value": "30"}]}}}, {"name": "Cleanup Fallback Files", "request": {"method": "DELETE", "url": {"raw": "{{baseUrl}}/logs/fallback/cleanup?days=7", "hst": ["{{baseUrl}}"], "path": ["logs", "fallback", "cleanup"], "query": [{"key": "days", "value": "7"}]}}}]}, {"name": "📚 API Documentation", "item": [{"name": "OpenAPI Specification", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api-docs.json", "host": ["{{baseUrl}}"], "path": ["api-docs.json"]}}}, {"name": "API Versioning Info", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/versions", "host": ["{{baseUrl}}"], "path": ["api", "versions"]}}}, {"name": "API Information", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api", "host": ["{{baseUrl}}"], "path": ["api"]}}}, {"name": "API v1 Information", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1", "host": ["{{baseUrl}}"], "path": ["api", "v1"]}}}]}]}
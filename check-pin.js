#!/usr/bin/env node

/**
 * Check PIN Script
 * Verifies what PIN is stored for the admin user
 */

const { sequelize } = require('./shared/config/database');
const bcrypt = require('bcryptjs');

async function checkPin() {
  try {
    console.log('🔍 Checking PIN for admin user...');

    // Get the admin PIN record
    const [pins] = await sequelize.query(`
      SELECT user_id, pin_hash, pin_salt, attempts, locked_until, lockout_count
      FROM admin_pins 
      WHERE user_id = 5
    `);

    if (pins.length === 0) {
      console.log('❌ No PIN found for user ID 5');
      return;
    }

    const pin = pins[0];
    console.log('📋 PIN Record:');
    console.table(pin);

    // Test common PINs using bcrypt (the correct method)
    const testPins = ['579973', '123456', '000000', '111111', '654321'];

    console.log('\n🧪 Testing common PINs with bcrypt...');

    for (const testPin of testPins) {
      try {
        // Use the same logic as AdminPin.validatePin: pin + salt
        const compareString = testPin + pin.pin_salt;
        const isMatch = await bcrypt.compare(compareString, pin.pin_hash);

        console.log(`PIN ${testPin}: ${isMatch ? '✅ MATCH' : '❌ No match'}`);

        if (isMatch) {
          console.log(`\n🎉 FOUND CORRECT PIN: ${testPin}`);
          break;
        }
      } catch (error) {
        console.log(`PIN ${testPin}: ❌ Error - ${error.message}`);
      }
    }

    // Also test if the PIN was stored differently (just the PIN without salt concatenation)
    console.log('\n🧪 Testing PINs without salt concatenation...');

    for (const testPin of testPins) {
      try {
        const isMatch = await bcrypt.compare(testPin, pin.pin_hash);

        console.log(`PIN ${testPin} (no salt): ${isMatch ? '✅ MATCH' : '❌ No match'}`);

        if (isMatch) {
          console.log(`\n🎉 FOUND CORRECT PIN (no salt): ${testPin}`);
          break;
        }
      } catch (error) {
        console.log(`PIN ${testPin} (no salt): ❌ Error - ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Error checking PIN:', error);
    throw error;
  }
}

async function main() {
  try {
    await checkPin();
  } catch (error) {
    console.error('Script failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  checkPin,
};

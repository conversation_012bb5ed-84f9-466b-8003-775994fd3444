#!/usr/bin/env node

/**
 * User Sessions Migration Script
 * Creates the user_sessions table for server-side session management
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Import database configuration
const { sequelize } = require('./shared/config/database');

async function runUserSessionsMigration() {
  try {
    console.log('🚀 Starting User Sessions Migration...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established');
    
    // Read migration SQL file
    const migrationPath = path.join(__dirname, 'migrations', '009_create_user_sessions.sql');
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`Migration file not found: ${migrationPath}`);
    }
    
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log('📄 Migration SQL loaded');
    
    // Split SQL into individual statements (handle multiple statements)
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📋 Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.trim()) {
        try {
          console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`);
          await sequelize.query(statement);
          console.log(`✅ Statement ${i + 1} executed successfully`);
        } catch (error) {
          // Some statements might fail if they already exist (like CREATE TABLE IF NOT EXISTS)
          if (error.message.includes('already exists') || 
              error.message.includes('Duplicate') ||
              error.message.includes('Table') && error.message.includes('already exists')) {
            console.log(`⚠️  Statement ${i + 1} skipped (already exists): ${error.message.split('\n')[0]}`);
          } else {
            console.error(`❌ Statement ${i + 1} failed:`, error.message);
            throw error;
          }
        }
      }
    }
    
    // Verify table creation
    const [results] = await sequelize.query(`
      SELECT COUNT(*) as table_exists 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() 
      AND table_name = 'user_sessions'
    `);
    
    if (results[0].table_exists > 0) {
      console.log('✅ user_sessions table verified');
      
      // Check table structure
      const [columns] = await sequelize.query(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
        FROM information_schema.columns 
        WHERE table_schema = DATABASE() 
        AND table_name = 'user_sessions'
        ORDER BY ORDINAL_POSITION
      `);
      
      console.log(`📊 Table structure verified (${columns.length} columns):`);
      columns.forEach(col => {
        console.log(`   - ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${col.IS_NULLABLE === 'NO' ? 'NOT NULL' : 'NULL'}`);
      });
      
      // Check indexes
      const [indexes] = await sequelize.query(`
        SELECT INDEX_NAME, COLUMN_NAME, NON_UNIQUE
        FROM information_schema.statistics 
        WHERE table_schema = DATABASE() 
        AND table_name = 'user_sessions'
        ORDER BY INDEX_NAME, SEQ_IN_INDEX
      `);
      
      console.log(`🔍 Indexes verified (${indexes.length} index entries):`);
      const indexGroups = {};
      indexes.forEach(idx => {
        if (!indexGroups[idx.INDEX_NAME]) {
          indexGroups[idx.INDEX_NAME] = [];
        }
        indexGroups[idx.INDEX_NAME].push(idx.COLUMN_NAME);
      });
      
      Object.entries(indexGroups).forEach(([indexName, columns]) => {
        console.log(`   - ${indexName}: [${columns.join(', ')}]`);
      });
      
    } else {
      throw new Error('user_sessions table was not created successfully');
    }
    
    console.log('🎉 User Sessions Migration completed successfully!');
    console.log('');
    console.log('📋 Migration Summary:');
    console.log('   ✅ user_sessions table created');
    console.log('   ✅ Foreign key constraints added');
    console.log('   ✅ Performance indexes created');
    console.log('   ✅ active_user_sessions view created');
    console.log('   ✅ Migration logged to database');
    console.log('');
    console.log('🔒 The session lock system is now ready for server-side management!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    // Close database connection
    try {
      await sequelize.close();
      console.log('🔌 Database connection closed');
    } catch (closeError) {
      console.error('⚠️  Error closing database connection:', closeError.message);
    }
  }
}

// Run migration if called directly
if (require.main === module) {
  runUserSessionsMigration();
}

module.exports = { runUserSessionsMigration };
